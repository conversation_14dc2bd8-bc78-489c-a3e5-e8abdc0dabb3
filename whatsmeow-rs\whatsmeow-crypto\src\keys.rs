//! Key management utilities

use crate::error::CryptoError;

/// Generic key pair structure
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct KeyPair {
    pub private_key: Vec<u8>,
    pub public_key: Vec<u8>,
}

impl KeyPair {
    pub fn new(private_key: Vec<u8>, public_key: Vec<u8>) -> Self {
        Self {
            private_key,
            public_key,
        }
    }

    /// Generate a new key pair
    pub fn generate() -> Result<Self, CryptoError> {
        // TODO: Implement key generation
        Err(CryptoError::KeyGenerationFailed(
            "Not implemented".to_string(),
        ))
    }
}

/// Identity key wrapper
#[derive(Debug, Clone)]
pub struct IdentityKey(pub Vec<u8>);

impl IdentityKey {
    pub fn new(key: Vec<u8>) -> Self {
        Self(key)
    }

    pub fn as_bytes(&self) -> &[u8] {
        &self.0
    }
}

/// PreKey structure
#[derive(Debug, <PERSON>lone)]
pub struct PreKey {
    pub id: u32,
    pub key_pair: Key<PERSON><PERSON>,
}

impl PreKey {
    pub fn new(id: u32, key_pair: KeyPair) -> Self {
        Self { id, key_pair }
    }
}

/// Signed PreKey structure
#[derive(Debug, Clone)]
pub struct SignedPreKey {
    pub id: u32,
    pub key_pair: KeyPair,
    pub signature: Vec<u8>,
    pub timestamp: u64,
}

impl SignedPreKey {
    pub fn new(id: u32, key_pair: KeyPair, signature: Vec<u8>, timestamp: u64) -> Self {
        Self {
            id,
            key_pair,
            signature,
            timestamp,
        }
    }
}
