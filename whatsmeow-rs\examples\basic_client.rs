use std::sync::Arc;
use whatsmeow_rs::{Client, ClientConfig};
use whatsmeow_store::MemoryStore;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("WhatsApp Rust Client Example");

    // Create an in-memory store for this example
    let store = Arc::new(MemoryStore::new());

    // Create client with default configuration
    let client = Client::new(store);

    // You can also create a client with custom configuration
    let config = ClientConfig {
        enable_auto_reconnect: true,
        auto_reconnect_errors: 5,
        synchronous_ack: false,
        enable_decrypted_event_buffer: true,
    };

    let _client_with_config = Client::with_config(Arc::new(MemoryStore::new()), config);

    println!("Client created successfully!");
    println!("Note: Connection functionality is not yet implemented.");

    // Add an event handler (this would handle events when implemented)
    client
        .add_event_handler(Box::new(|event| {
            println!("Received event: {:?}", event);
            true // Continue processing events
        }))
        .await;

    println!("Event handler added!");

    // In a real implementation, you would:
    // client.connect().await?;
    // ... handle events and send messages

    Ok(())
}
