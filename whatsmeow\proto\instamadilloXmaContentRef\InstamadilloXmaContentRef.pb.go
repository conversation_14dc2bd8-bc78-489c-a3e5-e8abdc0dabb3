// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: instamadilloXmaContentRef/InstamadilloXmaContentRef.proto

package instamadilloXmaContentRef

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type XmaActionType int32

const (
	XmaActionType_XMA_ACTION_TYPE_UNSPECIFIED XmaActionType = 0
	XmaActionType_XMA_ACTION_TYPE_SHARE       XmaActionType = 1
	XmaActionType_XMA_ACTION_TYPE_REPLY       XmaActionType = 2
	XmaActionType_XMA_ACTION_TYPE_REACT       XmaActionType = 3
	XmaActionType_XMA_ACTION_TYPE_MENTION     XmaActionType = 4
)

// Enum value maps for XmaActionType.
var (
	XmaActionType_name = map[int32]string{
		0: "XMA_ACTION_TYPE_UNSPECIFIED",
		1: "XMA_ACTION_TYPE_SHARE",
		2: "XMA_ACTION_TYPE_REPLY",
		3: "XMA_ACTION_TYPE_REACT",
		4: "XMA_ACTION_TYPE_MENTION",
	}
	XmaActionType_value = map[string]int32{
		"XMA_ACTION_TYPE_UNSPECIFIED": 0,
		"XMA_ACTION_TYPE_SHARE":       1,
		"XMA_ACTION_TYPE_REPLY":       2,
		"XMA_ACTION_TYPE_REACT":       3,
		"XMA_ACTION_TYPE_MENTION":     4,
	}
)

func (x XmaActionType) Enum() *XmaActionType {
	p := new(XmaActionType)
	*p = x
	return p
}

func (x XmaActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (XmaActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_enumTypes[0].Descriptor()
}

func (XmaActionType) Type() protoreflect.EnumType {
	return &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_enumTypes[0]
}

func (x XmaActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *XmaActionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = XmaActionType(num)
	return nil
}

// Deprecated: Use XmaActionType.Descriptor instead.
func (XmaActionType) EnumDescriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{0}
}

type ReceiverFetchContentType int32

const (
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_UNSPECIFIED     ReceiverFetchContentType = 0
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_NOTE            ReceiverFetchContentType = 1
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_STORY           ReceiverFetchContentType = 2
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_PROFILE         ReceiverFetchContentType = 3
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_CLIP            ReceiverFetchContentType = 4
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_FEED            ReceiverFetchContentType = 5
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_LIVE            ReceiverFetchContentType = 6
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_COMMENT         ReceiverFetchContentType = 7
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_LOCATION_SHARE  ReceiverFetchContentType = 8
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_REELS_AUDIO     ReceiverFetchContentType = 9
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_MEDIA_NOTE      ReceiverFetchContentType = 10
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_STORY_HIGHLIGHT ReceiverFetchContentType = 11
	ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_SOCIAL_CONTEXT  ReceiverFetchContentType = 12
)

// Enum value maps for ReceiverFetchContentType.
var (
	ReceiverFetchContentType_name = map[int32]string{
		0:  "RECEIVER_FETCH_CONTENT_TYPE_UNSPECIFIED",
		1:  "RECEIVER_FETCH_CONTENT_TYPE_NOTE",
		2:  "RECEIVER_FETCH_CONTENT_TYPE_STORY",
		3:  "RECEIVER_FETCH_CONTENT_TYPE_PROFILE",
		4:  "RECEIVER_FETCH_CONTENT_TYPE_CLIP",
		5:  "RECEIVER_FETCH_CONTENT_TYPE_FEED",
		6:  "RECEIVER_FETCH_CONTENT_TYPE_LIVE",
		7:  "RECEIVER_FETCH_CONTENT_TYPE_COMMENT",
		8:  "RECEIVER_FETCH_CONTENT_TYPE_LOCATION_SHARE",
		9:  "RECEIVER_FETCH_CONTENT_TYPE_REELS_AUDIO",
		10: "RECEIVER_FETCH_CONTENT_TYPE_MEDIA_NOTE",
		11: "RECEIVER_FETCH_CONTENT_TYPE_STORY_HIGHLIGHT",
		12: "RECEIVER_FETCH_CONTENT_TYPE_SOCIAL_CONTEXT",
	}
	ReceiverFetchContentType_value = map[string]int32{
		"RECEIVER_FETCH_CONTENT_TYPE_UNSPECIFIED":     0,
		"RECEIVER_FETCH_CONTENT_TYPE_NOTE":            1,
		"RECEIVER_FETCH_CONTENT_TYPE_STORY":           2,
		"RECEIVER_FETCH_CONTENT_TYPE_PROFILE":         3,
		"RECEIVER_FETCH_CONTENT_TYPE_CLIP":            4,
		"RECEIVER_FETCH_CONTENT_TYPE_FEED":            5,
		"RECEIVER_FETCH_CONTENT_TYPE_LIVE":            6,
		"RECEIVER_FETCH_CONTENT_TYPE_COMMENT":         7,
		"RECEIVER_FETCH_CONTENT_TYPE_LOCATION_SHARE":  8,
		"RECEIVER_FETCH_CONTENT_TYPE_REELS_AUDIO":     9,
		"RECEIVER_FETCH_CONTENT_TYPE_MEDIA_NOTE":      10,
		"RECEIVER_FETCH_CONTENT_TYPE_STORY_HIGHLIGHT": 11,
		"RECEIVER_FETCH_CONTENT_TYPE_SOCIAL_CONTEXT":  12,
	}
)

func (x ReceiverFetchContentType) Enum() *ReceiverFetchContentType {
	p := new(ReceiverFetchContentType)
	*p = x
	return p
}

func (x ReceiverFetchContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReceiverFetchContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_enumTypes[1].Descriptor()
}

func (ReceiverFetchContentType) Type() protoreflect.EnumType {
	return &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_enumTypes[1]
}

func (x ReceiverFetchContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ReceiverFetchContentType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ReceiverFetchContentType(num)
	return nil
}

// Deprecated: Use ReceiverFetchContentType.Descriptor instead.
func (ReceiverFetchContentType) EnumDescriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{1}
}

type MediaNoteFetchParamsMessageType int32

const (
	MediaNoteFetchParamsMessageType_MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_UNSPECIFIED MediaNoteFetchParamsMessageType = 0
	MediaNoteFetchParamsMessageType_MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_MENTION     MediaNoteFetchParamsMessageType = 1
	MediaNoteFetchParamsMessageType_MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_REPLY       MediaNoteFetchParamsMessageType = 2
)

// Enum value maps for MediaNoteFetchParamsMessageType.
var (
	MediaNoteFetchParamsMessageType_name = map[int32]string{
		0: "MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_UNSPECIFIED",
		1: "MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_MENTION",
		2: "MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_REPLY",
	}
	MediaNoteFetchParamsMessageType_value = map[string]int32{
		"MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_UNSPECIFIED": 0,
		"MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_MENTION":     1,
		"MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_REPLY":       2,
	}
)

func (x MediaNoteFetchParamsMessageType) Enum() *MediaNoteFetchParamsMessageType {
	p := new(MediaNoteFetchParamsMessageType)
	*p = x
	return p
}

func (x MediaNoteFetchParamsMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MediaNoteFetchParamsMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_enumTypes[2].Descriptor()
}

func (MediaNoteFetchParamsMessageType) Type() protoreflect.EnumType {
	return &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_enumTypes[2]
}

func (x MediaNoteFetchParamsMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MediaNoteFetchParamsMessageType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MediaNoteFetchParamsMessageType(num)
	return nil
}

// Deprecated: Use MediaNoteFetchParamsMessageType.Descriptor instead.
func (MediaNoteFetchParamsMessageType) EnumDescriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{2}
}

type XmaContentRef struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	ActionType    *XmaActionType               `protobuf:"varint,1,opt,name=actionType,enum=InstamadilloXmaContentRef.XmaActionType" json:"actionType,omitempty"`
	ContentType   *ReceiverFetchContentType    `protobuf:"varint,2,opt,name=contentType,enum=InstamadilloXmaContentRef.ReceiverFetchContentType" json:"contentType,omitempty"`
	TargetURL     *string                      `protobuf:"bytes,3,opt,name=targetURL" json:"targetURL,omitempty"`
	UserName      *string                      `protobuf:"bytes,4,opt,name=userName" json:"userName,omitempty"`
	OwnerFbid     *string                      `protobuf:"bytes,5,opt,name=ownerFbid" json:"ownerFbid,omitempty"`
	FetchParams   *ReceiverFetchXmaFetchParams `protobuf:"bytes,6,opt,name=fetchParams" json:"fetchParams,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *XmaContentRef) Reset() {
	*x = XmaContentRef{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *XmaContentRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*XmaContentRef) ProtoMessage() {}

func (x *XmaContentRef) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use XmaContentRef.ProtoReflect.Descriptor instead.
func (*XmaContentRef) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{0}
}

func (x *XmaContentRef) GetActionType() XmaActionType {
	if x != nil && x.ActionType != nil {
		return *x.ActionType
	}
	return XmaActionType_XMA_ACTION_TYPE_UNSPECIFIED
}

func (x *XmaContentRef) GetContentType() ReceiverFetchContentType {
	if x != nil && x.ContentType != nil {
		return *x.ContentType
	}
	return ReceiverFetchContentType_RECEIVER_FETCH_CONTENT_TYPE_UNSPECIFIED
}

func (x *XmaContentRef) GetTargetURL() string {
	if x != nil && x.TargetURL != nil {
		return *x.TargetURL
	}
	return ""
}

func (x *XmaContentRef) GetUserName() string {
	if x != nil && x.UserName != nil {
		return *x.UserName
	}
	return ""
}

func (x *XmaContentRef) GetOwnerFbid() string {
	if x != nil && x.OwnerFbid != nil {
		return *x.OwnerFbid
	}
	return ""
}

func (x *XmaContentRef) GetFetchParams() *ReceiverFetchXmaFetchParams {
	if x != nil {
		return x.FetchParams
	}
	return nil
}

type ReceiverFetchXmaFetchParams struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to ReceiverFetchXmaFetchParams:
	//
	//	*ReceiverFetchXmaFetchParams_NoteFetchParams
	//	*ReceiverFetchXmaFetchParams_StoryFetchParams
	//	*ReceiverFetchXmaFetchParams_ProfileFetchParams
	//	*ReceiverFetchXmaFetchParams_ClipFetchParams
	//	*ReceiverFetchXmaFetchParams_FeedFetchParams
	//	*ReceiverFetchXmaFetchParams_LiveFetchParams
	//	*ReceiverFetchXmaFetchParams_CommentFetchParams
	//	*ReceiverFetchXmaFetchParams_LocationShareFetchParams
	//	*ReceiverFetchXmaFetchParams_ReelsAudioFetchParams
	//	*ReceiverFetchXmaFetchParams_MediaNoteFetchParams
	//	*ReceiverFetchXmaFetchParams_SocialContextFetchParams
	ReceiverFetchXmaFetchParams isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams `protobuf_oneof:"receiverFetchXmaFetchParams"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *ReceiverFetchXmaFetchParams) Reset() {
	*x = ReceiverFetchXmaFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{1}
}

func (x *ReceiverFetchXmaFetchParams) GetReceiverFetchXmaFetchParams() isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams {
	if x != nil {
		return x.ReceiverFetchXmaFetchParams
	}
	return nil
}

func (x *ReceiverFetchXmaFetchParams) GetNoteFetchParams() *ReceiverFetchXmaNoteFetchParams {
	if x != nil {
		if x, ok := x.ReceiverFetchXmaFetchParams.(*ReceiverFetchXmaFetchParams_NoteFetchParams); ok {
			return x.NoteFetchParams
		}
	}
	return nil
}

func (x *ReceiverFetchXmaFetchParams) GetStoryFetchParams() *ReceiverFetchXmaStoryFetchParams {
	if x != nil {
		if x, ok := x.ReceiverFetchXmaFetchParams.(*ReceiverFetchXmaFetchParams_StoryFetchParams); ok {
			return x.StoryFetchParams
		}
	}
	return nil
}

func (x *ReceiverFetchXmaFetchParams) GetProfileFetchParams() *ReceiverFetchXmaProfileFetchParams {
	if x != nil {
		if x, ok := x.ReceiverFetchXmaFetchParams.(*ReceiverFetchXmaFetchParams_ProfileFetchParams); ok {
			return x.ProfileFetchParams
		}
	}
	return nil
}

func (x *ReceiverFetchXmaFetchParams) GetClipFetchParams() *ReceiverFetchXmaClipFetchParams {
	if x != nil {
		if x, ok := x.ReceiverFetchXmaFetchParams.(*ReceiverFetchXmaFetchParams_ClipFetchParams); ok {
			return x.ClipFetchParams
		}
	}
	return nil
}

func (x *ReceiverFetchXmaFetchParams) GetFeedFetchParams() *ReceiverFetchXmaFeedFetchParams {
	if x != nil {
		if x, ok := x.ReceiverFetchXmaFetchParams.(*ReceiverFetchXmaFetchParams_FeedFetchParams); ok {
			return x.FeedFetchParams
		}
	}
	return nil
}

func (x *ReceiverFetchXmaFetchParams) GetLiveFetchParams() *ReceiverFetchXmaLiveFetchParams {
	if x != nil {
		if x, ok := x.ReceiverFetchXmaFetchParams.(*ReceiverFetchXmaFetchParams_LiveFetchParams); ok {
			return x.LiveFetchParams
		}
	}
	return nil
}

func (x *ReceiverFetchXmaFetchParams) GetCommentFetchParams() *ReceiverFetchXmaCommentFetchParams {
	if x != nil {
		if x, ok := x.ReceiverFetchXmaFetchParams.(*ReceiverFetchXmaFetchParams_CommentFetchParams); ok {
			return x.CommentFetchParams
		}
	}
	return nil
}

func (x *ReceiverFetchXmaFetchParams) GetLocationShareFetchParams() *ReceiverFetchXmaLocationShareFetchParams {
	if x != nil {
		if x, ok := x.ReceiverFetchXmaFetchParams.(*ReceiverFetchXmaFetchParams_LocationShareFetchParams); ok {
			return x.LocationShareFetchParams
		}
	}
	return nil
}

func (x *ReceiverFetchXmaFetchParams) GetReelsAudioFetchParams() *ReceiverFetchXmaReelsAudioFetchParams {
	if x != nil {
		if x, ok := x.ReceiverFetchXmaFetchParams.(*ReceiverFetchXmaFetchParams_ReelsAudioFetchParams); ok {
			return x.ReelsAudioFetchParams
		}
	}
	return nil
}

func (x *ReceiverFetchXmaFetchParams) GetMediaNoteFetchParams() *ReceiverFetchXmaMediaNoteFetchParams {
	if x != nil {
		if x, ok := x.ReceiverFetchXmaFetchParams.(*ReceiverFetchXmaFetchParams_MediaNoteFetchParams); ok {
			return x.MediaNoteFetchParams
		}
	}
	return nil
}

func (x *ReceiverFetchXmaFetchParams) GetSocialContextFetchParams() *ReceiverFetchXmaSocialContextFetchParams {
	if x != nil {
		if x, ok := x.ReceiverFetchXmaFetchParams.(*ReceiverFetchXmaFetchParams_SocialContextFetchParams); ok {
			return x.SocialContextFetchParams
		}
	}
	return nil
}

type isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams interface {
	isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams()
}

type ReceiverFetchXmaFetchParams_NoteFetchParams struct {
	NoteFetchParams *ReceiverFetchXmaNoteFetchParams `protobuf:"bytes,1,opt,name=noteFetchParams,oneof"`
}

type ReceiverFetchXmaFetchParams_StoryFetchParams struct {
	StoryFetchParams *ReceiverFetchXmaStoryFetchParams `protobuf:"bytes,2,opt,name=storyFetchParams,oneof"`
}

type ReceiverFetchXmaFetchParams_ProfileFetchParams struct {
	ProfileFetchParams *ReceiverFetchXmaProfileFetchParams `protobuf:"bytes,3,opt,name=profileFetchParams,oneof"`
}

type ReceiverFetchXmaFetchParams_ClipFetchParams struct {
	ClipFetchParams *ReceiverFetchXmaClipFetchParams `protobuf:"bytes,4,opt,name=clipFetchParams,oneof"`
}

type ReceiverFetchXmaFetchParams_FeedFetchParams struct {
	FeedFetchParams *ReceiverFetchXmaFeedFetchParams `protobuf:"bytes,5,opt,name=feedFetchParams,oneof"`
}

type ReceiverFetchXmaFetchParams_LiveFetchParams struct {
	LiveFetchParams *ReceiverFetchXmaLiveFetchParams `protobuf:"bytes,6,opt,name=liveFetchParams,oneof"`
}

type ReceiverFetchXmaFetchParams_CommentFetchParams struct {
	CommentFetchParams *ReceiverFetchXmaCommentFetchParams `protobuf:"bytes,7,opt,name=commentFetchParams,oneof"`
}

type ReceiverFetchXmaFetchParams_LocationShareFetchParams struct {
	LocationShareFetchParams *ReceiverFetchXmaLocationShareFetchParams `protobuf:"bytes,8,opt,name=locationShareFetchParams,oneof"`
}

type ReceiverFetchXmaFetchParams_ReelsAudioFetchParams struct {
	ReelsAudioFetchParams *ReceiverFetchXmaReelsAudioFetchParams `protobuf:"bytes,9,opt,name=reelsAudioFetchParams,oneof"`
}

type ReceiverFetchXmaFetchParams_MediaNoteFetchParams struct {
	MediaNoteFetchParams *ReceiverFetchXmaMediaNoteFetchParams `protobuf:"bytes,10,opt,name=mediaNoteFetchParams,oneof"`
}

type ReceiverFetchXmaFetchParams_SocialContextFetchParams struct {
	SocialContextFetchParams *ReceiverFetchXmaSocialContextFetchParams `protobuf:"bytes,11,opt,name=socialContextFetchParams,oneof"`
}

func (*ReceiverFetchXmaFetchParams_NoteFetchParams) isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams() {
}

func (*ReceiverFetchXmaFetchParams_StoryFetchParams) isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams() {
}

func (*ReceiverFetchXmaFetchParams_ProfileFetchParams) isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams() {
}

func (*ReceiverFetchXmaFetchParams_ClipFetchParams) isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams() {
}

func (*ReceiverFetchXmaFetchParams_FeedFetchParams) isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams() {
}

func (*ReceiverFetchXmaFetchParams_LiveFetchParams) isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams() {
}

func (*ReceiverFetchXmaFetchParams_CommentFetchParams) isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams() {
}

func (*ReceiverFetchXmaFetchParams_LocationShareFetchParams) isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams() {
}

func (*ReceiverFetchXmaFetchParams_ReelsAudioFetchParams) isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams() {
}

func (*ReceiverFetchXmaFetchParams_MediaNoteFetchParams) isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams() {
}

func (*ReceiverFetchXmaFetchParams_SocialContextFetchParams) isReceiverFetchXmaFetchParams_ReceiverFetchXmaFetchParams() {
}

type ReceiverFetchXmaNoteFetchParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NoteIgid      *string                `protobuf:"bytes,1,opt,name=noteIgid" json:"noteIgid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceiverFetchXmaNoteFetchParams) Reset() {
	*x = ReceiverFetchXmaNoteFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaNoteFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaNoteFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaNoteFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaNoteFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaNoteFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{2}
}

func (x *ReceiverFetchXmaNoteFetchParams) GetNoteIgid() string {
	if x != nil && x.NoteIgid != nil {
		return *x.NoteIgid
	}
	return ""
}

type ReceiverFetchXmaStoryFetchParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryIgid     *string                `protobuf:"bytes,1,opt,name=storyIgid" json:"storyIgid,omitempty"`
	ReelID        *string                `protobuf:"bytes,2,opt,name=reelID" json:"reelID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceiverFetchXmaStoryFetchParams) Reset() {
	*x = ReceiverFetchXmaStoryFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaStoryFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaStoryFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaStoryFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaStoryFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaStoryFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{3}
}

func (x *ReceiverFetchXmaStoryFetchParams) GetStoryIgid() string {
	if x != nil && x.StoryIgid != nil {
		return *x.StoryIgid
	}
	return ""
}

func (x *ReceiverFetchXmaStoryFetchParams) GetReelID() string {
	if x != nil && x.ReelID != nil {
		return *x.ReelID
	}
	return ""
}

type ReceiverFetchXmaProfileFetchParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProfileIgid   *string                `protobuf:"bytes,1,opt,name=profileIgid" json:"profileIgid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceiverFetchXmaProfileFetchParams) Reset() {
	*x = ReceiverFetchXmaProfileFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaProfileFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaProfileFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaProfileFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaProfileFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaProfileFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{4}
}

func (x *ReceiverFetchXmaProfileFetchParams) GetProfileIgid() string {
	if x != nil && x.ProfileIgid != nil {
		return *x.ProfileIgid
	}
	return ""
}

type ReceiverFetchXmaClipFetchParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MediaIgid     *string                `protobuf:"bytes,1,opt,name=mediaIgid" json:"mediaIgid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceiverFetchXmaClipFetchParams) Reset() {
	*x = ReceiverFetchXmaClipFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaClipFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaClipFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaClipFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaClipFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaClipFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{5}
}

func (x *ReceiverFetchXmaClipFetchParams) GetMediaIgid() string {
	if x != nil && x.MediaIgid != nil {
		return *x.MediaIgid
	}
	return ""
}

type ReceiverFetchXmaFeedFetchParams struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	MediaIgid                   *string                `protobuf:"bytes,1,opt,name=mediaIgid" json:"mediaIgid,omitempty"`
	CarouselShareChildMediaIgid *string                `protobuf:"bytes,2,opt,name=carouselShareChildMediaIgid" json:"carouselShareChildMediaIgid,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *ReceiverFetchXmaFeedFetchParams) Reset() {
	*x = ReceiverFetchXmaFeedFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaFeedFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaFeedFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaFeedFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaFeedFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaFeedFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{6}
}

func (x *ReceiverFetchXmaFeedFetchParams) GetMediaIgid() string {
	if x != nil && x.MediaIgid != nil {
		return *x.MediaIgid
	}
	return ""
}

func (x *ReceiverFetchXmaFeedFetchParams) GetCarouselShareChildMediaIgid() string {
	if x != nil && x.CarouselShareChildMediaIgid != nil {
		return *x.CarouselShareChildMediaIgid
	}
	return ""
}

type ReceiverFetchXmaLiveFetchParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LiveIgid      *string                `protobuf:"bytes,1,opt,name=liveIgid" json:"liveIgid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceiverFetchXmaLiveFetchParams) Reset() {
	*x = ReceiverFetchXmaLiveFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaLiveFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaLiveFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaLiveFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaLiveFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaLiveFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{7}
}

func (x *ReceiverFetchXmaLiveFetchParams) GetLiveIgid() string {
	if x != nil && x.LiveIgid != nil {
		return *x.LiveIgid
	}
	return ""
}

type ReceiverFetchXmaCommentFetchParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CommentFbid   *string                `protobuf:"bytes,1,opt,name=commentFbid" json:"commentFbid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceiverFetchXmaCommentFetchParams) Reset() {
	*x = ReceiverFetchXmaCommentFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaCommentFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaCommentFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaCommentFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaCommentFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaCommentFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{8}
}

func (x *ReceiverFetchXmaCommentFetchParams) GetCommentFbid() string {
	if x != nil && x.CommentFbid != nil {
		return *x.CommentFbid
	}
	return ""
}

type ReceiverFetchXmaLocationShareFetchParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LocationIgid  *string                `protobuf:"bytes,1,opt,name=locationIgid" json:"locationIgid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceiverFetchXmaLocationShareFetchParams) Reset() {
	*x = ReceiverFetchXmaLocationShareFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaLocationShareFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaLocationShareFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaLocationShareFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaLocationShareFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaLocationShareFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{9}
}

func (x *ReceiverFetchXmaLocationShareFetchParams) GetLocationIgid() string {
	if x != nil && x.LocationIgid != nil {
		return *x.LocationIgid
	}
	return ""
}

type ReceiverFetchXmaReelsAudioFetchParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AudioIgid     *string                `protobuf:"bytes,1,opt,name=audioIgid" json:"audioIgid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceiverFetchXmaReelsAudioFetchParams) Reset() {
	*x = ReceiverFetchXmaReelsAudioFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaReelsAudioFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaReelsAudioFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaReelsAudioFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaReelsAudioFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaReelsAudioFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{10}
}

func (x *ReceiverFetchXmaReelsAudioFetchParams) GetAudioIgid() string {
	if x != nil && x.AudioIgid != nil {
		return *x.AudioIgid
	}
	return ""
}

type ReceiverFetchXmaMediaNoteFetchParams struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	MediaNoteIgid *string                          `protobuf:"bytes,1,opt,name=mediaNoteIgid" json:"mediaNoteIgid,omitempty"`
	MessageType   *MediaNoteFetchParamsMessageType `protobuf:"varint,2,opt,name=messageType,enum=InstamadilloXmaContentRef.MediaNoteFetchParamsMessageType" json:"messageType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceiverFetchXmaMediaNoteFetchParams) Reset() {
	*x = ReceiverFetchXmaMediaNoteFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaMediaNoteFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaMediaNoteFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaMediaNoteFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaMediaNoteFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaMediaNoteFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{11}
}

func (x *ReceiverFetchXmaMediaNoteFetchParams) GetMediaNoteIgid() string {
	if x != nil && x.MediaNoteIgid != nil {
		return *x.MediaNoteIgid
	}
	return ""
}

func (x *ReceiverFetchXmaMediaNoteFetchParams) GetMessageType() MediaNoteFetchParamsMessageType {
	if x != nil && x.MessageType != nil {
		return *x.MessageType
	}
	return MediaNoteFetchParamsMessageType_MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_UNSPECIFIED
}

type ReceiverFetchXmaSocialContextFetchParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MediaIgid     *string                `protobuf:"bytes,1,opt,name=mediaIgid" json:"mediaIgid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReceiverFetchXmaSocialContextFetchParams) Reset() {
	*x = ReceiverFetchXmaSocialContextFetchParams{}
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReceiverFetchXmaSocialContextFetchParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiverFetchXmaSocialContextFetchParams) ProtoMessage() {}

func (x *ReceiverFetchXmaSocialContextFetchParams) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiverFetchXmaSocialContextFetchParams.ProtoReflect.Descriptor instead.
func (*ReceiverFetchXmaSocialContextFetchParams) Descriptor() ([]byte, []int) {
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP(), []int{12}
}

func (x *ReceiverFetchXmaSocialContextFetchParams) GetMediaIgid() string {
	if x != nil && x.MediaIgid != nil {
		return *x.MediaIgid
	}
	return ""
}

var File_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto protoreflect.FileDescriptor

const file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDesc = "" +
	"\n" +
	"9instamadilloXmaContentRef/InstamadilloXmaContentRef.proto\x12\x19InstamadilloXmaContentRef\"\xe2\x02\n" +
	"\rXmaContentRef\x12H\n" +
	"\n" +
	"actionType\x18\x01 \x01(\x0e2(.InstamadilloXmaContentRef.XmaActionTypeR\n" +
	"actionType\x12U\n" +
	"\vcontentType\x18\x02 \x01(\x0e23.InstamadilloXmaContentRef.ReceiverFetchContentTypeR\vcontentType\x12\x1c\n" +
	"\ttargetURL\x18\x03 \x01(\tR\ttargetURL\x12\x1a\n" +
	"\buserName\x18\x04 \x01(\tR\buserName\x12\x1c\n" +
	"\townerFbid\x18\x05 \x01(\tR\townerFbid\x12X\n" +
	"\vfetchParams\x18\x06 \x01(\v26.InstamadilloXmaContentRef.ReceiverFetchXmaFetchParamsR\vfetchParams\"\xa2\n" +
	"\n" +
	"\x1bReceiverFetchXmaFetchParams\x12f\n" +
	"\x0fnoteFetchParams\x18\x01 \x01(\v2:.InstamadilloXmaContentRef.ReceiverFetchXmaNoteFetchParamsH\x00R\x0fnoteFetchParams\x12i\n" +
	"\x10storyFetchParams\x18\x02 \x01(\v2;.InstamadilloXmaContentRef.ReceiverFetchXmaStoryFetchParamsH\x00R\x10storyFetchParams\x12o\n" +
	"\x12profileFetchParams\x18\x03 \x01(\v2=.InstamadilloXmaContentRef.ReceiverFetchXmaProfileFetchParamsH\x00R\x12profileFetchParams\x12f\n" +
	"\x0fclipFetchParams\x18\x04 \x01(\v2:.InstamadilloXmaContentRef.ReceiverFetchXmaClipFetchParamsH\x00R\x0fclipFetchParams\x12f\n" +
	"\x0ffeedFetchParams\x18\x05 \x01(\v2:.InstamadilloXmaContentRef.ReceiverFetchXmaFeedFetchParamsH\x00R\x0ffeedFetchParams\x12f\n" +
	"\x0fliveFetchParams\x18\x06 \x01(\v2:.InstamadilloXmaContentRef.ReceiverFetchXmaLiveFetchParamsH\x00R\x0fliveFetchParams\x12o\n" +
	"\x12commentFetchParams\x18\a \x01(\v2=.InstamadilloXmaContentRef.ReceiverFetchXmaCommentFetchParamsH\x00R\x12commentFetchParams\x12\x81\x01\n" +
	"\x18locationShareFetchParams\x18\b \x01(\v2C.InstamadilloXmaContentRef.ReceiverFetchXmaLocationShareFetchParamsH\x00R\x18locationShareFetchParams\x12x\n" +
	"\x15reelsAudioFetchParams\x18\t \x01(\<EMAIL>\x00R\x15reelsAudioFetchParams\x12u\n" +
	"\x14mediaNoteFetchParams\x18\n" +
	" \x01(\v2?.InstamadilloXmaContentRef.ReceiverFetchXmaMediaNoteFetchParamsH\x00R\x14mediaNoteFetchParams\x12\x81\x01\n" +
	"\x18socialContextFetchParams\x18\v \x01(\v2C.InstamadilloXmaContentRef.ReceiverFetchXmaSocialContextFetchParamsH\x00R\x18socialContextFetchParamsB\x1d\n" +
	"\x1breceiverFetchXmaFetchParams\"=\n" +
	"\x1fReceiverFetchXmaNoteFetchParams\x12\x1a\n" +
	"\bnoteIgid\x18\x01 \x01(\tR\bnoteIgid\"X\n" +
	" ReceiverFetchXmaStoryFetchParams\x12\x1c\n" +
	"\tstoryIgid\x18\x01 \x01(\tR\tstoryIgid\x12\x16\n" +
	"\x06reelID\x18\x02 \x01(\tR\x06reelID\"F\n" +
	"\"ReceiverFetchXmaProfileFetchParams\x12 \n" +
	"\vprofileIgid\x18\x01 \x01(\tR\vprofileIgid\"?\n" +
	"\x1fReceiverFetchXmaClipFetchParams\x12\x1c\n" +
	"\tmediaIgid\x18\x01 \x01(\tR\tmediaIgid\"\x81\x01\n" +
	"\x1fReceiverFetchXmaFeedFetchParams\x12\x1c\n" +
	"\tmediaIgid\x18\x01 \x01(\tR\tmediaIgid\x12@\n" +
	"\x1bcarouselShareChildMediaIgid\x18\x02 \x01(\tR\x1bcarouselShareChildMediaIgid\"=\n" +
	"\x1fReceiverFetchXmaLiveFetchParams\x12\x1a\n" +
	"\bliveIgid\x18\x01 \x01(\tR\bliveIgid\"F\n" +
	"\"ReceiverFetchXmaCommentFetchParams\x12 \n" +
	"\vcommentFbid\x18\x01 \x01(\tR\vcommentFbid\"N\n" +
	"(ReceiverFetchXmaLocationShareFetchParams\x12\"\n" +
	"\flocationIgid\x18\x01 \x01(\tR\flocationIgid\"E\n" +
	"%ReceiverFetchXmaReelsAudioFetchParams\x12\x1c\n" +
	"\taudioIgid\x18\x01 \x01(\tR\taudioIgid\"\xaa\x01\n" +
	"$ReceiverFetchXmaMediaNoteFetchParams\x12$\n" +
	"\rmediaNoteIgid\x18\x01 \x01(\tR\rmediaNoteIgid\x12\\\n" +
	"\vmessageType\x18\x02 \x01(\x0e2:.InstamadilloXmaContentRef.MediaNoteFetchParamsMessageTypeR\vmessageType\"H\n" +
	"(ReceiverFetchXmaSocialContextFetchParams\x12\x1c\n" +
	"\tmediaIgid\x18\x01 \x01(\tR\tmediaIgid*\x9e\x01\n" +
	"\rXmaActionType\x12\x1f\n" +
	"\x1bXMA_ACTION_TYPE_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15XMA_ACTION_TYPE_SHARE\x10\x01\x12\x19\n" +
	"\x15XMA_ACTION_TYPE_REPLY\x10\x02\x12\x19\n" +
	"\x15XMA_ACTION_TYPE_REACT\x10\x03\x12\x1b\n" +
	"\x17XMA_ACTION_TYPE_MENTION\x10\x04*\xc2\x04\n" +
	"\x18ReceiverFetchContentType\x12+\n" +
	"'RECEIVER_FETCH_CONTENT_TYPE_UNSPECIFIED\x10\x00\x12$\n" +
	" RECEIVER_FETCH_CONTENT_TYPE_NOTE\x10\x01\x12%\n" +
	"!RECEIVER_FETCH_CONTENT_TYPE_STORY\x10\x02\x12'\n" +
	"#RECEIVER_FETCH_CONTENT_TYPE_PROFILE\x10\x03\x12$\n" +
	" RECEIVER_FETCH_CONTENT_TYPE_CLIP\x10\x04\x12$\n" +
	" RECEIVER_FETCH_CONTENT_TYPE_FEED\x10\x05\x12$\n" +
	" RECEIVER_FETCH_CONTENT_TYPE_LIVE\x10\x06\x12'\n" +
	"#RECEIVER_FETCH_CONTENT_TYPE_COMMENT\x10\a\x12.\n" +
	"*RECEIVER_FETCH_CONTENT_TYPE_LOCATION_SHARE\x10\b\x12+\n" +
	"'RECEIVER_FETCH_CONTENT_TYPE_REELS_AUDIO\x10\t\x12*\n" +
	"&RECEIVER_FETCH_CONTENT_TYPE_MEDIA_NOTE\x10\n" +
	"\x12/\n" +
	"+RECEIVER_FETCH_CONTENT_TYPE_STORY_HIGHLIGHT\x10\v\x12.\n" +
	"*RECEIVER_FETCH_CONTENT_TYPE_SOCIAL_CONTEXT\x10\f*\xb9\x01\n" +
	"\x1fMediaNoteFetchParamsMessageType\x124\n" +
	"0MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_UNSPECIFIED\x10\x00\x120\n" +
	",MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_MENTION\x10\x01\x12.\n" +
	"*MEDIA_NOTE_FETCH_PARAMS_MESSAGE_TYPE_REPLY\x10\x02B5Z3go.mau.fi/whatsmeow/proto/instamadilloXmaContentRef"

var (
	file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescOnce sync.Once
	file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescData []byte
)

func file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescGZIP() []byte {
	file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescOnce.Do(func() {
		file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDesc), len(file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDesc)))
	})
	return file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDescData
}

var file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_goTypes = []any{
	(XmaActionType)(0),                               // 0: InstamadilloXmaContentRef.XmaActionType
	(ReceiverFetchContentType)(0),                    // 1: InstamadilloXmaContentRef.ReceiverFetchContentType
	(MediaNoteFetchParamsMessageType)(0),             // 2: InstamadilloXmaContentRef.MediaNoteFetchParamsMessageType
	(*XmaContentRef)(nil),                            // 3: InstamadilloXmaContentRef.XmaContentRef
	(*ReceiverFetchXmaFetchParams)(nil),              // 4: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams
	(*ReceiverFetchXmaNoteFetchParams)(nil),          // 5: InstamadilloXmaContentRef.ReceiverFetchXmaNoteFetchParams
	(*ReceiverFetchXmaStoryFetchParams)(nil),         // 6: InstamadilloXmaContentRef.ReceiverFetchXmaStoryFetchParams
	(*ReceiverFetchXmaProfileFetchParams)(nil),       // 7: InstamadilloXmaContentRef.ReceiverFetchXmaProfileFetchParams
	(*ReceiverFetchXmaClipFetchParams)(nil),          // 8: InstamadilloXmaContentRef.ReceiverFetchXmaClipFetchParams
	(*ReceiverFetchXmaFeedFetchParams)(nil),          // 9: InstamadilloXmaContentRef.ReceiverFetchXmaFeedFetchParams
	(*ReceiverFetchXmaLiveFetchParams)(nil),          // 10: InstamadilloXmaContentRef.ReceiverFetchXmaLiveFetchParams
	(*ReceiverFetchXmaCommentFetchParams)(nil),       // 11: InstamadilloXmaContentRef.ReceiverFetchXmaCommentFetchParams
	(*ReceiverFetchXmaLocationShareFetchParams)(nil), // 12: InstamadilloXmaContentRef.ReceiverFetchXmaLocationShareFetchParams
	(*ReceiverFetchXmaReelsAudioFetchParams)(nil),    // 13: InstamadilloXmaContentRef.ReceiverFetchXmaReelsAudioFetchParams
	(*ReceiverFetchXmaMediaNoteFetchParams)(nil),     // 14: InstamadilloXmaContentRef.ReceiverFetchXmaMediaNoteFetchParams
	(*ReceiverFetchXmaSocialContextFetchParams)(nil), // 15: InstamadilloXmaContentRef.ReceiverFetchXmaSocialContextFetchParams
}
var file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_depIdxs = []int32{
	0,  // 0: InstamadilloXmaContentRef.XmaContentRef.actionType:type_name -> InstamadilloXmaContentRef.XmaActionType
	1,  // 1: InstamadilloXmaContentRef.XmaContentRef.contentType:type_name -> InstamadilloXmaContentRef.ReceiverFetchContentType
	4,  // 2: InstamadilloXmaContentRef.XmaContentRef.fetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams
	5,  // 3: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams.noteFetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaNoteFetchParams
	6,  // 4: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams.storyFetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaStoryFetchParams
	7,  // 5: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams.profileFetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaProfileFetchParams
	8,  // 6: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams.clipFetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaClipFetchParams
	9,  // 7: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams.feedFetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaFeedFetchParams
	10, // 8: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams.liveFetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaLiveFetchParams
	11, // 9: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams.commentFetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaCommentFetchParams
	12, // 10: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams.locationShareFetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaLocationShareFetchParams
	13, // 11: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams.reelsAudioFetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaReelsAudioFetchParams
	14, // 12: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams.mediaNoteFetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaMediaNoteFetchParams
	15, // 13: InstamadilloXmaContentRef.ReceiverFetchXmaFetchParams.socialContextFetchParams:type_name -> InstamadilloXmaContentRef.ReceiverFetchXmaSocialContextFetchParams
	2,  // 14: InstamadilloXmaContentRef.ReceiverFetchXmaMediaNoteFetchParams.messageType:type_name -> InstamadilloXmaContentRef.MediaNoteFetchParamsMessageType
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_init() }
func file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_init() {
	if File_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto != nil {
		return
	}
	file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes[1].OneofWrappers = []any{
		(*ReceiverFetchXmaFetchParams_NoteFetchParams)(nil),
		(*ReceiverFetchXmaFetchParams_StoryFetchParams)(nil),
		(*ReceiverFetchXmaFetchParams_ProfileFetchParams)(nil),
		(*ReceiverFetchXmaFetchParams_ClipFetchParams)(nil),
		(*ReceiverFetchXmaFetchParams_FeedFetchParams)(nil),
		(*ReceiverFetchXmaFetchParams_LiveFetchParams)(nil),
		(*ReceiverFetchXmaFetchParams_CommentFetchParams)(nil),
		(*ReceiverFetchXmaFetchParams_LocationShareFetchParams)(nil),
		(*ReceiverFetchXmaFetchParams_ReelsAudioFetchParams)(nil),
		(*ReceiverFetchXmaFetchParams_MediaNoteFetchParams)(nil),
		(*ReceiverFetchXmaFetchParams_SocialContextFetchParams)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDesc), len(file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_goTypes,
		DependencyIndexes: file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_depIdxs,
		EnumInfos:         file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_enumTypes,
		MessageInfos:      file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_msgTypes,
	}.Build()
	File_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto = out.File
	file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_goTypes = nil
	file_instamadilloXmaContentRef_InstamadilloXmaContentRef_proto_depIdxs = nil
}
