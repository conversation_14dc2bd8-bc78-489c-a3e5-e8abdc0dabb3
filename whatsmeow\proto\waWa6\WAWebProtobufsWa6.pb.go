// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waWa6/WAWebProtobufsWa6.proto

package waWa6

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ClientPayload_TrafficAnonymization int32

const (
	ClientPayload_OFF      ClientPayload_TrafficAnonymization = 0
	ClientPayload_STANDARD ClientPayload_TrafficAnonymization = 1
)

// Enum value maps for ClientPayload_TrafficAnonymization.
var (
	ClientPayload_TrafficAnonymization_name = map[int32]string{
		0: "OFF",
		1: "STANDARD",
	}
	ClientPayload_TrafficAnonymization_value = map[string]int32{
		"OFF":      0,
		"STANDARD": 1,
	}
)

func (x ClientPayload_TrafficAnonymization) Enum() *ClientPayload_TrafficAnonymization {
	p := new(ClientPayload_TrafficAnonymization)
	*p = x
	return p
}

func (x ClientPayload_TrafficAnonymization) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientPayload_TrafficAnonymization) Descriptor() protoreflect.EnumDescriptor {
	return file_waWa6_WAWebProtobufsWa6_proto_enumTypes[0].Descriptor()
}

func (ClientPayload_TrafficAnonymization) Type() protoreflect.EnumType {
	return &file_waWa6_WAWebProtobufsWa6_proto_enumTypes[0]
}

func (x ClientPayload_TrafficAnonymization) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ClientPayload_TrafficAnonymization) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ClientPayload_TrafficAnonymization(num)
	return nil
}

// Deprecated: Use ClientPayload_TrafficAnonymization.Descriptor instead.
func (ClientPayload_TrafficAnonymization) EnumDescriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 0}
}

type ClientPayload_AccountType int32

const (
	ClientPayload_DEFAULT ClientPayload_AccountType = 0
	ClientPayload_GUEST   ClientPayload_AccountType = 1
)

// Enum value maps for ClientPayload_AccountType.
var (
	ClientPayload_AccountType_name = map[int32]string{
		0: "DEFAULT",
		1: "GUEST",
	}
	ClientPayload_AccountType_value = map[string]int32{
		"DEFAULT": 0,
		"GUEST":   1,
	}
)

func (x ClientPayload_AccountType) Enum() *ClientPayload_AccountType {
	p := new(ClientPayload_AccountType)
	*p = x
	return p
}

func (x ClientPayload_AccountType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientPayload_AccountType) Descriptor() protoreflect.EnumDescriptor {
	return file_waWa6_WAWebProtobufsWa6_proto_enumTypes[1].Descriptor()
}

func (ClientPayload_AccountType) Type() protoreflect.EnumType {
	return &file_waWa6_WAWebProtobufsWa6_proto_enumTypes[1]
}

func (x ClientPayload_AccountType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ClientPayload_AccountType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ClientPayload_AccountType(num)
	return nil
}

// Deprecated: Use ClientPayload_AccountType.Descriptor instead.
func (ClientPayload_AccountType) EnumDescriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 1}
}

type ClientPayload_Product int32

const (
	ClientPayload_WHATSAPP     ClientPayload_Product = 0
	ClientPayload_MESSENGER    ClientPayload_Product = 1
	ClientPayload_INTEROP      ClientPayload_Product = 2
	ClientPayload_INTEROP_MSGR ClientPayload_Product = 3
	ClientPayload_WHATSAPP_LID ClientPayload_Product = 4
)

// Enum value maps for ClientPayload_Product.
var (
	ClientPayload_Product_name = map[int32]string{
		0: "WHATSAPP",
		1: "MESSENGER",
		2: "INTEROP",
		3: "INTEROP_MSGR",
		4: "WHATSAPP_LID",
	}
	ClientPayload_Product_value = map[string]int32{
		"WHATSAPP":     0,
		"MESSENGER":    1,
		"INTEROP":      2,
		"INTEROP_MSGR": 3,
		"WHATSAPP_LID": 4,
	}
)

func (x ClientPayload_Product) Enum() *ClientPayload_Product {
	p := new(ClientPayload_Product)
	*p = x
	return p
}

func (x ClientPayload_Product) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientPayload_Product) Descriptor() protoreflect.EnumDescriptor {
	return file_waWa6_WAWebProtobufsWa6_proto_enumTypes[2].Descriptor()
}

func (ClientPayload_Product) Type() protoreflect.EnumType {
	return &file_waWa6_WAWebProtobufsWa6_proto_enumTypes[2]
}

func (x ClientPayload_Product) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ClientPayload_Product) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ClientPayload_Product(num)
	return nil
}

// Deprecated: Use ClientPayload_Product.Descriptor instead.
func (ClientPayload_Product) EnumDescriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 2}
}

type ClientPayload_ConnectType int32

const (
	ClientPayload_CELLULAR_UNKNOWN ClientPayload_ConnectType = 0
	ClientPayload_WIFI_UNKNOWN     ClientPayload_ConnectType = 1
	ClientPayload_CELLULAR_EDGE    ClientPayload_ConnectType = 100
	ClientPayload_CELLULAR_IDEN    ClientPayload_ConnectType = 101
	ClientPayload_CELLULAR_UMTS    ClientPayload_ConnectType = 102
	ClientPayload_CELLULAR_EVDO    ClientPayload_ConnectType = 103
	ClientPayload_CELLULAR_GPRS    ClientPayload_ConnectType = 104
	ClientPayload_CELLULAR_HSDPA   ClientPayload_ConnectType = 105
	ClientPayload_CELLULAR_HSUPA   ClientPayload_ConnectType = 106
	ClientPayload_CELLULAR_HSPA    ClientPayload_ConnectType = 107
	ClientPayload_CELLULAR_CDMA    ClientPayload_ConnectType = 108
	ClientPayload_CELLULAR_1XRTT   ClientPayload_ConnectType = 109
	ClientPayload_CELLULAR_EHRPD   ClientPayload_ConnectType = 110
	ClientPayload_CELLULAR_LTE     ClientPayload_ConnectType = 111
	ClientPayload_CELLULAR_HSPAP   ClientPayload_ConnectType = 112
)

// Enum value maps for ClientPayload_ConnectType.
var (
	ClientPayload_ConnectType_name = map[int32]string{
		0:   "CELLULAR_UNKNOWN",
		1:   "WIFI_UNKNOWN",
		100: "CELLULAR_EDGE",
		101: "CELLULAR_IDEN",
		102: "CELLULAR_UMTS",
		103: "CELLULAR_EVDO",
		104: "CELLULAR_GPRS",
		105: "CELLULAR_HSDPA",
		106: "CELLULAR_HSUPA",
		107: "CELLULAR_HSPA",
		108: "CELLULAR_CDMA",
		109: "CELLULAR_1XRTT",
		110: "CELLULAR_EHRPD",
		111: "CELLULAR_LTE",
		112: "CELLULAR_HSPAP",
	}
	ClientPayload_ConnectType_value = map[string]int32{
		"CELLULAR_UNKNOWN": 0,
		"WIFI_UNKNOWN":     1,
		"CELLULAR_EDGE":    100,
		"CELLULAR_IDEN":    101,
		"CELLULAR_UMTS":    102,
		"CELLULAR_EVDO":    103,
		"CELLULAR_GPRS":    104,
		"CELLULAR_HSDPA":   105,
		"CELLULAR_HSUPA":   106,
		"CELLULAR_HSPA":    107,
		"CELLULAR_CDMA":    108,
		"CELLULAR_1XRTT":   109,
		"CELLULAR_EHRPD":   110,
		"CELLULAR_LTE":     111,
		"CELLULAR_HSPAP":   112,
	}
)

func (x ClientPayload_ConnectType) Enum() *ClientPayload_ConnectType {
	p := new(ClientPayload_ConnectType)
	*p = x
	return p
}

func (x ClientPayload_ConnectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientPayload_ConnectType) Descriptor() protoreflect.EnumDescriptor {
	return file_waWa6_WAWebProtobufsWa6_proto_enumTypes[3].Descriptor()
}

func (ClientPayload_ConnectType) Type() protoreflect.EnumType {
	return &file_waWa6_WAWebProtobufsWa6_proto_enumTypes[3]
}

func (x ClientPayload_ConnectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ClientPayload_ConnectType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ClientPayload_ConnectType(num)
	return nil
}

// Deprecated: Use ClientPayload_ConnectType.Descriptor instead.
func (ClientPayload_ConnectType) EnumDescriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 3}
}

type ClientPayload_ConnectReason int32

const (
	ClientPayload_PUSH            ClientPayload_ConnectReason = 0
	ClientPayload_USER_ACTIVATED  ClientPayload_ConnectReason = 1
	ClientPayload_SCHEDULED       ClientPayload_ConnectReason = 2
	ClientPayload_ERROR_RECONNECT ClientPayload_ConnectReason = 3
	ClientPayload_NETWORK_SWITCH  ClientPayload_ConnectReason = 4
	ClientPayload_PING_RECONNECT  ClientPayload_ConnectReason = 5
	ClientPayload_UNKNOWN         ClientPayload_ConnectReason = 6
)

// Enum value maps for ClientPayload_ConnectReason.
var (
	ClientPayload_ConnectReason_name = map[int32]string{
		0: "PUSH",
		1: "USER_ACTIVATED",
		2: "SCHEDULED",
		3: "ERROR_RECONNECT",
		4: "NETWORK_SWITCH",
		5: "PING_RECONNECT",
		6: "UNKNOWN",
	}
	ClientPayload_ConnectReason_value = map[string]int32{
		"PUSH":            0,
		"USER_ACTIVATED":  1,
		"SCHEDULED":       2,
		"ERROR_RECONNECT": 3,
		"NETWORK_SWITCH":  4,
		"PING_RECONNECT":  5,
		"UNKNOWN":         6,
	}
)

func (x ClientPayload_ConnectReason) Enum() *ClientPayload_ConnectReason {
	p := new(ClientPayload_ConnectReason)
	*p = x
	return p
}

func (x ClientPayload_ConnectReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientPayload_ConnectReason) Descriptor() protoreflect.EnumDescriptor {
	return file_waWa6_WAWebProtobufsWa6_proto_enumTypes[4].Descriptor()
}

func (ClientPayload_ConnectReason) Type() protoreflect.EnumType {
	return &file_waWa6_WAWebProtobufsWa6_proto_enumTypes[4]
}

func (x ClientPayload_ConnectReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ClientPayload_ConnectReason) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ClientPayload_ConnectReason(num)
	return nil
}

// Deprecated: Use ClientPayload_ConnectReason.Descriptor instead.
func (ClientPayload_ConnectReason) EnumDescriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 4}
}

type ClientPayload_IOSAppExtension int32

const (
	ClientPayload_SHARE_EXTENSION   ClientPayload_IOSAppExtension = 0
	ClientPayload_SERVICE_EXTENSION ClientPayload_IOSAppExtension = 1
	ClientPayload_INTENTS_EXTENSION ClientPayload_IOSAppExtension = 2
)

// Enum value maps for ClientPayload_IOSAppExtension.
var (
	ClientPayload_IOSAppExtension_name = map[int32]string{
		0: "SHARE_EXTENSION",
		1: "SERVICE_EXTENSION",
		2: "INTENTS_EXTENSION",
	}
	ClientPayload_IOSAppExtension_value = map[string]int32{
		"SHARE_EXTENSION":   0,
		"SERVICE_EXTENSION": 1,
		"INTENTS_EXTENSION": 2,
	}
)

func (x ClientPayload_IOSAppExtension) Enum() *ClientPayload_IOSAppExtension {
	p := new(ClientPayload_IOSAppExtension)
	*p = x
	return p
}

func (x ClientPayload_IOSAppExtension) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientPayload_IOSAppExtension) Descriptor() protoreflect.EnumDescriptor {
	return file_waWa6_WAWebProtobufsWa6_proto_enumTypes[5].Descriptor()
}

func (ClientPayload_IOSAppExtension) Type() protoreflect.EnumType {
	return &file_waWa6_WAWebProtobufsWa6_proto_enumTypes[5]
}

func (x ClientPayload_IOSAppExtension) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ClientPayload_IOSAppExtension) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ClientPayload_IOSAppExtension(num)
	return nil
}

// Deprecated: Use ClientPayload_IOSAppExtension.Descriptor instead.
func (ClientPayload_IOSAppExtension) EnumDescriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 5}
}

type ClientPayload_DNSSource_DNSResolutionMethod int32

const (
	ClientPayload_DNSSource_SYSTEM    ClientPayload_DNSSource_DNSResolutionMethod = 0
	ClientPayload_DNSSource_GOOGLE    ClientPayload_DNSSource_DNSResolutionMethod = 1
	ClientPayload_DNSSource_HARDCODED ClientPayload_DNSSource_DNSResolutionMethod = 2
	ClientPayload_DNSSource_OVERRIDE  ClientPayload_DNSSource_DNSResolutionMethod = 3
	ClientPayload_DNSSource_FALLBACK  ClientPayload_DNSSource_DNSResolutionMethod = 4
	ClientPayload_DNSSource_MNS       ClientPayload_DNSSource_DNSResolutionMethod = 5
)

// Enum value maps for ClientPayload_DNSSource_DNSResolutionMethod.
var (
	ClientPayload_DNSSource_DNSResolutionMethod_name = map[int32]string{
		0: "SYSTEM",
		1: "GOOGLE",
		2: "HARDCODED",
		3: "OVERRIDE",
		4: "FALLBACK",
		5: "MNS",
	}
	ClientPayload_DNSSource_DNSResolutionMethod_value = map[string]int32{
		"SYSTEM":    0,
		"GOOGLE":    1,
		"HARDCODED": 2,
		"OVERRIDE":  3,
		"FALLBACK":  4,
		"MNS":       5,
	}
)

func (x ClientPayload_DNSSource_DNSResolutionMethod) Enum() *ClientPayload_DNSSource_DNSResolutionMethod {
	p := new(ClientPayload_DNSSource_DNSResolutionMethod)
	*p = x
	return p
}

func (x ClientPayload_DNSSource_DNSResolutionMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientPayload_DNSSource_DNSResolutionMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_waWa6_WAWebProtobufsWa6_proto_enumTypes[6].Descriptor()
}

func (ClientPayload_DNSSource_DNSResolutionMethod) Type() protoreflect.EnumType {
	return &file_waWa6_WAWebProtobufsWa6_proto_enumTypes[6]
}

func (x ClientPayload_DNSSource_DNSResolutionMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ClientPayload_DNSSource_DNSResolutionMethod) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ClientPayload_DNSSource_DNSResolutionMethod(num)
	return nil
}

// Deprecated: Use ClientPayload_DNSSource_DNSResolutionMethod.Descriptor instead.
func (ClientPayload_DNSSource_DNSResolutionMethod) EnumDescriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 0, 0}
}

type ClientPayload_WebInfo_WebSubPlatform int32

const (
	ClientPayload_WebInfo_WEB_BROWSER ClientPayload_WebInfo_WebSubPlatform = 0
	ClientPayload_WebInfo_APP_STORE   ClientPayload_WebInfo_WebSubPlatform = 1
	ClientPayload_WebInfo_WIN_STORE   ClientPayload_WebInfo_WebSubPlatform = 2
	ClientPayload_WebInfo_DARWIN      ClientPayload_WebInfo_WebSubPlatform = 3
	ClientPayload_WebInfo_WIN32       ClientPayload_WebInfo_WebSubPlatform = 4
	ClientPayload_WebInfo_WIN_HYBRID  ClientPayload_WebInfo_WebSubPlatform = 5
)

// Enum value maps for ClientPayload_WebInfo_WebSubPlatform.
var (
	ClientPayload_WebInfo_WebSubPlatform_name = map[int32]string{
		0: "WEB_BROWSER",
		1: "APP_STORE",
		2: "WIN_STORE",
		3: "DARWIN",
		4: "WIN32",
		5: "WIN_HYBRID",
	}
	ClientPayload_WebInfo_WebSubPlatform_value = map[string]int32{
		"WEB_BROWSER": 0,
		"APP_STORE":   1,
		"WIN_STORE":   2,
		"DARWIN":      3,
		"WIN32":       4,
		"WIN_HYBRID":  5,
	}
)

func (x ClientPayload_WebInfo_WebSubPlatform) Enum() *ClientPayload_WebInfo_WebSubPlatform {
	p := new(ClientPayload_WebInfo_WebSubPlatform)
	*p = x
	return p
}

func (x ClientPayload_WebInfo_WebSubPlatform) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientPayload_WebInfo_WebSubPlatform) Descriptor() protoreflect.EnumDescriptor {
	return file_waWa6_WAWebProtobufsWa6_proto_enumTypes[7].Descriptor()
}

func (ClientPayload_WebInfo_WebSubPlatform) Type() protoreflect.EnumType {
	return &file_waWa6_WAWebProtobufsWa6_proto_enumTypes[7]
}

func (x ClientPayload_WebInfo_WebSubPlatform) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ClientPayload_WebInfo_WebSubPlatform) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ClientPayload_WebInfo_WebSubPlatform(num)
	return nil
}

// Deprecated: Use ClientPayload_WebInfo_WebSubPlatform.Descriptor instead.
func (ClientPayload_WebInfo_WebSubPlatform) EnumDescriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 1, 0}
}

type ClientPayload_UserAgent_DeviceType int32

const (
	ClientPayload_UserAgent_PHONE    ClientPayload_UserAgent_DeviceType = 0
	ClientPayload_UserAgent_TABLET   ClientPayload_UserAgent_DeviceType = 1
	ClientPayload_UserAgent_DESKTOP  ClientPayload_UserAgent_DeviceType = 2
	ClientPayload_UserAgent_WEARABLE ClientPayload_UserAgent_DeviceType = 3
	ClientPayload_UserAgent_VR       ClientPayload_UserAgent_DeviceType = 4
)

// Enum value maps for ClientPayload_UserAgent_DeviceType.
var (
	ClientPayload_UserAgent_DeviceType_name = map[int32]string{
		0: "PHONE",
		1: "TABLET",
		2: "DESKTOP",
		3: "WEARABLE",
		4: "VR",
	}
	ClientPayload_UserAgent_DeviceType_value = map[string]int32{
		"PHONE":    0,
		"TABLET":   1,
		"DESKTOP":  2,
		"WEARABLE": 3,
		"VR":       4,
	}
)

func (x ClientPayload_UserAgent_DeviceType) Enum() *ClientPayload_UserAgent_DeviceType {
	p := new(ClientPayload_UserAgent_DeviceType)
	*p = x
	return p
}

func (x ClientPayload_UserAgent_DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientPayload_UserAgent_DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_waWa6_WAWebProtobufsWa6_proto_enumTypes[8].Descriptor()
}

func (ClientPayload_UserAgent_DeviceType) Type() protoreflect.EnumType {
	return &file_waWa6_WAWebProtobufsWa6_proto_enumTypes[8]
}

func (x ClientPayload_UserAgent_DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ClientPayload_UserAgent_DeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ClientPayload_UserAgent_DeviceType(num)
	return nil
}

// Deprecated: Use ClientPayload_UserAgent_DeviceType.Descriptor instead.
func (ClientPayload_UserAgent_DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 2, 0}
}

type ClientPayload_UserAgent_ReleaseChannel int32

const (
	ClientPayload_UserAgent_RELEASE ClientPayload_UserAgent_ReleaseChannel = 0
	ClientPayload_UserAgent_BETA    ClientPayload_UserAgent_ReleaseChannel = 1
	ClientPayload_UserAgent_ALPHA   ClientPayload_UserAgent_ReleaseChannel = 2
	ClientPayload_UserAgent_DEBUG   ClientPayload_UserAgent_ReleaseChannel = 3
)

// Enum value maps for ClientPayload_UserAgent_ReleaseChannel.
var (
	ClientPayload_UserAgent_ReleaseChannel_name = map[int32]string{
		0: "RELEASE",
		1: "BETA",
		2: "ALPHA",
		3: "DEBUG",
	}
	ClientPayload_UserAgent_ReleaseChannel_value = map[string]int32{
		"RELEASE": 0,
		"BETA":    1,
		"ALPHA":   2,
		"DEBUG":   3,
	}
)

func (x ClientPayload_UserAgent_ReleaseChannel) Enum() *ClientPayload_UserAgent_ReleaseChannel {
	p := new(ClientPayload_UserAgent_ReleaseChannel)
	*p = x
	return p
}

func (x ClientPayload_UserAgent_ReleaseChannel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientPayload_UserAgent_ReleaseChannel) Descriptor() protoreflect.EnumDescriptor {
	return file_waWa6_WAWebProtobufsWa6_proto_enumTypes[9].Descriptor()
}

func (ClientPayload_UserAgent_ReleaseChannel) Type() protoreflect.EnumType {
	return &file_waWa6_WAWebProtobufsWa6_proto_enumTypes[9]
}

func (x ClientPayload_UserAgent_ReleaseChannel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ClientPayload_UserAgent_ReleaseChannel) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ClientPayload_UserAgent_ReleaseChannel(num)
	return nil
}

// Deprecated: Use ClientPayload_UserAgent_ReleaseChannel.Descriptor instead.
func (ClientPayload_UserAgent_ReleaseChannel) EnumDescriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 2, 1}
}

type ClientPayload_UserAgent_Platform int32

const (
	ClientPayload_UserAgent_ANDROID        ClientPayload_UserAgent_Platform = 0
	ClientPayload_UserAgent_IOS            ClientPayload_UserAgent_Platform = 1
	ClientPayload_UserAgent_WINDOWS_PHONE  ClientPayload_UserAgent_Platform = 2
	ClientPayload_UserAgent_BLACKBERRY     ClientPayload_UserAgent_Platform = 3
	ClientPayload_UserAgent_BLACKBERRYX    ClientPayload_UserAgent_Platform = 4
	ClientPayload_UserAgent_S40            ClientPayload_UserAgent_Platform = 5
	ClientPayload_UserAgent_S60            ClientPayload_UserAgent_Platform = 6
	ClientPayload_UserAgent_PYTHON_CLIENT  ClientPayload_UserAgent_Platform = 7
	ClientPayload_UserAgent_TIZEN          ClientPayload_UserAgent_Platform = 8
	ClientPayload_UserAgent_ENTERPRISE     ClientPayload_UserAgent_Platform = 9
	ClientPayload_UserAgent_SMB_ANDROID    ClientPayload_UserAgent_Platform = 10
	ClientPayload_UserAgent_KAIOS          ClientPayload_UserAgent_Platform = 11
	ClientPayload_UserAgent_SMB_IOS        ClientPayload_UserAgent_Platform = 12
	ClientPayload_UserAgent_WINDOWS        ClientPayload_UserAgent_Platform = 13
	ClientPayload_UserAgent_WEB            ClientPayload_UserAgent_Platform = 14
	ClientPayload_UserAgent_PORTAL         ClientPayload_UserAgent_Platform = 15
	ClientPayload_UserAgent_GREEN_ANDROID  ClientPayload_UserAgent_Platform = 16
	ClientPayload_UserAgent_GREEN_IPHONE   ClientPayload_UserAgent_Platform = 17
	ClientPayload_UserAgent_BLUE_ANDROID   ClientPayload_UserAgent_Platform = 18
	ClientPayload_UserAgent_BLUE_IPHONE    ClientPayload_UserAgent_Platform = 19
	ClientPayload_UserAgent_FBLITE_ANDROID ClientPayload_UserAgent_Platform = 20
	ClientPayload_UserAgent_MLITE_ANDROID  ClientPayload_UserAgent_Platform = 21
	ClientPayload_UserAgent_IGLITE_ANDROID ClientPayload_UserAgent_Platform = 22
	ClientPayload_UserAgent_PAGE           ClientPayload_UserAgent_Platform = 23
	ClientPayload_UserAgent_MACOS          ClientPayload_UserAgent_Platform = 24
	ClientPayload_UserAgent_OCULUS_MSG     ClientPayload_UserAgent_Platform = 25
	ClientPayload_UserAgent_OCULUS_CALL    ClientPayload_UserAgent_Platform = 26
	ClientPayload_UserAgent_MILAN          ClientPayload_UserAgent_Platform = 27
	ClientPayload_UserAgent_CAPI           ClientPayload_UserAgent_Platform = 28
	ClientPayload_UserAgent_WEAROS         ClientPayload_UserAgent_Platform = 29
	ClientPayload_UserAgent_ARDEVICE       ClientPayload_UserAgent_Platform = 30
	ClientPayload_UserAgent_VRDEVICE       ClientPayload_UserAgent_Platform = 31
	ClientPayload_UserAgent_BLUE_WEB       ClientPayload_UserAgent_Platform = 32
	ClientPayload_UserAgent_IPAD           ClientPayload_UserAgent_Platform = 33
	ClientPayload_UserAgent_TEST           ClientPayload_UserAgent_Platform = 34
	ClientPayload_UserAgent_SMART_GLASSES  ClientPayload_UserAgent_Platform = 35
	ClientPayload_UserAgent_BLUE_VR        ClientPayload_UserAgent_Platform = 36
)

// Enum value maps for ClientPayload_UserAgent_Platform.
var (
	ClientPayload_UserAgent_Platform_name = map[int32]string{
		0:  "ANDROID",
		1:  "IOS",
		2:  "WINDOWS_PHONE",
		3:  "BLACKBERRY",
		4:  "BLACKBERRYX",
		5:  "S40",
		6:  "S60",
		7:  "PYTHON_CLIENT",
		8:  "TIZEN",
		9:  "ENTERPRISE",
		10: "SMB_ANDROID",
		11: "KAIOS",
		12: "SMB_IOS",
		13: "WINDOWS",
		14: "WEB",
		15: "PORTAL",
		16: "GREEN_ANDROID",
		17: "GREEN_IPHONE",
		18: "BLUE_ANDROID",
		19: "BLUE_IPHONE",
		20: "FBLITE_ANDROID",
		21: "MLITE_ANDROID",
		22: "IGLITE_ANDROID",
		23: "PAGE",
		24: "MACOS",
		25: "OCULUS_MSG",
		26: "OCULUS_CALL",
		27: "MILAN",
		28: "CAPI",
		29: "WEAROS",
		30: "ARDEVICE",
		31: "VRDEVICE",
		32: "BLUE_WEB",
		33: "IPAD",
		34: "TEST",
		35: "SMART_GLASSES",
		36: "BLUE_VR",
	}
	ClientPayload_UserAgent_Platform_value = map[string]int32{
		"ANDROID":        0,
		"IOS":            1,
		"WINDOWS_PHONE":  2,
		"BLACKBERRY":     3,
		"BLACKBERRYX":    4,
		"S40":            5,
		"S60":            6,
		"PYTHON_CLIENT":  7,
		"TIZEN":          8,
		"ENTERPRISE":     9,
		"SMB_ANDROID":    10,
		"KAIOS":          11,
		"SMB_IOS":        12,
		"WINDOWS":        13,
		"WEB":            14,
		"PORTAL":         15,
		"GREEN_ANDROID":  16,
		"GREEN_IPHONE":   17,
		"BLUE_ANDROID":   18,
		"BLUE_IPHONE":    19,
		"FBLITE_ANDROID": 20,
		"MLITE_ANDROID":  21,
		"IGLITE_ANDROID": 22,
		"PAGE":           23,
		"MACOS":          24,
		"OCULUS_MSG":     25,
		"OCULUS_CALL":    26,
		"MILAN":          27,
		"CAPI":           28,
		"WEAROS":         29,
		"ARDEVICE":       30,
		"VRDEVICE":       31,
		"BLUE_WEB":       32,
		"IPAD":           33,
		"TEST":           34,
		"SMART_GLASSES":  35,
		"BLUE_VR":        36,
	}
)

func (x ClientPayload_UserAgent_Platform) Enum() *ClientPayload_UserAgent_Platform {
	p := new(ClientPayload_UserAgent_Platform)
	*p = x
	return p
}

func (x ClientPayload_UserAgent_Platform) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientPayload_UserAgent_Platform) Descriptor() protoreflect.EnumDescriptor {
	return file_waWa6_WAWebProtobufsWa6_proto_enumTypes[10].Descriptor()
}

func (ClientPayload_UserAgent_Platform) Type() protoreflect.EnumType {
	return &file_waWa6_WAWebProtobufsWa6_proto_enumTypes[10]
}

func (x ClientPayload_UserAgent_Platform) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ClientPayload_UserAgent_Platform) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ClientPayload_UserAgent_Platform(num)
	return nil
}

// Deprecated: Use ClientPayload_UserAgent_Platform.Descriptor instead.
func (ClientPayload_UserAgent_Platform) EnumDescriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 2, 2}
}

type ClientPayload struct {
	state                protoimpl.MessageState                       `protogen:"open.v1"`
	Username             *uint64                                      `protobuf:"varint,1,opt,name=username" json:"username,omitempty"`
	Passive              *bool                                        `protobuf:"varint,3,opt,name=passive" json:"passive,omitempty"`
	UserAgent            *ClientPayload_UserAgent                     `protobuf:"bytes,5,opt,name=userAgent" json:"userAgent,omitempty"`
	WebInfo              *ClientPayload_WebInfo                       `protobuf:"bytes,6,opt,name=webInfo" json:"webInfo,omitempty"`
	PushName             *string                                      `protobuf:"bytes,7,opt,name=pushName" json:"pushName,omitempty"`
	SessionID            *int32                                       `protobuf:"fixed32,9,opt,name=sessionID" json:"sessionID,omitempty"`
	ShortConnect         *bool                                        `protobuf:"varint,10,opt,name=shortConnect" json:"shortConnect,omitempty"`
	ConnectType          *ClientPayload_ConnectType                   `protobuf:"varint,12,opt,name=connectType,enum=WAWebProtobufsWa6.ClientPayload_ConnectType" json:"connectType,omitempty"`
	ConnectReason        *ClientPayload_ConnectReason                 `protobuf:"varint,13,opt,name=connectReason,enum=WAWebProtobufsWa6.ClientPayload_ConnectReason" json:"connectReason,omitempty"`
	Shards               []int32                                      `protobuf:"varint,14,rep,name=shards" json:"shards,omitempty"`
	DnsSource            *ClientPayload_DNSSource                     `protobuf:"bytes,15,opt,name=dnsSource" json:"dnsSource,omitempty"`
	ConnectAttemptCount  *uint32                                      `protobuf:"varint,16,opt,name=connectAttemptCount" json:"connectAttemptCount,omitempty"`
	Device               *uint32                                      `protobuf:"varint,18,opt,name=device" json:"device,omitempty"`
	DevicePairingData    *ClientPayload_DevicePairingRegistrationData `protobuf:"bytes,19,opt,name=devicePairingData" json:"devicePairingData,omitempty"`
	Product              *ClientPayload_Product                       `protobuf:"varint,20,opt,name=product,enum=WAWebProtobufsWa6.ClientPayload_Product" json:"product,omitempty"`
	FbCat                []byte                                       `protobuf:"bytes,21,opt,name=fbCat" json:"fbCat,omitempty"`
	FbUserAgent          []byte                                       `protobuf:"bytes,22,opt,name=fbUserAgent" json:"fbUserAgent,omitempty"`
	Oc                   *bool                                        `protobuf:"varint,23,opt,name=oc" json:"oc,omitempty"`
	Lc                   *int32                                       `protobuf:"varint,24,opt,name=lc" json:"lc,omitempty"`
	IosAppExtension      *ClientPayload_IOSAppExtension               `protobuf:"varint,30,opt,name=iosAppExtension,enum=WAWebProtobufsWa6.ClientPayload_IOSAppExtension" json:"iosAppExtension,omitempty"`
	FbAppID              *uint64                                      `protobuf:"varint,31,opt,name=fbAppID" json:"fbAppID,omitempty"`
	FbDeviceID           []byte                                       `protobuf:"bytes,32,opt,name=fbDeviceID" json:"fbDeviceID,omitempty"`
	Pull                 *bool                                        `protobuf:"varint,33,opt,name=pull" json:"pull,omitempty"`
	PaddingBytes         []byte                                       `protobuf:"bytes,34,opt,name=paddingBytes" json:"paddingBytes,omitempty"`
	YearClass            *int32                                       `protobuf:"varint,36,opt,name=yearClass" json:"yearClass,omitempty"`
	MemClass             *int32                                       `protobuf:"varint,37,opt,name=memClass" json:"memClass,omitempty"`
	InteropData          *ClientPayload_InteropData                   `protobuf:"bytes,38,opt,name=interopData" json:"interopData,omitempty"`
	TrafficAnonymization *ClientPayload_TrafficAnonymization          `protobuf:"varint,40,opt,name=trafficAnonymization,enum=WAWebProtobufsWa6.ClientPayload_TrafficAnonymization" json:"trafficAnonymization,omitempty"`
	LidDbMigrated        *bool                                        `protobuf:"varint,41,opt,name=lidDbMigrated" json:"lidDbMigrated,omitempty"`
	AccountType          *ClientPayload_AccountType                   `protobuf:"varint,42,opt,name=accountType,enum=WAWebProtobufsWa6.ClientPayload_AccountType" json:"accountType,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ClientPayload) Reset() {
	*x = ClientPayload{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientPayload) ProtoMessage() {}

func (x *ClientPayload) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientPayload.ProtoReflect.Descriptor instead.
func (*ClientPayload) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0}
}

func (x *ClientPayload) GetUsername() uint64 {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return 0
}

func (x *ClientPayload) GetPassive() bool {
	if x != nil && x.Passive != nil {
		return *x.Passive
	}
	return false
}

func (x *ClientPayload) GetUserAgent() *ClientPayload_UserAgent {
	if x != nil {
		return x.UserAgent
	}
	return nil
}

func (x *ClientPayload) GetWebInfo() *ClientPayload_WebInfo {
	if x != nil {
		return x.WebInfo
	}
	return nil
}

func (x *ClientPayload) GetPushName() string {
	if x != nil && x.PushName != nil {
		return *x.PushName
	}
	return ""
}

func (x *ClientPayload) GetSessionID() int32 {
	if x != nil && x.SessionID != nil {
		return *x.SessionID
	}
	return 0
}

func (x *ClientPayload) GetShortConnect() bool {
	if x != nil && x.ShortConnect != nil {
		return *x.ShortConnect
	}
	return false
}

func (x *ClientPayload) GetConnectType() ClientPayload_ConnectType {
	if x != nil && x.ConnectType != nil {
		return *x.ConnectType
	}
	return ClientPayload_CELLULAR_UNKNOWN
}

func (x *ClientPayload) GetConnectReason() ClientPayload_ConnectReason {
	if x != nil && x.ConnectReason != nil {
		return *x.ConnectReason
	}
	return ClientPayload_PUSH
}

func (x *ClientPayload) GetShards() []int32 {
	if x != nil {
		return x.Shards
	}
	return nil
}

func (x *ClientPayload) GetDnsSource() *ClientPayload_DNSSource {
	if x != nil {
		return x.DnsSource
	}
	return nil
}

func (x *ClientPayload) GetConnectAttemptCount() uint32 {
	if x != nil && x.ConnectAttemptCount != nil {
		return *x.ConnectAttemptCount
	}
	return 0
}

func (x *ClientPayload) GetDevice() uint32 {
	if x != nil && x.Device != nil {
		return *x.Device
	}
	return 0
}

func (x *ClientPayload) GetDevicePairingData() *ClientPayload_DevicePairingRegistrationData {
	if x != nil {
		return x.DevicePairingData
	}
	return nil
}

func (x *ClientPayload) GetProduct() ClientPayload_Product {
	if x != nil && x.Product != nil {
		return *x.Product
	}
	return ClientPayload_WHATSAPP
}

func (x *ClientPayload) GetFbCat() []byte {
	if x != nil {
		return x.FbCat
	}
	return nil
}

func (x *ClientPayload) GetFbUserAgent() []byte {
	if x != nil {
		return x.FbUserAgent
	}
	return nil
}

func (x *ClientPayload) GetOc() bool {
	if x != nil && x.Oc != nil {
		return *x.Oc
	}
	return false
}

func (x *ClientPayload) GetLc() int32 {
	if x != nil && x.Lc != nil {
		return *x.Lc
	}
	return 0
}

func (x *ClientPayload) GetIosAppExtension() ClientPayload_IOSAppExtension {
	if x != nil && x.IosAppExtension != nil {
		return *x.IosAppExtension
	}
	return ClientPayload_SHARE_EXTENSION
}

func (x *ClientPayload) GetFbAppID() uint64 {
	if x != nil && x.FbAppID != nil {
		return *x.FbAppID
	}
	return 0
}

func (x *ClientPayload) GetFbDeviceID() []byte {
	if x != nil {
		return x.FbDeviceID
	}
	return nil
}

func (x *ClientPayload) GetPull() bool {
	if x != nil && x.Pull != nil {
		return *x.Pull
	}
	return false
}

func (x *ClientPayload) GetPaddingBytes() []byte {
	if x != nil {
		return x.PaddingBytes
	}
	return nil
}

func (x *ClientPayload) GetYearClass() int32 {
	if x != nil && x.YearClass != nil {
		return *x.YearClass
	}
	return 0
}

func (x *ClientPayload) GetMemClass() int32 {
	if x != nil && x.MemClass != nil {
		return *x.MemClass
	}
	return 0
}

func (x *ClientPayload) GetInteropData() *ClientPayload_InteropData {
	if x != nil {
		return x.InteropData
	}
	return nil
}

func (x *ClientPayload) GetTrafficAnonymization() ClientPayload_TrafficAnonymization {
	if x != nil && x.TrafficAnonymization != nil {
		return *x.TrafficAnonymization
	}
	return ClientPayload_OFF
}

func (x *ClientPayload) GetLidDbMigrated() bool {
	if x != nil && x.LidDbMigrated != nil {
		return *x.LidDbMigrated
	}
	return false
}

func (x *ClientPayload) GetAccountType() ClientPayload_AccountType {
	if x != nil && x.AccountType != nil {
		return *x.AccountType
	}
	return ClientPayload_DEFAULT
}

type HandshakeMessage struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	ClientHello   *HandshakeMessage_ClientHello  `protobuf:"bytes,2,opt,name=clientHello" json:"clientHello,omitempty"`
	ServerHello   *HandshakeMessage_ServerHello  `protobuf:"bytes,3,opt,name=serverHello" json:"serverHello,omitempty"`
	ClientFinish  *HandshakeMessage_ClientFinish `protobuf:"bytes,4,opt,name=clientFinish" json:"clientFinish,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandshakeMessage) Reset() {
	*x = HandshakeMessage{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandshakeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandshakeMessage) ProtoMessage() {}

func (x *HandshakeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandshakeMessage.ProtoReflect.Descriptor instead.
func (*HandshakeMessage) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{1}
}

func (x *HandshakeMessage) GetClientHello() *HandshakeMessage_ClientHello {
	if x != nil {
		return x.ClientHello
	}
	return nil
}

func (x *HandshakeMessage) GetServerHello() *HandshakeMessage_ServerHello {
	if x != nil {
		return x.ServerHello
	}
	return nil
}

func (x *HandshakeMessage) GetClientFinish() *HandshakeMessage_ClientFinish {
	if x != nil {
		return x.ClientFinish
	}
	return nil
}

type ClientPayload_DNSSource struct {
	state         protoimpl.MessageState                       `protogen:"open.v1"`
	DnsMethod     *ClientPayload_DNSSource_DNSResolutionMethod `protobuf:"varint,15,opt,name=dnsMethod,enum=WAWebProtobufsWa6.ClientPayload_DNSSource_DNSResolutionMethod" json:"dnsMethod,omitempty"`
	AppCached     *bool                                        `protobuf:"varint,16,opt,name=appCached" json:"appCached,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientPayload_DNSSource) Reset() {
	*x = ClientPayload_DNSSource{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientPayload_DNSSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientPayload_DNSSource) ProtoMessage() {}

func (x *ClientPayload_DNSSource) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientPayload_DNSSource.ProtoReflect.Descriptor instead.
func (*ClientPayload_DNSSource) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ClientPayload_DNSSource) GetDnsMethod() ClientPayload_DNSSource_DNSResolutionMethod {
	if x != nil && x.DnsMethod != nil {
		return *x.DnsMethod
	}
	return ClientPayload_DNSSource_SYSTEM
}

func (x *ClientPayload_DNSSource) GetAppCached() bool {
	if x != nil && x.AppCached != nil {
		return *x.AppCached
	}
	return false
}

type ClientPayload_WebInfo struct {
	state          protoimpl.MessageState                `protogen:"open.v1"`
	RefToken       *string                               `protobuf:"bytes,1,opt,name=refToken" json:"refToken,omitempty"`
	Version        *string                               `protobuf:"bytes,2,opt,name=version" json:"version,omitempty"`
	WebdPayload    *ClientPayload_WebInfo_WebdPayload    `protobuf:"bytes,3,opt,name=webdPayload" json:"webdPayload,omitempty"`
	WebSubPlatform *ClientPayload_WebInfo_WebSubPlatform `protobuf:"varint,4,opt,name=webSubPlatform,enum=WAWebProtobufsWa6.ClientPayload_WebInfo_WebSubPlatform" json:"webSubPlatform,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ClientPayload_WebInfo) Reset() {
	*x = ClientPayload_WebInfo{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientPayload_WebInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientPayload_WebInfo) ProtoMessage() {}

func (x *ClientPayload_WebInfo) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientPayload_WebInfo.ProtoReflect.Descriptor instead.
func (*ClientPayload_WebInfo) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 1}
}

func (x *ClientPayload_WebInfo) GetRefToken() string {
	if x != nil && x.RefToken != nil {
		return *x.RefToken
	}
	return ""
}

func (x *ClientPayload_WebInfo) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

func (x *ClientPayload_WebInfo) GetWebdPayload() *ClientPayload_WebInfo_WebdPayload {
	if x != nil {
		return x.WebdPayload
	}
	return nil
}

func (x *ClientPayload_WebInfo) GetWebSubPlatform() ClientPayload_WebInfo_WebSubPlatform {
	if x != nil && x.WebSubPlatform != nil {
		return *x.WebSubPlatform
	}
	return ClientPayload_WebInfo_WEB_BROWSER
}

type ClientPayload_UserAgent struct {
	state                       protoimpl.MessageState                  `protogen:"open.v1"`
	Platform                    *ClientPayload_UserAgent_Platform       `protobuf:"varint,1,opt,name=platform,enum=WAWebProtobufsWa6.ClientPayload_UserAgent_Platform" json:"platform,omitempty"`
	AppVersion                  *ClientPayload_UserAgent_AppVersion     `protobuf:"bytes,2,opt,name=appVersion" json:"appVersion,omitempty"`
	Mcc                         *string                                 `protobuf:"bytes,3,opt,name=mcc" json:"mcc,omitempty"`
	Mnc                         *string                                 `protobuf:"bytes,4,opt,name=mnc" json:"mnc,omitempty"`
	OsVersion                   *string                                 `protobuf:"bytes,5,opt,name=osVersion" json:"osVersion,omitempty"`
	Manufacturer                *string                                 `protobuf:"bytes,6,opt,name=manufacturer" json:"manufacturer,omitempty"`
	Device                      *string                                 `protobuf:"bytes,7,opt,name=device" json:"device,omitempty"`
	OsBuildNumber               *string                                 `protobuf:"bytes,8,opt,name=osBuildNumber" json:"osBuildNumber,omitempty"`
	PhoneID                     *string                                 `protobuf:"bytes,9,opt,name=phoneID" json:"phoneID,omitempty"`
	ReleaseChannel              *ClientPayload_UserAgent_ReleaseChannel `protobuf:"varint,10,opt,name=releaseChannel,enum=WAWebProtobufsWa6.ClientPayload_UserAgent_ReleaseChannel" json:"releaseChannel,omitempty"`
	LocaleLanguageIso6391       *string                                 `protobuf:"bytes,11,opt,name=localeLanguageIso6391" json:"localeLanguageIso6391,omitempty"`
	LocaleCountryIso31661Alpha2 *string                                 `protobuf:"bytes,12,opt,name=localeCountryIso31661Alpha2" json:"localeCountryIso31661Alpha2,omitempty"`
	DeviceBoard                 *string                                 `protobuf:"bytes,13,opt,name=deviceBoard" json:"deviceBoard,omitempty"`
	DeviceExpID                 *string                                 `protobuf:"bytes,14,opt,name=deviceExpID" json:"deviceExpID,omitempty"`
	DeviceType                  *ClientPayload_UserAgent_DeviceType     `protobuf:"varint,15,opt,name=deviceType,enum=WAWebProtobufsWa6.ClientPayload_UserAgent_DeviceType" json:"deviceType,omitempty"`
	DeviceModelType             *string                                 `protobuf:"bytes,16,opt,name=deviceModelType" json:"deviceModelType,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *ClientPayload_UserAgent) Reset() {
	*x = ClientPayload_UserAgent{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientPayload_UserAgent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientPayload_UserAgent) ProtoMessage() {}

func (x *ClientPayload_UserAgent) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientPayload_UserAgent.ProtoReflect.Descriptor instead.
func (*ClientPayload_UserAgent) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 2}
}

func (x *ClientPayload_UserAgent) GetPlatform() ClientPayload_UserAgent_Platform {
	if x != nil && x.Platform != nil {
		return *x.Platform
	}
	return ClientPayload_UserAgent_ANDROID
}

func (x *ClientPayload_UserAgent) GetAppVersion() *ClientPayload_UserAgent_AppVersion {
	if x != nil {
		return x.AppVersion
	}
	return nil
}

func (x *ClientPayload_UserAgent) GetMcc() string {
	if x != nil && x.Mcc != nil {
		return *x.Mcc
	}
	return ""
}

func (x *ClientPayload_UserAgent) GetMnc() string {
	if x != nil && x.Mnc != nil {
		return *x.Mnc
	}
	return ""
}

func (x *ClientPayload_UserAgent) GetOsVersion() string {
	if x != nil && x.OsVersion != nil {
		return *x.OsVersion
	}
	return ""
}

func (x *ClientPayload_UserAgent) GetManufacturer() string {
	if x != nil && x.Manufacturer != nil {
		return *x.Manufacturer
	}
	return ""
}

func (x *ClientPayload_UserAgent) GetDevice() string {
	if x != nil && x.Device != nil {
		return *x.Device
	}
	return ""
}

func (x *ClientPayload_UserAgent) GetOsBuildNumber() string {
	if x != nil && x.OsBuildNumber != nil {
		return *x.OsBuildNumber
	}
	return ""
}

func (x *ClientPayload_UserAgent) GetPhoneID() string {
	if x != nil && x.PhoneID != nil {
		return *x.PhoneID
	}
	return ""
}

func (x *ClientPayload_UserAgent) GetReleaseChannel() ClientPayload_UserAgent_ReleaseChannel {
	if x != nil && x.ReleaseChannel != nil {
		return *x.ReleaseChannel
	}
	return ClientPayload_UserAgent_RELEASE
}

func (x *ClientPayload_UserAgent) GetLocaleLanguageIso6391() string {
	if x != nil && x.LocaleLanguageIso6391 != nil {
		return *x.LocaleLanguageIso6391
	}
	return ""
}

func (x *ClientPayload_UserAgent) GetLocaleCountryIso31661Alpha2() string {
	if x != nil && x.LocaleCountryIso31661Alpha2 != nil {
		return *x.LocaleCountryIso31661Alpha2
	}
	return ""
}

func (x *ClientPayload_UserAgent) GetDeviceBoard() string {
	if x != nil && x.DeviceBoard != nil {
		return *x.DeviceBoard
	}
	return ""
}

func (x *ClientPayload_UserAgent) GetDeviceExpID() string {
	if x != nil && x.DeviceExpID != nil {
		return *x.DeviceExpID
	}
	return ""
}

func (x *ClientPayload_UserAgent) GetDeviceType() ClientPayload_UserAgent_DeviceType {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return ClientPayload_UserAgent_PHONE
}

func (x *ClientPayload_UserAgent) GetDeviceModelType() string {
	if x != nil && x.DeviceModelType != nil {
		return *x.DeviceModelType
	}
	return ""
}

type ClientPayload_InteropData struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	AccountID          *uint64                `protobuf:"varint,1,opt,name=accountID" json:"accountID,omitempty"`
	Token              []byte                 `protobuf:"bytes,2,opt,name=token" json:"token,omitempty"`
	EnableReadReceipts *bool                  `protobuf:"varint,3,opt,name=enableReadReceipts" json:"enableReadReceipts,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ClientPayload_InteropData) Reset() {
	*x = ClientPayload_InteropData{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientPayload_InteropData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientPayload_InteropData) ProtoMessage() {}

func (x *ClientPayload_InteropData) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientPayload_InteropData.ProtoReflect.Descriptor instead.
func (*ClientPayload_InteropData) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 3}
}

func (x *ClientPayload_InteropData) GetAccountID() uint64 {
	if x != nil && x.AccountID != nil {
		return *x.AccountID
	}
	return 0
}

func (x *ClientPayload_InteropData) GetToken() []byte {
	if x != nil {
		return x.Token
	}
	return nil
}

func (x *ClientPayload_InteropData) GetEnableReadReceipts() bool {
	if x != nil && x.EnableReadReceipts != nil {
		return *x.EnableReadReceipts
	}
	return false
}

type ClientPayload_DevicePairingRegistrationData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ERegid        []byte                 `protobuf:"bytes,1,opt,name=eRegid" json:"eRegid,omitempty"`
	EKeytype      []byte                 `protobuf:"bytes,2,opt,name=eKeytype" json:"eKeytype,omitempty"`
	EIdent        []byte                 `protobuf:"bytes,3,opt,name=eIdent" json:"eIdent,omitempty"`
	ESkeyID       []byte                 `protobuf:"bytes,4,opt,name=eSkeyID" json:"eSkeyID,omitempty"`
	ESkeyVal      []byte                 `protobuf:"bytes,5,opt,name=eSkeyVal" json:"eSkeyVal,omitempty"`
	ESkeySig      []byte                 `protobuf:"bytes,6,opt,name=eSkeySig" json:"eSkeySig,omitempty"`
	BuildHash     []byte                 `protobuf:"bytes,7,opt,name=buildHash" json:"buildHash,omitempty"`
	DeviceProps   []byte                 `protobuf:"bytes,8,opt,name=deviceProps" json:"deviceProps,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientPayload_DevicePairingRegistrationData) Reset() {
	*x = ClientPayload_DevicePairingRegistrationData{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientPayload_DevicePairingRegistrationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientPayload_DevicePairingRegistrationData) ProtoMessage() {}

func (x *ClientPayload_DevicePairingRegistrationData) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientPayload_DevicePairingRegistrationData.ProtoReflect.Descriptor instead.
func (*ClientPayload_DevicePairingRegistrationData) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 4}
}

func (x *ClientPayload_DevicePairingRegistrationData) GetERegid() []byte {
	if x != nil {
		return x.ERegid
	}
	return nil
}

func (x *ClientPayload_DevicePairingRegistrationData) GetEKeytype() []byte {
	if x != nil {
		return x.EKeytype
	}
	return nil
}

func (x *ClientPayload_DevicePairingRegistrationData) GetEIdent() []byte {
	if x != nil {
		return x.EIdent
	}
	return nil
}

func (x *ClientPayload_DevicePairingRegistrationData) GetESkeyID() []byte {
	if x != nil {
		return x.ESkeyID
	}
	return nil
}

func (x *ClientPayload_DevicePairingRegistrationData) GetESkeyVal() []byte {
	if x != nil {
		return x.ESkeyVal
	}
	return nil
}

func (x *ClientPayload_DevicePairingRegistrationData) GetESkeySig() []byte {
	if x != nil {
		return x.ESkeySig
	}
	return nil
}

func (x *ClientPayload_DevicePairingRegistrationData) GetBuildHash() []byte {
	if x != nil {
		return x.BuildHash
	}
	return nil
}

func (x *ClientPayload_DevicePairingRegistrationData) GetDeviceProps() []byte {
	if x != nil {
		return x.DeviceProps
	}
	return nil
}

type ClientPayload_WebInfo_WebdPayload struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	UsesParticipantInKey     *bool                  `protobuf:"varint,1,opt,name=usesParticipantInKey" json:"usesParticipantInKey,omitempty"`
	SupportsStarredMessages  *bool                  `protobuf:"varint,2,opt,name=supportsStarredMessages" json:"supportsStarredMessages,omitempty"`
	SupportsDocumentMessages *bool                  `protobuf:"varint,3,opt,name=supportsDocumentMessages" json:"supportsDocumentMessages,omitempty"`
	SupportsURLMessages      *bool                  `protobuf:"varint,4,opt,name=supportsURLMessages" json:"supportsURLMessages,omitempty"`
	SupportsMediaRetry       *bool                  `protobuf:"varint,5,opt,name=supportsMediaRetry" json:"supportsMediaRetry,omitempty"`
	SupportsE2EImage         *bool                  `protobuf:"varint,6,opt,name=supportsE2EImage" json:"supportsE2EImage,omitempty"`
	SupportsE2EVideo         *bool                  `protobuf:"varint,7,opt,name=supportsE2EVideo" json:"supportsE2EVideo,omitempty"`
	SupportsE2EAudio         *bool                  `protobuf:"varint,8,opt,name=supportsE2EAudio" json:"supportsE2EAudio,omitempty"`
	SupportsE2EDocument      *bool                  `protobuf:"varint,9,opt,name=supportsE2EDocument" json:"supportsE2EDocument,omitempty"`
	DocumentTypes            *string                `protobuf:"bytes,10,opt,name=documentTypes" json:"documentTypes,omitempty"`
	Features                 []byte                 `protobuf:"bytes,11,opt,name=features" json:"features,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *ClientPayload_WebInfo_WebdPayload) Reset() {
	*x = ClientPayload_WebInfo_WebdPayload{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientPayload_WebInfo_WebdPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientPayload_WebInfo_WebdPayload) ProtoMessage() {}

func (x *ClientPayload_WebInfo_WebdPayload) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientPayload_WebInfo_WebdPayload.ProtoReflect.Descriptor instead.
func (*ClientPayload_WebInfo_WebdPayload) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 1, 0}
}

func (x *ClientPayload_WebInfo_WebdPayload) GetUsesParticipantInKey() bool {
	if x != nil && x.UsesParticipantInKey != nil {
		return *x.UsesParticipantInKey
	}
	return false
}

func (x *ClientPayload_WebInfo_WebdPayload) GetSupportsStarredMessages() bool {
	if x != nil && x.SupportsStarredMessages != nil {
		return *x.SupportsStarredMessages
	}
	return false
}

func (x *ClientPayload_WebInfo_WebdPayload) GetSupportsDocumentMessages() bool {
	if x != nil && x.SupportsDocumentMessages != nil {
		return *x.SupportsDocumentMessages
	}
	return false
}

func (x *ClientPayload_WebInfo_WebdPayload) GetSupportsURLMessages() bool {
	if x != nil && x.SupportsURLMessages != nil {
		return *x.SupportsURLMessages
	}
	return false
}

func (x *ClientPayload_WebInfo_WebdPayload) GetSupportsMediaRetry() bool {
	if x != nil && x.SupportsMediaRetry != nil {
		return *x.SupportsMediaRetry
	}
	return false
}

func (x *ClientPayload_WebInfo_WebdPayload) GetSupportsE2EImage() bool {
	if x != nil && x.SupportsE2EImage != nil {
		return *x.SupportsE2EImage
	}
	return false
}

func (x *ClientPayload_WebInfo_WebdPayload) GetSupportsE2EVideo() bool {
	if x != nil && x.SupportsE2EVideo != nil {
		return *x.SupportsE2EVideo
	}
	return false
}

func (x *ClientPayload_WebInfo_WebdPayload) GetSupportsE2EAudio() bool {
	if x != nil && x.SupportsE2EAudio != nil {
		return *x.SupportsE2EAudio
	}
	return false
}

func (x *ClientPayload_WebInfo_WebdPayload) GetSupportsE2EDocument() bool {
	if x != nil && x.SupportsE2EDocument != nil {
		return *x.SupportsE2EDocument
	}
	return false
}

func (x *ClientPayload_WebInfo_WebdPayload) GetDocumentTypes() string {
	if x != nil && x.DocumentTypes != nil {
		return *x.DocumentTypes
	}
	return ""
}

func (x *ClientPayload_WebInfo_WebdPayload) GetFeatures() []byte {
	if x != nil {
		return x.Features
	}
	return nil
}

type ClientPayload_UserAgent_AppVersion struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Primary       *uint32                `protobuf:"varint,1,opt,name=primary" json:"primary,omitempty"`
	Secondary     *uint32                `protobuf:"varint,2,opt,name=secondary" json:"secondary,omitempty"`
	Tertiary      *uint32                `protobuf:"varint,3,opt,name=tertiary" json:"tertiary,omitempty"`
	Quaternary    *uint32                `protobuf:"varint,4,opt,name=quaternary" json:"quaternary,omitempty"`
	Quinary       *uint32                `protobuf:"varint,5,opt,name=quinary" json:"quinary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientPayload_UserAgent_AppVersion) Reset() {
	*x = ClientPayload_UserAgent_AppVersion{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientPayload_UserAgent_AppVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientPayload_UserAgent_AppVersion) ProtoMessage() {}

func (x *ClientPayload_UserAgent_AppVersion) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientPayload_UserAgent_AppVersion.ProtoReflect.Descriptor instead.
func (*ClientPayload_UserAgent_AppVersion) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{0, 2, 0}
}

func (x *ClientPayload_UserAgent_AppVersion) GetPrimary() uint32 {
	if x != nil && x.Primary != nil {
		return *x.Primary
	}
	return 0
}

func (x *ClientPayload_UserAgent_AppVersion) GetSecondary() uint32 {
	if x != nil && x.Secondary != nil {
		return *x.Secondary
	}
	return 0
}

func (x *ClientPayload_UserAgent_AppVersion) GetTertiary() uint32 {
	if x != nil && x.Tertiary != nil {
		return *x.Tertiary
	}
	return 0
}

func (x *ClientPayload_UserAgent_AppVersion) GetQuaternary() uint32 {
	if x != nil && x.Quaternary != nil {
		return *x.Quaternary
	}
	return 0
}

func (x *ClientPayload_UserAgent_AppVersion) GetQuinary() uint32 {
	if x != nil && x.Quinary != nil {
		return *x.Quinary
	}
	return 0
}

type HandshakeMessage_ClientFinish struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Static        []byte                 `protobuf:"bytes,1,opt,name=static" json:"static,omitempty"`
	Payload       []byte                 `protobuf:"bytes,2,opt,name=payload" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandshakeMessage_ClientFinish) Reset() {
	*x = HandshakeMessage_ClientFinish{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandshakeMessage_ClientFinish) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandshakeMessage_ClientFinish) ProtoMessage() {}

func (x *HandshakeMessage_ClientFinish) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandshakeMessage_ClientFinish.ProtoReflect.Descriptor instead.
func (*HandshakeMessage_ClientFinish) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{1, 0}
}

func (x *HandshakeMessage_ClientFinish) GetStatic() []byte {
	if x != nil {
		return x.Static
	}
	return nil
}

func (x *HandshakeMessage_ClientFinish) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

type HandshakeMessage_ServerHello struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ephemeral     []byte                 `protobuf:"bytes,1,opt,name=ephemeral" json:"ephemeral,omitempty"`
	Static        []byte                 `protobuf:"bytes,2,opt,name=static" json:"static,omitempty"`
	Payload       []byte                 `protobuf:"bytes,3,opt,name=payload" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandshakeMessage_ServerHello) Reset() {
	*x = HandshakeMessage_ServerHello{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandshakeMessage_ServerHello) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandshakeMessage_ServerHello) ProtoMessage() {}

func (x *HandshakeMessage_ServerHello) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandshakeMessage_ServerHello.ProtoReflect.Descriptor instead.
func (*HandshakeMessage_ServerHello) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{1, 1}
}

func (x *HandshakeMessage_ServerHello) GetEphemeral() []byte {
	if x != nil {
		return x.Ephemeral
	}
	return nil
}

func (x *HandshakeMessage_ServerHello) GetStatic() []byte {
	if x != nil {
		return x.Static
	}
	return nil
}

func (x *HandshakeMessage_ServerHello) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

type HandshakeMessage_ClientHello struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ephemeral     []byte                 `protobuf:"bytes,1,opt,name=ephemeral" json:"ephemeral,omitempty"`
	Static        []byte                 `protobuf:"bytes,2,opt,name=static" json:"static,omitempty"`
	Payload       []byte                 `protobuf:"bytes,3,opt,name=payload" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandshakeMessage_ClientHello) Reset() {
	*x = HandshakeMessage_ClientHello{}
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandshakeMessage_ClientHello) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandshakeMessage_ClientHello) ProtoMessage() {}

func (x *HandshakeMessage_ClientHello) ProtoReflect() protoreflect.Message {
	mi := &file_waWa6_WAWebProtobufsWa6_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandshakeMessage_ClientHello.ProtoReflect.Descriptor instead.
func (*HandshakeMessage_ClientHello) Descriptor() ([]byte, []int) {
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP(), []int{1, 2}
}

func (x *HandshakeMessage_ClientHello) GetEphemeral() []byte {
	if x != nil {
		return x.Ephemeral
	}
	return nil
}

func (x *HandshakeMessage_ClientHello) GetStatic() []byte {
	if x != nil {
		return x.Static
	}
	return nil
}

func (x *HandshakeMessage_ClientHello) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

var File_waWa6_WAWebProtobufsWa6_proto protoreflect.FileDescriptor

const file_waWa6_WAWebProtobufsWa6_proto_rawDesc = "" +
	"\n" +
	"\x1dwaWa6/WAWebProtobufsWa6.proto\x12\x11WAWebProtobufsWa6\"\xb0)\n" +
	"\rClientPayload\x12\x1a\n" +
	"\busername\x18\x01 \x01(\x04R\busername\x12\x18\n" +
	"\apassive\x18\x03 \x01(\bR\apassive\x12H\n" +
	"\tuserAgent\x18\x05 \x01(\v2*.WAWebProtobufsWa6.ClientPayload.UserAgentR\tuserAgent\x12B\n" +
	"\awebInfo\x18\x06 \x01(\v2(.WAWebProtobufsWa6.ClientPayload.WebInfoR\awebInfo\x12\x1a\n" +
	"\bpushName\x18\a \x01(\tR\bpushName\x12\x1c\n" +
	"\tsessionID\x18\t \x01(\x0fR\tsessionID\x12\"\n" +
	"\fshortConnect\x18\n" +
	" \x01(\bR\fshortConnect\x12N\n" +
	"\vconnectType\x18\f \x01(\x0e2,.WAWebProtobufsWa6.ClientPayload.ConnectTypeR\vconnectType\x12T\n" +
	"\rconnectReason\x18\r \x01(\x0e2..WAWebProtobufsWa6.ClientPayload.ConnectReasonR\rconnectReason\x12\x16\n" +
	"\x06shards\x18\x0e \x03(\x05R\x06shards\x12H\n" +
	"\tdnsSource\x18\x0f \x01(\v2*.WAWebProtobufsWa6.ClientPayload.DNSSourceR\tdnsSource\x120\n" +
	"\x13connectAttemptCount\x18\x10 \x01(\rR\x13connectAttemptCount\x12\x16\n" +
	"\x06device\x18\x12 \x01(\rR\x06device\x12l\n" +
	"\x11devicePairingData\x18\x13 \x01(\v2>.WAWebProtobufsWa6.ClientPayload.DevicePairingRegistrationDataR\x11devicePairingData\x12B\n" +
	"\aproduct\x18\x14 \x01(\x0e2(.WAWebProtobufsWa6.ClientPayload.ProductR\aproduct\x12\x14\n" +
	"\x05fbCat\x18\x15 \x01(\fR\x05fbCat\x12 \n" +
	"\vfbUserAgent\x18\x16 \x01(\fR\vfbUserAgent\x12\x0e\n" +
	"\x02oc\x18\x17 \x01(\bR\x02oc\x12\x0e\n" +
	"\x02lc\x18\x18 \x01(\x05R\x02lc\x12Z\n" +
	"\x0fiosAppExtension\x18\x1e \x01(\x0e20.WAWebProtobufsWa6.ClientPayload.IOSAppExtensionR\x0fiosAppExtension\x12\x18\n" +
	"\afbAppID\x18\x1f \x01(\x04R\afbAppID\x12\x1e\n" +
	"\n" +
	"fbDeviceID\x18  \x01(\fR\n" +
	"fbDeviceID\x12\x12\n" +
	"\x04pull\x18! \x01(\bR\x04pull\x12\"\n" +
	"\fpaddingBytes\x18\" \x01(\fR\fpaddingBytes\x12\x1c\n" +
	"\tyearClass\x18$ \x01(\x05R\tyearClass\x12\x1a\n" +
	"\bmemClass\x18% \x01(\x05R\bmemClass\x12N\n" +
	"\vinteropData\x18& \x01(\v2,.WAWebProtobufsWa6.ClientPayload.InteropDataR\vinteropData\x12i\n" +
	"\x14trafficAnonymization\x18( \x01(\x0e25.WAWebProtobufsWa6.ClientPayload.TrafficAnonymizationR\x14trafficAnonymization\x12$\n" +
	"\rlidDbMigrated\x18) \x01(\bR\rlidDbMigrated\x12N\n" +
	"\vaccountType\x18* \x01(\x0e2,.WAWebProtobufsWa6.ClientPayload.AccountTypeR\vaccountType\x1a\xea\x01\n" +
	"\tDNSSource\x12\\\n" +
	"\tdnsMethod\x18\x0f \x01(\x0e2>.WAWebProtobufsWa6.ClientPayload.DNSSource.DNSResolutionMethodR\tdnsMethod\x12\x1c\n" +
	"\tappCached\x18\x10 \x01(\bR\tappCached\"a\n" +
	"\x13DNSResolutionMethod\x12\n" +
	"\n" +
	"\x06SYSTEM\x10\x00\x12\n" +
	"\n" +
	"\x06GOOGLE\x10\x01\x12\r\n" +
	"\tHARDCODED\x10\x02\x12\f\n" +
	"\bOVERRIDE\x10\x03\x12\f\n" +
	"\bFALLBACK\x10\x04\x12\a\n" +
	"\x03MNS\x10\x05\x1a\xf4\x06\n" +
	"\aWebInfo\x12\x1a\n" +
	"\brefToken\x18\x01 \x01(\tR\brefToken\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12V\n" +
	"\vwebdPayload\x18\x03 \x01(\v24.WAWebProtobufsWa6.ClientPayload.WebInfo.WebdPayloadR\vwebdPayload\x12_\n" +
	"\x0ewebSubPlatform\x18\x04 \x01(\x0e27.WAWebProtobufsWa6.ClientPayload.WebInfo.WebSubPlatformR\x0ewebSubPlatform\x1a\x91\x04\n" +
	"\vWebdPayload\x122\n" +
	"\x14usesParticipantInKey\x18\x01 \x01(\bR\x14usesParticipantInKey\x128\n" +
	"\x17supportsStarredMessages\x18\x02 \x01(\bR\x17supportsStarredMessages\x12:\n" +
	"\x18supportsDocumentMessages\x18\x03 \x01(\bR\x18supportsDocumentMessages\x120\n" +
	"\x13supportsURLMessages\x18\x04 \x01(\bR\x13supportsURLMessages\x12.\n" +
	"\x12supportsMediaRetry\x18\x05 \x01(\bR\x12supportsMediaRetry\x12*\n" +
	"\x10supportsE2EImage\x18\x06 \x01(\bR\x10supportsE2EImage\x12*\n" +
	"\x10supportsE2EVideo\x18\a \x01(\bR\x10supportsE2EVideo\x12*\n" +
	"\x10supportsE2EAudio\x18\b \x01(\bR\x10supportsE2EAudio\x120\n" +
	"\x13supportsE2EDocument\x18\t \x01(\bR\x13supportsE2EDocument\x12$\n" +
	"\rdocumentTypes\x18\n" +
	" \x01(\tR\rdocumentTypes\x12\x1a\n" +
	"\bfeatures\x18\v \x01(\fR\bfeatures\"f\n" +
	"\x0eWebSubPlatform\x12\x0f\n" +
	"\vWEB_BROWSER\x10\x00\x12\r\n" +
	"\tAPP_STORE\x10\x01\x12\r\n" +
	"\tWIN_STORE\x10\x02\x12\n" +
	"\n" +
	"\x06DARWIN\x10\x03\x12\t\n" +
	"\x05WIN32\x10\x04\x12\x0e\n" +
	"\n" +
	"WIN_HYBRID\x10\x05\x1a\xcf\f\n" +
	"\tUserAgent\x12O\n" +
	"\bplatform\x18\x01 \x01(\x0e23.WAWebProtobufsWa6.ClientPayload.UserAgent.PlatformR\bplatform\x12U\n" +
	"\n" +
	"appVersion\x18\x02 \x01(\v25.WAWebProtobufsWa6.ClientPayload.UserAgent.AppVersionR\n" +
	"appVersion\x12\x10\n" +
	"\x03mcc\x18\x03 \x01(\tR\x03mcc\x12\x10\n" +
	"\x03mnc\x18\x04 \x01(\tR\x03mnc\x12\x1c\n" +
	"\tosVersion\x18\x05 \x01(\tR\tosVersion\x12\"\n" +
	"\fmanufacturer\x18\x06 \x01(\tR\fmanufacturer\x12\x16\n" +
	"\x06device\x18\a \x01(\tR\x06device\x12$\n" +
	"\rosBuildNumber\x18\b \x01(\tR\rosBuildNumber\x12\x18\n" +
	"\aphoneID\x18\t \x01(\tR\aphoneID\x12a\n" +
	"\x0ereleaseChannel\x18\n" +
	" \x01(\x0e29.WAWebProtobufsWa6.ClientPayload.UserAgent.ReleaseChannelR\x0ereleaseChannel\x124\n" +
	"\x15localeLanguageIso6391\x18\v \x01(\tR\x15localeLanguageIso6391\x12@\n" +
	"\x1blocaleCountryIso31661Alpha2\x18\f \x01(\tR\x1blocaleCountryIso31661Alpha2\x12 \n" +
	"\vdeviceBoard\x18\r \x01(\tR\vdeviceBoard\x12 \n" +
	"\vdeviceExpID\x18\x0e \x01(\tR\vdeviceExpID\x12U\n" +
	"\n" +
	"deviceType\x18\x0f \x01(\x0e25.WAWebProtobufsWa6.ClientPayload.UserAgent.DeviceTypeR\n" +
	"deviceType\x12(\n" +
	"\x0fdeviceModelType\x18\x10 \x01(\tR\x0fdeviceModelType\x1a\x9a\x01\n" +
	"\n" +
	"AppVersion\x12\x18\n" +
	"\aprimary\x18\x01 \x01(\rR\aprimary\x12\x1c\n" +
	"\tsecondary\x18\x02 \x01(\rR\tsecondary\x12\x1a\n" +
	"\btertiary\x18\x03 \x01(\rR\btertiary\x12\x1e\n" +
	"\n" +
	"quaternary\x18\x04 \x01(\rR\n" +
	"quaternary\x12\x18\n" +
	"\aquinary\x18\x05 \x01(\rR\aquinary\"F\n" +
	"\n" +
	"DeviceType\x12\t\n" +
	"\x05PHONE\x10\x00\x12\n" +
	"\n" +
	"\x06TABLET\x10\x01\x12\v\n" +
	"\aDESKTOP\x10\x02\x12\f\n" +
	"\bWEARABLE\x10\x03\x12\x06\n" +
	"\x02VR\x10\x04\"=\n" +
	"\x0eReleaseChannel\x12\v\n" +
	"\aRELEASE\x10\x00\x12\b\n" +
	"\x04BETA\x10\x01\x12\t\n" +
	"\x05ALPHA\x10\x02\x12\t\n" +
	"\x05DEBUG\x10\x03\"\x97\x04\n" +
	"\bPlatform\x12\v\n" +
	"\aANDROID\x10\x00\x12\a\n" +
	"\x03IOS\x10\x01\x12\x11\n" +
	"\rWINDOWS_PHONE\x10\x02\x12\x0e\n" +
	"\n" +
	"BLACKBERRY\x10\x03\x12\x0f\n" +
	"\vBLACKBERRYX\x10\x04\x12\a\n" +
	"\x03S40\x10\x05\x12\a\n" +
	"\x03S60\x10\x06\x12\x11\n" +
	"\rPYTHON_CLIENT\x10\a\x12\t\n" +
	"\x05TIZEN\x10\b\x12\x0e\n" +
	"\n" +
	"ENTERPRISE\x10\t\x12\x0f\n" +
	"\vSMB_ANDROID\x10\n" +
	"\x12\t\n" +
	"\x05KAIOS\x10\v\x12\v\n" +
	"\aSMB_IOS\x10\f\x12\v\n" +
	"\aWINDOWS\x10\r\x12\a\n" +
	"\x03WEB\x10\x0e\x12\n" +
	"\n" +
	"\x06PORTAL\x10\x0f\x12\x11\n" +
	"\rGREEN_ANDROID\x10\x10\x12\x10\n" +
	"\fGREEN_IPHONE\x10\x11\x12\x10\n" +
	"\fBLUE_ANDROID\x10\x12\x12\x0f\n" +
	"\vBLUE_IPHONE\x10\x13\x12\x12\n" +
	"\x0eFBLITE_ANDROID\x10\x14\x12\x11\n" +
	"\rMLITE_ANDROID\x10\x15\x12\x12\n" +
	"\x0eIGLITE_ANDROID\x10\x16\x12\b\n" +
	"\x04PAGE\x10\x17\x12\t\n" +
	"\x05MACOS\x10\x18\x12\x0e\n" +
	"\n" +
	"OCULUS_MSG\x10\x19\x12\x0f\n" +
	"\vOCULUS_CALL\x10\x1a\x12\t\n" +
	"\x05MILAN\x10\x1b\x12\b\n" +
	"\x04CAPI\x10\x1c\x12\n" +
	"\n" +
	"\x06WEAROS\x10\x1d\x12\f\n" +
	"\bARDEVICE\x10\x1e\x12\f\n" +
	"\bVRDEVICE\x10\x1f\x12\f\n" +
	"\bBLUE_WEB\x10 \x12\b\n" +
	"\x04IPAD\x10!\x12\b\n" +
	"\x04TEST\x10\"\x12\x11\n" +
	"\rSMART_GLASSES\x10#\x12\v\n" +
	"\aBLUE_VR\x10$\x1aq\n" +
	"\vInteropData\x12\x1c\n" +
	"\taccountID\x18\x01 \x01(\x04R\taccountID\x12\x14\n" +
	"\x05token\x18\x02 \x01(\fR\x05token\x12.\n" +
	"\x12enableReadReceipts\x18\x03 \x01(\bR\x12enableReadReceipts\x1a\xfd\x01\n" +
	"\x1dDevicePairingRegistrationData\x12\x16\n" +
	"\x06eRegid\x18\x01 \x01(\fR\x06eRegid\x12\x1a\n" +
	"\beKeytype\x18\x02 \x01(\fR\beKeytype\x12\x16\n" +
	"\x06eIdent\x18\x03 \x01(\fR\x06eIdent\x12\x18\n" +
	"\aeSkeyID\x18\x04 \x01(\fR\aeSkeyID\x12\x1a\n" +
	"\beSkeyVal\x18\x05 \x01(\fR\beSkeyVal\x12\x1a\n" +
	"\beSkeySig\x18\x06 \x01(\fR\beSkeySig\x12\x1c\n" +
	"\tbuildHash\x18\a \x01(\fR\tbuildHash\x12 \n" +
	"\vdeviceProps\x18\b \x01(\fR\vdeviceProps\"-\n" +
	"\x14TrafficAnonymization\x12\a\n" +
	"\x03OFF\x10\x00\x12\f\n" +
	"\bSTANDARD\x10\x01\"%\n" +
	"\vAccountType\x12\v\n" +
	"\aDEFAULT\x10\x00\x12\t\n" +
	"\x05GUEST\x10\x01\"W\n" +
	"\aProduct\x12\f\n" +
	"\bWHATSAPP\x10\x00\x12\r\n" +
	"\tMESSENGER\x10\x01\x12\v\n" +
	"\aINTEROP\x10\x02\x12\x10\n" +
	"\fINTEROP_MSGR\x10\x03\x12\x10\n" +
	"\fWHATSAPP_LID\x10\x04\"\xb0\x02\n" +
	"\vConnectType\x12\x14\n" +
	"\x10CELLULAR_UNKNOWN\x10\x00\x12\x10\n" +
	"\fWIFI_UNKNOWN\x10\x01\x12\x11\n" +
	"\rCELLULAR_EDGE\x10d\x12\x11\n" +
	"\rCELLULAR_IDEN\x10e\x12\x11\n" +
	"\rCELLULAR_UMTS\x10f\x12\x11\n" +
	"\rCELLULAR_EVDO\x10g\x12\x11\n" +
	"\rCELLULAR_GPRS\x10h\x12\x12\n" +
	"\x0eCELLULAR_HSDPA\x10i\x12\x12\n" +
	"\x0eCELLULAR_HSUPA\x10j\x12\x11\n" +
	"\rCELLULAR_HSPA\x10k\x12\x11\n" +
	"\rCELLULAR_CDMA\x10l\x12\x12\n" +
	"\x0eCELLULAR_1XRTT\x10m\x12\x12\n" +
	"\x0eCELLULAR_EHRPD\x10n\x12\x10\n" +
	"\fCELLULAR_LTE\x10o\x12\x12\n" +
	"\x0eCELLULAR_HSPAP\x10p\"\x86\x01\n" +
	"\rConnectReason\x12\b\n" +
	"\x04PUSH\x10\x00\x12\x12\n" +
	"\x0eUSER_ACTIVATED\x10\x01\x12\r\n" +
	"\tSCHEDULED\x10\x02\x12\x13\n" +
	"\x0fERROR_RECONNECT\x10\x03\x12\x12\n" +
	"\x0eNETWORK_SWITCH\x10\x04\x12\x12\n" +
	"\x0ePING_RECONNECT\x10\x05\x12\v\n" +
	"\aUNKNOWN\x10\x06\"T\n" +
	"\x0fIOSAppExtension\x12\x13\n" +
	"\x0fSHARE_EXTENSION\x10\x00\x12\x15\n" +
	"\x11SERVICE_EXTENSION\x10\x01\x12\x15\n" +
	"\x11INTENTS_EXTENSION\x10\x02\"\x8e\x04\n" +
	"\x10HandshakeMessage\x12Q\n" +
	"\vclientHello\x18\x02 \x01(\v2/.WAWebProtobufsWa6.HandshakeMessage.ClientHelloR\vclientHello\x12Q\n" +
	"\vserverHello\x18\x03 \x01(\v2/.WAWebProtobufsWa6.HandshakeMessage.ServerHelloR\vserverHello\x12T\n" +
	"\fclientFinish\x18\x04 \x01(\v20.WAWebProtobufsWa6.HandshakeMessage.ClientFinishR\fclientFinish\x1a@\n" +
	"\fClientFinish\x12\x16\n" +
	"\x06static\x18\x01 \x01(\fR\x06static\x12\x18\n" +
	"\apayload\x18\x02 \x01(\fR\apayload\x1a]\n" +
	"\vServerHello\x12\x1c\n" +
	"\tephemeral\x18\x01 \x01(\fR\tephemeral\x12\x16\n" +
	"\x06static\x18\x02 \x01(\fR\x06static\x12\x18\n" +
	"\apayload\x18\x03 \x01(\fR\apayload\x1a]\n" +
	"\vClientHello\x12\x1c\n" +
	"\tephemeral\x18\x01 \x01(\fR\tephemeral\x12\x16\n" +
	"\x06static\x18\x02 \x01(\fR\x06static\x12\x18\n" +
	"\apayload\x18\x03 \x01(\fR\apayloadB!Z\x1fgo.mau.fi/whatsmeow/proto/waWa6"

var (
	file_waWa6_WAWebProtobufsWa6_proto_rawDescOnce sync.Once
	file_waWa6_WAWebProtobufsWa6_proto_rawDescData []byte
)

func file_waWa6_WAWebProtobufsWa6_proto_rawDescGZIP() []byte {
	file_waWa6_WAWebProtobufsWa6_proto_rawDescOnce.Do(func() {
		file_waWa6_WAWebProtobufsWa6_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waWa6_WAWebProtobufsWa6_proto_rawDesc), len(file_waWa6_WAWebProtobufsWa6_proto_rawDesc)))
	})
	return file_waWa6_WAWebProtobufsWa6_proto_rawDescData
}

var file_waWa6_WAWebProtobufsWa6_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_waWa6_WAWebProtobufsWa6_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_waWa6_WAWebProtobufsWa6_proto_goTypes = []any{
	(ClientPayload_TrafficAnonymization)(0),             // 0: WAWebProtobufsWa6.ClientPayload.TrafficAnonymization
	(ClientPayload_AccountType)(0),                      // 1: WAWebProtobufsWa6.ClientPayload.AccountType
	(ClientPayload_Product)(0),                          // 2: WAWebProtobufsWa6.ClientPayload.Product
	(ClientPayload_ConnectType)(0),                      // 3: WAWebProtobufsWa6.ClientPayload.ConnectType
	(ClientPayload_ConnectReason)(0),                    // 4: WAWebProtobufsWa6.ClientPayload.ConnectReason
	(ClientPayload_IOSAppExtension)(0),                  // 5: WAWebProtobufsWa6.ClientPayload.IOSAppExtension
	(ClientPayload_DNSSource_DNSResolutionMethod)(0),    // 6: WAWebProtobufsWa6.ClientPayload.DNSSource.DNSResolutionMethod
	(ClientPayload_WebInfo_WebSubPlatform)(0),           // 7: WAWebProtobufsWa6.ClientPayload.WebInfo.WebSubPlatform
	(ClientPayload_UserAgent_DeviceType)(0),             // 8: WAWebProtobufsWa6.ClientPayload.UserAgent.DeviceType
	(ClientPayload_UserAgent_ReleaseChannel)(0),         // 9: WAWebProtobufsWa6.ClientPayload.UserAgent.ReleaseChannel
	(ClientPayload_UserAgent_Platform)(0),               // 10: WAWebProtobufsWa6.ClientPayload.UserAgent.Platform
	(*ClientPayload)(nil),                               // 11: WAWebProtobufsWa6.ClientPayload
	(*HandshakeMessage)(nil),                            // 12: WAWebProtobufsWa6.HandshakeMessage
	(*ClientPayload_DNSSource)(nil),                     // 13: WAWebProtobufsWa6.ClientPayload.DNSSource
	(*ClientPayload_WebInfo)(nil),                       // 14: WAWebProtobufsWa6.ClientPayload.WebInfo
	(*ClientPayload_UserAgent)(nil),                     // 15: WAWebProtobufsWa6.ClientPayload.UserAgent
	(*ClientPayload_InteropData)(nil),                   // 16: WAWebProtobufsWa6.ClientPayload.InteropData
	(*ClientPayload_DevicePairingRegistrationData)(nil), // 17: WAWebProtobufsWa6.ClientPayload.DevicePairingRegistrationData
	(*ClientPayload_WebInfo_WebdPayload)(nil),           // 18: WAWebProtobufsWa6.ClientPayload.WebInfo.WebdPayload
	(*ClientPayload_UserAgent_AppVersion)(nil),          // 19: WAWebProtobufsWa6.ClientPayload.UserAgent.AppVersion
	(*HandshakeMessage_ClientFinish)(nil),               // 20: WAWebProtobufsWa6.HandshakeMessage.ClientFinish
	(*HandshakeMessage_ServerHello)(nil),                // 21: WAWebProtobufsWa6.HandshakeMessage.ServerHello
	(*HandshakeMessage_ClientHello)(nil),                // 22: WAWebProtobufsWa6.HandshakeMessage.ClientHello
}
var file_waWa6_WAWebProtobufsWa6_proto_depIdxs = []int32{
	15, // 0: WAWebProtobufsWa6.ClientPayload.userAgent:type_name -> WAWebProtobufsWa6.ClientPayload.UserAgent
	14, // 1: WAWebProtobufsWa6.ClientPayload.webInfo:type_name -> WAWebProtobufsWa6.ClientPayload.WebInfo
	3,  // 2: WAWebProtobufsWa6.ClientPayload.connectType:type_name -> WAWebProtobufsWa6.ClientPayload.ConnectType
	4,  // 3: WAWebProtobufsWa6.ClientPayload.connectReason:type_name -> WAWebProtobufsWa6.ClientPayload.ConnectReason
	13, // 4: WAWebProtobufsWa6.ClientPayload.dnsSource:type_name -> WAWebProtobufsWa6.ClientPayload.DNSSource
	17, // 5: WAWebProtobufsWa6.ClientPayload.devicePairingData:type_name -> WAWebProtobufsWa6.ClientPayload.DevicePairingRegistrationData
	2,  // 6: WAWebProtobufsWa6.ClientPayload.product:type_name -> WAWebProtobufsWa6.ClientPayload.Product
	5,  // 7: WAWebProtobufsWa6.ClientPayload.iosAppExtension:type_name -> WAWebProtobufsWa6.ClientPayload.IOSAppExtension
	16, // 8: WAWebProtobufsWa6.ClientPayload.interopData:type_name -> WAWebProtobufsWa6.ClientPayload.InteropData
	0,  // 9: WAWebProtobufsWa6.ClientPayload.trafficAnonymization:type_name -> WAWebProtobufsWa6.ClientPayload.TrafficAnonymization
	1,  // 10: WAWebProtobufsWa6.ClientPayload.accountType:type_name -> WAWebProtobufsWa6.ClientPayload.AccountType
	22, // 11: WAWebProtobufsWa6.HandshakeMessage.clientHello:type_name -> WAWebProtobufsWa6.HandshakeMessage.ClientHello
	21, // 12: WAWebProtobufsWa6.HandshakeMessage.serverHello:type_name -> WAWebProtobufsWa6.HandshakeMessage.ServerHello
	20, // 13: WAWebProtobufsWa6.HandshakeMessage.clientFinish:type_name -> WAWebProtobufsWa6.HandshakeMessage.ClientFinish
	6,  // 14: WAWebProtobufsWa6.ClientPayload.DNSSource.dnsMethod:type_name -> WAWebProtobufsWa6.ClientPayload.DNSSource.DNSResolutionMethod
	18, // 15: WAWebProtobufsWa6.ClientPayload.WebInfo.webdPayload:type_name -> WAWebProtobufsWa6.ClientPayload.WebInfo.WebdPayload
	7,  // 16: WAWebProtobufsWa6.ClientPayload.WebInfo.webSubPlatform:type_name -> WAWebProtobufsWa6.ClientPayload.WebInfo.WebSubPlatform
	10, // 17: WAWebProtobufsWa6.ClientPayload.UserAgent.platform:type_name -> WAWebProtobufsWa6.ClientPayload.UserAgent.Platform
	19, // 18: WAWebProtobufsWa6.ClientPayload.UserAgent.appVersion:type_name -> WAWebProtobufsWa6.ClientPayload.UserAgent.AppVersion
	9,  // 19: WAWebProtobufsWa6.ClientPayload.UserAgent.releaseChannel:type_name -> WAWebProtobufsWa6.ClientPayload.UserAgent.ReleaseChannel
	8,  // 20: WAWebProtobufsWa6.ClientPayload.UserAgent.deviceType:type_name -> WAWebProtobufsWa6.ClientPayload.UserAgent.DeviceType
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_waWa6_WAWebProtobufsWa6_proto_init() }
func file_waWa6_WAWebProtobufsWa6_proto_init() {
	if File_waWa6_WAWebProtobufsWa6_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waWa6_WAWebProtobufsWa6_proto_rawDesc), len(file_waWa6_WAWebProtobufsWa6_proto_rawDesc)),
			NumEnums:      11,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waWa6_WAWebProtobufsWa6_proto_goTypes,
		DependencyIndexes: file_waWa6_WAWebProtobufsWa6_proto_depIdxs,
		EnumInfos:         file_waWa6_WAWebProtobufsWa6_proto_enumTypes,
		MessageInfos:      file_waWa6_WAWebProtobufsWa6_proto_msgTypes,
	}.Build()
	File_waWa6_WAWebProtobufsWa6_proto = out.File
	file_waWa6_WAWebProtobufsWa6_proto_goTypes = nil
	file_waWa6_WAWebProtobufsWa6_proto_depIdxs = nil
}
