//! Binary node parser

use crate::error::BinaryError;
use crate::node::Node;
use bytes::Bytes;

/// Binary node parser
pub struct Parser {
    data: Bytes,
    position: usize,
}

impl Parser {
    /// Create a new parser with data
    pub fn new(data: Bytes) -> Self {
        Self { data, position: 0 }
    }

    /// Parse a node from the current position
    pub fn parse_node(&mut self) -> Result<Node, BinaryError> {
        // TODO: Implement binary node parsing logic
        Err(BinaryError::DeserializationFailed(
            "Parser not implemented".to_string(),
        ))
    }

    /// Check if there's more data to parse
    pub fn has_more(&self) -> bool {
        self.position < self.data.len()
    }

    /// Get the current position
    pub fn position(&self) -> usize {
        self.position
    }

    /// Read a byte from the current position
    fn read_byte(&mut self) -> Result<u8, BinaryError> {
        if self.position >= self.data.len() {
            return Err(BinaryError::UnexpectedEndOfData);
        }

        let byte = self.data[self.position];
        self.position += 1;
        Ok(byte)
    }

    /// Read multiple bytes from the current position
    fn read_bytes(&mut self, count: usize) -> Result<Bytes, BinaryError> {
        if self.position + count > self.data.len() {
            return Err(BinaryError::UnexpectedEndOfData);
        }

        let bytes = self.data.slice(self.position..self.position + count);
        self.position += count;
        Ok(bytes)
    }
}
