//! JID (Jabber ID) implementation for WhatsApp
//!
//! This module provides a comprehensive implementation of WhatsApp's JID system,
//! which is based on the XMPP Jabber ID format but with WhatsApp-specific extensions.
//!
//! # Examples
//!
//! ```rust
//! use whatsmeow_types::jid::Jid;
//!
//! // Parse a user JID
//! let user_jid = Jid::from_string("<EMAIL>").unwrap();
//! assert!(user_jid.is_user());
//!
//! // Create a group JID using factory method
//! let group_jid = Jid::group("group123").unwrap();
//! assert!(group_jid.is_group());
//!
//! // Use builder pattern
//! let jid = Jid::builder()
//!     .user("1234567890")
//!     .server("s.whatsapp.net")
//!     .device(1)
//!     .build()
//!     .unwrap();
//! ```

use serde::{Deserialize, Serialize};
use std::fmt;
use std::str::FromStr;
use thiserror::Error;

// WhatsApp server constants
const GROUP_SERVER: &str = "g.us";
const BROADCAST_SERVER: &str = "broadcast";
const USER_SERVER_NEW: &str = "s.whatsapp.net";
const USER_SERVER_OLD: &str = "c.us";
const WHATSAPP_SERVER: &str = "server.whatsapp.net";
const NEWSLETTER_SERVER: &str = "newsletter";
const STATUS_USER: &str = "status";

/// WhatsApp JID (Jabber ID) representation
#[derive(Debug, Clone, PartialEq, Eq, Hash, PartialOrd, Ord, Serialize, Deserialize)]
pub struct Jid {
    pub user: String,
    pub server: String,
    pub device: u8,
}

#[derive(Debug, Error)]
pub enum JidError {
    #[error("Invalid JID format: {0}")]
    InvalidFormat(String),

    #[error("Invalid device ID: {0}")]
    InvalidDevice(u8),

    #[error("Empty JID string")]
    EmptyString,

    #[error("Invalid user part: {0}")]
    InvalidUser(String),

    #[error("Invalid server part: {0}")]
    InvalidServer(String),
}

/// Internal struct for parsed JID components
struct ParsedJidComponents {
    user: String,
    server: String,
    device: u8,
}

impl Jid {
    /// Create a new JID with validation
    pub fn new(user: String, server: String, device: u8) -> Result<Self, JidError> {
        Self::validate_user(&user)?;
        Self::validate_server(&server)?;

        Ok(Self {
            user,
            server,
            device,
        })
    }

    /// Create a new JID without validation (for internal use)
    pub fn new_unchecked(user: String, server: String, device: u8) -> Self {
        Self {
            user,
            server,
            device,
        }
    }

    /// Parse a JID from string format with comprehensive validation
    pub fn from_string(s: &str) -> Result<Self, JidError> {
        let normalized = Self::normalize_input(s)?;
        let parsed_parts = Self::parse_jid_components(normalized)?;
        Self::new(parsed_parts.user, parsed_parts.server, parsed_parts.device)
    }

    /// Parse JID components from normalized string
    fn parse_jid_components(s: &str) -> Result<ParsedJidComponents, JidError> {
        let (user, server_part) = Self::split_jid_parts(s)?;
        let (server, device) = Self::parse_server_and_device(server_part)?;

        Ok(ParsedJidComponents {
            user: user.to_string(),
            server: server.to_string(),
            device,
        })
    }

    /// Normalize and validate input string
    fn normalize_input(s: &str) -> Result<&str, JidError> {
        if s.is_empty() {
            return Err(JidError::EmptyString);
        }

        let s = s.trim();
        if s.is_empty() {
            return Err(JidError::EmptyString);
        }

        Ok(s)
    }

    /// Split JID into user and server parts
    fn split_jid_parts(s: &str) -> Result<(&str, &str), JidError> {
        let (user, server_part) = s
            .split_once('@')
            .ok_or_else(|| JidError::InvalidFormat(s.to_string()))?;

        if user.is_empty() {
            return Err(JidError::InvalidUser(
                "User part cannot be empty".to_string(),
            ));
        }

        if server_part.is_empty() {
            return Err(JidError::InvalidServer(
                "Server part cannot be empty".to_string(),
            ));
        }

        // Check for additional @ symbols in server part
        if server_part.contains('@') {
            return Err(JidError::InvalidFormat(format!(
                "Multiple @ symbols found: {}",
                s
            )));
        }

        Ok((user, server_part))
    }

    /// Parse server part and extract device number if present
    fn parse_server_and_device(server_part: &str) -> Result<(&str, u8), JidError> {
        // Find the last dot to check for device number
        if let Some(dot_pos) = server_part.rfind('.') {
            let server = &server_part[..dot_pos];
            let device_str = &server_part[dot_pos + 1..];

            // Only parse as device if it's a valid number and both parts are non-empty
            if !server.is_empty() && !device_str.is_empty() {
                // Fast path: check if all characters are ASCII digits before parsing
                if device_str.len() <= 3 && device_str.bytes().all(|b| b.is_ascii_digit()) {
                    // Use faster parsing for small numbers
                    if let Ok(device) = device_str.parse::<u8>() {
                        return Ok((server, device));
                    }
                }
            }
        }

        Ok((server_part, 0))
    }

    /// Validate user part of JID
    fn validate_user(user: &str) -> Result<(), JidError> {
        if user.is_empty() {
            return Err(JidError::InvalidUser("User cannot be empty".to_string()));
        }

        // Check for invalid characters in user part
        if let Some(invalid_char) = user.chars().find(|&c| c == '@' || c == '/' || c == '\0') {
            return Err(JidError::InvalidUser(format!(
                "User contains invalid character '{}': {}",
                invalid_char, user
            )));
        }

        // Additional validation for excessively long user parts
        if user.len() > 64 {
            return Err(JidError::InvalidUser(format!(
                "User part too long (max 64 chars, got {}): {}",
                user.len(),
                user
            )));
        }

        Ok(())
    }

    /// Validate phone number format for WhatsApp users
    fn validate_phone_number(phone: &str) -> Result<(), JidError> {
        // Remove + prefix if present
        let phone = phone.strip_prefix('+').unwrap_or(phone);

        // Check length (typical phone numbers are 7-15 digits)
        if phone.len() < 7 || phone.len() > 15 {
            return Err(JidError::InvalidUser(format!(
                "Phone number length invalid (7-15 digits expected, got {}): {}",
                phone.len(),
                phone
            )));
        }

        // Check that all characters are digits
        if !phone.chars().all(|c| c.is_ascii_digit()) {
            return Err(JidError::InvalidUser(
                "Phone number must contain only digits".to_string(),
            ));
        }

        // Basic country code validation (1-4 digits)
        let first_digit = phone.chars().next().unwrap();
        if first_digit == '0' {
            return Err(JidError::InvalidUser(
                "Phone number cannot start with 0".to_string(),
            ));
        }

        Ok(())
    }

    /// Validate server part of JID
    fn validate_server(server: &str) -> Result<(), JidError> {
        if server.is_empty() {
            return Err(JidError::InvalidServer(
                "Server cannot be empty".to_string(),
            ));
        }

        // Check for invalid characters in server part
        if server.contains('@') || server.contains('/') {
            return Err(JidError::InvalidServer(format!(
                "Server contains invalid characters: {}",
                server
            )));
        }

        Ok(())
    }

    /// Check if this is a group JID
    pub fn is_group(&self) -> bool {
        self.server == GROUP_SERVER
    }

    /// Check if this is a broadcast JID
    pub fn is_broadcast(&self) -> bool {
        self.server == BROADCAST_SERVER
    }

    /// Check if this is a status broadcast JID
    pub fn is_status_broadcast(&self) -> bool {
        self.user == STATUS_USER && self.server == BROADCAST_SERVER
    }

    /// Check if this is a user JID (individual chat)
    pub fn is_user(&self) -> bool {
        self.server == USER_SERVER_NEW || self.server == USER_SERVER_OLD
    }

    /// Check if this is a server JID (WhatsApp server)
    pub fn is_server(&self) -> bool {
        self.server == WHATSAPP_SERVER
    }

    /// Check if this is a newsletter JID
    pub fn is_newsletter(&self) -> bool {
        self.server == NEWSLETTER_SERVER
    }

    /// Get the bare JID (without device)
    pub fn to_bare(&self) -> Self {
        Self {
            user: self.user.clone(),
            server: self.server.clone(),
            device: 0,
        }
    }

    /// Get the user part as a phone number (if applicable)
    pub fn to_phone_number(&self) -> Option<String> {
        if self.is_user() && self.user.chars().all(|c| c.is_ascii_digit()) {
            Some(format!("+{}", self.user))
        } else {
            None
        }
    }

    /// Create a JID with a specific device
    pub fn with_device(&self, device: u8) -> Self {
        Self {
            user: self.user.clone(),
            server: self.server.clone(),
            device,
        }
    }

    /// Check if this JID has a device specified
    pub fn has_device(&self) -> bool {
        self.device != 0
    }

    /// Get the JID type as a string for debugging/logging
    pub fn jid_type(&self) -> &'static str {
        if self.is_user() {
            "user"
        } else if self.is_group() {
            "group"
        } else if self.is_status_broadcast() {
            "status_broadcast"
        } else if self.is_broadcast() {
            "broadcast"
        } else if self.is_newsletter() {
            "newsletter"
        } else if self.is_server() {
            "server"
        } else {
            "unknown"
        }
    }

    /// Check if this JID is equal to another JID ignoring device
    pub fn equals_bare(&self, other: &Self) -> bool {
        self.user == other.user && self.server == other.server
    }

    /// Normalize JID for consistent comparison and storage
    pub fn normalize(&self) -> Self {
        let normalized_user = if self.is_user() {
            // For user JIDs, ensure phone number format
            if self.user.starts_with('+') {
                self.user[1..].to_string()
            } else {
                self.user.clone()
            }
        } else {
            // For non-user JIDs, convert to lowercase
            self.user.to_lowercase()
        };

        let normalized_server = match self.server.as_str() {
            // Normalize old user server to new format
            USER_SERVER_OLD => USER_SERVER_NEW.to_string(),
            // Keep other servers as-is but lowercase
            _ => self.server.to_lowercase(),
        };

        Self {
            user: normalized_user,
            server: normalized_server,
            device: self.device,
        }
    }

    /// Create a normalized version of this JID for comparison
    pub fn to_normalized_string(&self) -> String {
        self.normalize().to_string()
    }

    /// Async-friendly validation method for future I/O operations
    /// Currently just wraps the sync version, but can be extended for database lookups, etc.
    pub async fn validate_async(&self) -> Result<(), JidError> {
        Self::validate_user(&self.user)?;
        Self::validate_server(&self.server)?;
        Ok(())
    }

    /// Check if this JID represents a valid WhatsApp contact
    /// This is a placeholder for future contact validation logic
    pub fn is_valid_contact(&self) -> bool {
        self.is_user() && self.user.len() >= 7 && self.user.len() <= 15
    }
}

/// Builder for creating JIDs with a fluent API
#[derive(Debug, Default)]
pub struct JidBuilder {
    user: Option<String>,
    server: Option<String>,
    device: u8,
}

impl JidBuilder {
    /// Create a new JID builder
    pub fn new() -> Self {
        Self::default()
    }

    /// Set the user part
    pub fn user<S: Into<String>>(mut self, user: S) -> Self {
        self.user = Some(user.into());
        self
    }

    /// Set the server part
    pub fn server<S: Into<String>>(mut self, server: S) -> Self {
        self.server = Some(server.into());
        self
    }

    /// Set the device number
    pub fn device(mut self, device: u8) -> Self {
        self.device = device;
        self
    }

    /// Build the JID with validation
    pub fn build(self) -> Result<Jid, JidError> {
        let user = self
            .user
            .ok_or_else(|| JidError::InvalidUser("User is required".to_string()))?;
        let server = self
            .server
            .ok_or_else(|| JidError::InvalidServer("Server is required".to_string()))?;

        Jid::new(user, server, self.device)
    }
}

impl Jid {
    /// Create a builder for this JID type
    pub fn builder() -> JidBuilder {
        JidBuilder::new()
    }

    /// Factory method for creating user JIDs
    pub fn user(phone_number: &str) -> Result<Self, JidError> {
        Self::validate_phone_number(phone_number)?;

        let user = phone_number.strip_prefix('+').unwrap_or(phone_number);

        Self::new(user.to_string(), USER_SERVER_NEW.to_string(), 0)
    }

    /// Factory method for creating group JIDs
    pub fn group(group_id: &str) -> Result<Self, JidError> {
        Self::new(group_id.to_string(), GROUP_SERVER.to_string(), 0)
    }

    /// Factory method for creating broadcast JIDs
    pub fn broadcast(broadcast_id: &str) -> Result<Self, JidError> {
        Self::new(broadcast_id.to_string(), BROADCAST_SERVER.to_string(), 0)
    }

    /// Factory method for creating the status broadcast JID
    pub fn status_broadcast() -> Self {
        Self::new_unchecked(STATUS_USER.to_string(), BROADCAST_SERVER.to_string(), 0)
    }
}

impl fmt::Display for Jid {
    /// Convert JID to string representation
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        if self.device == 0 {
            write!(f, "{}@{}", self.user, self.server)
        } else {
            write!(f, "{}@{}.{}", self.user, self.server, self.device)
        }
    }
}

impl FromStr for Jid {
    type Err = JidError;

    /// Parse a JID from a string using the standard FromStr trait
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        Self::from_string(s)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_jid_parsing_basic() {
        let jid = Jid::from_string("<EMAIL>").unwrap();
        assert_eq!(jid.user, "1234567890");
        assert_eq!(jid.server, "s.whatsapp.net");
        assert_eq!(jid.device, 0);
        assert!(jid.is_user());
        assert!(!jid.is_group());
    }

    #[test]
    fn test_jid_with_device() {
        let jid = Jid::from_string("<EMAIL>.1").unwrap();
        assert_eq!(jid.user, "1234567890");
        assert_eq!(jid.server, "s.whatsapp.net");
        assert_eq!(jid.device, 1);
        assert!(jid.has_device());
    }

    #[test]
    fn test_group_jid() {
        let jid = Jid::from_string("<EMAIL>").unwrap();
        assert!(jid.is_group());
        assert!(!jid.is_broadcast());
        assert!(!jid.is_user());
        assert!(!jid.is_status_broadcast());
    }

    #[test]
    fn test_broadcast_jid() {
        let jid = Jid::from_string("12345@broadcast").unwrap();
        assert!(jid.is_broadcast());
        assert!(!jid.is_group());
        assert!(!jid.is_status_broadcast());
    }

    #[test]
    fn test_status_broadcast_jid() {
        let jid = Jid::from_string("status@broadcast").unwrap();
        assert!(jid.is_status_broadcast());
        assert!(jid.is_broadcast());
        assert!(!jid.is_group());
    }

    #[test]
    fn test_newsletter_jid() {
        let jid = Jid::from_string("newsletter123@newsletter").unwrap();
        assert!(jid.is_newsletter());
        assert!(!jid.is_group());
        assert!(!jid.is_user());
    }

    #[test]
    fn test_server_jid() {
        let jid = Jid::from_string("<EMAIL>").unwrap();
        assert!(jid.is_server());
        assert!(!jid.is_user());
        assert!(!jid.is_group());
    }

    #[test]
    fn test_jid_to_string() {
        let jid = Jid::new_unchecked("1234567890".to_string(), "s.whatsapp.net".to_string(), 0);
        assert_eq!(jid.to_string(), "<EMAIL>");

        let jid_with_device =
            Jid::new_unchecked("1234567890".to_string(), "s.whatsapp.net".to_string(), 1);
        assert_eq!(jid_with_device.to_string(), "<EMAIL>.1");
    }

    #[test]
    fn test_jid_validation_success() {
        let jid = Jid::new("1234567890".to_string(), "s.whatsapp.net".to_string(), 0);
        assert!(jid.is_ok());
    }

    #[test]
    fn test_jid_validation_empty_user() {
        let result = Jid::new("".to_string(), "s.whatsapp.net".to_string(), 0);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), JidError::InvalidUser(_)));
    }

    #[test]
    fn test_jid_validation_empty_server() {
        let result = Jid::new("user".to_string(), "".to_string(), 0);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), JidError::InvalidServer(_)));
    }

    #[test]
    fn test_jid_validation_invalid_user_chars() {
        let result = Jid::new("user@invalid".to_string(), "s.whatsapp.net".to_string(), 0);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), JidError::InvalidUser(_)));
    }

    #[test]
    fn test_jid_validation_invalid_server_chars() {
        let result = Jid::new("user".to_string(), "server@invalid".to_string(), 0);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), JidError::InvalidServer(_)));
    }

    #[test]
    fn test_jid_parsing_edge_cases() {
        // Empty string
        assert!(matches!(Jid::from_string(""), Err(JidError::EmptyString)));

        // Whitespace only
        assert!(matches!(
            Jid::from_string("   "),
            Err(JidError::EmptyString)
        ));

        // No @ symbol
        assert!(matches!(
            Jid::from_string("invalid"),
            Err(JidError::InvalidFormat(_))
        ));

        // Multiple @ symbols
        assert!(matches!(
            Jid::from_string("user@server@extra"),
            Err(JidError::InvalidFormat(_))
        ));

        // Empty user part
        assert!(matches!(
            Jid::from_string("@server.com"),
            Err(JidError::InvalidUser(_))
        ));

        // Empty server part
        assert!(matches!(
            Jid::from_string("user@"),
            Err(JidError::InvalidServer(_))
        ));
    }

    #[test]
    fn test_jid_parsing_device_edge_cases() {
        // Valid device number
        let jid = Jid::from_string("user@server.1").unwrap();
        assert_eq!(jid.device, 1);

        // Invalid device number (too large)
        let jid = Jid::from_string("user@server.999").unwrap();
        assert_eq!(jid.device, 0); // Should fallback to treating as server
        assert_eq!(jid.server, "server.999");

        // Non-numeric device
        let jid = Jid::from_string("<EMAIL>").unwrap();
        assert_eq!(jid.device, 0);
        assert_eq!(jid.server, "server.abc");

        // Empty device part
        let jid = Jid::from_string("user@server.").unwrap();
        assert_eq!(jid.device, 0);
        assert_eq!(jid.server, "server.");

        // Multiple dots
        let jid = Jid::from_string("<EMAIL>.1").unwrap();
        assert_eq!(jid.device, 1);
        assert_eq!(jid.server, "server.domain");
    }

    #[test]
    fn test_jid_bare() {
        let jid = Jid::from_string("user@server.5").unwrap();
        let bare = jid.to_bare();
        assert_eq!(bare.device, 0);
        assert_eq!(bare.user, jid.user);
        assert_eq!(bare.server, jid.server);
    }

    #[test]
    fn test_jid_with_device_method() {
        let jid = Jid::from_string("user@server").unwrap();
        let with_device = jid.with_device(3);
        assert_eq!(with_device.device, 3);
        assert_eq!(with_device.user, jid.user);
        assert_eq!(with_device.server, jid.server);
    }

    #[test]
    fn test_jid_phone_number() {
        let user_jid = Jid::from_string("<EMAIL>").unwrap();
        assert_eq!(user_jid.to_phone_number(), Some("+1234567890".to_string()));

        let group_jid = Jid::from_string("<EMAIL>").unwrap();
        assert_eq!(group_jid.to_phone_number(), None);

        let non_numeric_jid = Jid::from_string("<EMAIL>").unwrap();
        assert_eq!(non_numeric_jid.to_phone_number(), None);
    }

    #[test]
    fn test_jid_has_device() {
        let jid_no_device = Jid::from_string("user@server").unwrap();
        assert!(!jid_no_device.has_device());

        let jid_with_device = Jid::from_string("user@server.1").unwrap();
        assert!(jid_with_device.has_device());
    }

    #[test]
    fn test_jid_trimming() {
        let jid = Jid::from_string("  user@server.1  ").unwrap();
        assert_eq!(jid.user, "user");
        assert_eq!(jid.server, "server");
        assert_eq!(jid.device, 1);
    }

    #[test]
    fn test_jid_builder() {
        let jid = Jid::builder()
            .user("1234567890")
            .server("s.whatsapp.net")
            .device(1)
            .build()
            .unwrap();

        assert_eq!(jid.user, "1234567890");
        assert_eq!(jid.server, "s.whatsapp.net");
        assert_eq!(jid.device, 1);
    }

    #[test]
    fn test_jid_builder_missing_user() {
        let result = Jid::builder().server("s.whatsapp.net").build();

        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), JidError::InvalidUser(_)));
    }

    #[test]
    fn test_jid_builder_missing_server() {
        let result = Jid::builder().user("1234567890").build();

        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), JidError::InvalidServer(_)));
    }

    #[test]
    fn test_jid_constants() {
        // Test that constants are used correctly
        let group_jid = Jid::from_string("<EMAIL>").unwrap();
        assert!(group_jid.is_group());

        let broadcast_jid = Jid::from_string("broadcast@broadcast").unwrap();
        assert!(broadcast_jid.is_broadcast());

        let status_jid = Jid::from_string("status@broadcast").unwrap();
        assert!(status_jid.is_status_broadcast());

        let user_jid_new = Jid::from_string("<EMAIL>").unwrap();
        assert!(user_jid_new.is_user());

        let user_jid_old = Jid::from_string("<EMAIL>").unwrap();
        assert!(user_jid_old.is_user());

        let server_jid = Jid::from_string("<EMAIL>").unwrap();
        assert!(server_jid.is_server());

        let newsletter_jid = Jid::from_string("news@newsletter").unwrap();
        assert!(newsletter_jid.is_newsletter());
    }

    #[test]
    fn test_jid_factory_methods() {
        // Test user factory
        let user_jid = Jid::user("1234567890").unwrap();
        assert!(user_jid.is_user());
        assert_eq!(user_jid.user, "1234567890");
        assert_eq!(user_jid.server, "s.whatsapp.net");

        // Test user factory with + prefix
        let user_jid_plus = Jid::user("+1234567890").unwrap();
        assert_eq!(user_jid_plus.user, "1234567890");

        // Test invalid phone number
        assert!(Jid::user("123abc").is_err());

        // Test group factory
        let group_jid = Jid::group("group123").unwrap();
        assert!(group_jid.is_group());
        assert_eq!(group_jid.server, "g.us");

        // Test broadcast factory
        let broadcast_jid = Jid::broadcast("broadcast123").unwrap();
        assert!(broadcast_jid.is_broadcast());

        // Test status broadcast factory
        let status_jid = Jid::status_broadcast();
        assert!(status_jid.is_status_broadcast());
    }

    #[test]
    fn test_from_str_trait() {
        let jid: Jid = "<EMAIL>".parse().unwrap();
        assert_eq!(jid.user, "1234567890");
        assert_eq!(jid.server, "s.whatsapp.net");
        assert_eq!(jid.device, 0);

        let result: Result<Jid, _> = "invalid".parse();
        assert!(result.is_err());
    }

    #[test]
    fn test_jid_ordering() {
        let jid1 = Jid::from_string("<EMAIL>").unwrap();
        let jid2 = Jid::from_string("<EMAIL>").unwrap();
        let jid3 = Jid::from_string("<EMAIL>").unwrap();

        assert!(jid1 < jid2);
        assert!(jid3 < jid1); // g.us comes before s.whatsapp.net

        let mut jids = vec![jid2.clone(), jid1.clone(), jid3.clone()];
        jids.sort();
        assert_eq!(jids, vec![jid3, jid1, jid2]);
    }

    #[test]
    fn test_phone_number_validation() {
        // Valid phone numbers
        assert!(Jid::user("1234567890").is_ok());
        assert!(Jid::user("+1234567890").is_ok());
        assert!(Jid::user("12345678901234").is_ok()); // 14 digits

        // Invalid phone numbers
        assert!(Jid::user("123456").is_err()); // Too short
        assert!(Jid::user("1234567890123456").is_err()); // Too long
        assert!(Jid::user("0123456789").is_err()); // Starts with 0
        assert!(Jid::user("123abc7890").is_err()); // Contains letters
        assert!(Jid::user("+123abc7890").is_err()); // Contains letters with +
    }

    #[test]
    fn test_jid_normalization() {
        // Test user JID normalization
        let jid1 = Jid::from_string("<EMAIL>").unwrap();
        let normalized1 = jid1.normalize();
        assert_eq!(normalized1.server, "s.whatsapp.net");
        assert_eq!(normalized1.user, "1234567890");

        // Test group JID normalization (lowercase)
        let jid2 = Jid::from_string("<EMAIL>").unwrap();
        let normalized2 = jid2.normalize();
        assert_eq!(normalized2.user, "group123");
        assert_eq!(normalized2.server, "g.us");

        // Test normalized string output
        let jid3 = Jid::from_string("Test@BROADCAST").unwrap();
        assert_eq!(jid3.to_normalized_string(), "test@broadcast");
    }

    #[test]
    fn test_jid_type_method() {
        assert_eq!(Jid::user("1234567890").unwrap().jid_type(), "user");
        assert_eq!(Jid::group("group123").unwrap().jid_type(), "group");
        assert_eq!(Jid::broadcast("bc123").unwrap().jid_type(), "broadcast");
        assert_eq!(Jid::status_broadcast().jid_type(), "status_broadcast");

        let newsletter = Jid::from_string("news@newsletter").unwrap();
        assert_eq!(newsletter.jid_type(), "newsletter");

        let server = Jid::from_string("<EMAIL>").unwrap();
        assert_eq!(server.jid_type(), "server");
    }

    #[test]
    fn test_equals_bare() {
        let jid1 = Jid::from_string("user@server.1").unwrap();
        let jid2 = Jid::from_string("user@server.2").unwrap();
        let jid3 = Jid::from_string("user@server").unwrap();
        let jid4 = Jid::from_string("other@server").unwrap();

        assert!(jid1.equals_bare(&jid2));
        assert!(jid1.equals_bare(&jid3));
        assert!(!jid1.equals_bare(&jid4));
    }
}
