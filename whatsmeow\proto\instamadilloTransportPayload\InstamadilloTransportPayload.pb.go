// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: instamadilloTransportPayload/InstamadilloTransportPayload.proto

package instamadilloTransportPayload

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	instamadilloAddMessage "go.mau.fi/whatsmeow/proto/instamadilloAddMessage"
	instamadilloDeleteMessage "go.mau.fi/whatsmeow/proto/instamadilloDeleteMessage"
	instamadilloSupplementMessage "go.mau.fi/whatsmeow/proto/instamadilloSupplementMessage"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PayloadCreator int32

const (
	PayloadCreator_PAYLOAD_CREATOR_UNSPECIFIED PayloadCreator = 0
	PayloadCreator_PAYLOAD_CREATOR_IGIOS       PayloadCreator = 1
	PayloadCreator_PAYLOAD_CREATOR_IG4A        PayloadCreator = 2
	PayloadCreator_PAYLOAD_CREATOR_WWW         PayloadCreator = 3
	PayloadCreator_PAYLOAD_CREATOR_IGLITE      PayloadCreator = 4
)

// Enum value maps for PayloadCreator.
var (
	PayloadCreator_name = map[int32]string{
		0: "PAYLOAD_CREATOR_UNSPECIFIED",
		1: "PAYLOAD_CREATOR_IGIOS",
		2: "PAYLOAD_CREATOR_IG4A",
		3: "PAYLOAD_CREATOR_WWW",
		4: "PAYLOAD_CREATOR_IGLITE",
	}
	PayloadCreator_value = map[string]int32{
		"PAYLOAD_CREATOR_UNSPECIFIED": 0,
		"PAYLOAD_CREATOR_IGIOS":       1,
		"PAYLOAD_CREATOR_IG4A":        2,
		"PAYLOAD_CREATOR_WWW":         3,
		"PAYLOAD_CREATOR_IGLITE":      4,
	}
)

func (x PayloadCreator) Enum() *PayloadCreator {
	p := new(PayloadCreator)
	*p = x
	return p
}

func (x PayloadCreator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PayloadCreator) Descriptor() protoreflect.EnumDescriptor {
	return file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_enumTypes[0].Descriptor()
}

func (PayloadCreator) Type() protoreflect.EnumType {
	return &file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_enumTypes[0]
}

func (x PayloadCreator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PayloadCreator) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PayloadCreator(num)
	return nil
}

// Deprecated: Use PayloadCreator.Descriptor instead.
func (PayloadCreator) EnumDescriptor() ([]byte, []int) {
	return file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDescGZIP(), []int{0}
}

type TransportPayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to TransportPayload:
	//
	//	*TransportPayload_Add
	//	*TransportPayload_Delete
	//	*TransportPayload_Supplement
	TransportPayload isTransportPayload_TransportPayload `protobuf_oneof:"transportPayload"`
	Franking         *Franking                           `protobuf:"bytes,4,opt,name=franking" json:"franking,omitempty"`
	OpenEb           *bool                               `protobuf:"varint,5,opt,name=openEb" json:"openEb,omitempty"`
	IsE2EeAttributed *bool                               `protobuf:"varint,6,opt,name=isE2EeAttributed" json:"isE2EeAttributed,omitempty"`
	PayloadCreator   *PayloadCreator                     `protobuf:"varint,7,opt,name=payloadCreator,enum=InstamadilloTransportPayload.PayloadCreator" json:"payloadCreator,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *TransportPayload) Reset() {
	*x = TransportPayload{}
	mi := &file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransportPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportPayload) ProtoMessage() {}

func (x *TransportPayload) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportPayload.ProtoReflect.Descriptor instead.
func (*TransportPayload) Descriptor() ([]byte, []int) {
	return file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDescGZIP(), []int{0}
}

func (x *TransportPayload) GetTransportPayload() isTransportPayload_TransportPayload {
	if x != nil {
		return x.TransportPayload
	}
	return nil
}

func (x *TransportPayload) GetAdd() *instamadilloAddMessage.AddMessagePayload {
	if x != nil {
		if x, ok := x.TransportPayload.(*TransportPayload_Add); ok {
			return x.Add
		}
	}
	return nil
}

func (x *TransportPayload) GetDelete() *instamadilloDeleteMessage.DeleteMessagePayload {
	if x != nil {
		if x, ok := x.TransportPayload.(*TransportPayload_Delete); ok {
			return x.Delete
		}
	}
	return nil
}

func (x *TransportPayload) GetSupplement() *instamadilloSupplementMessage.SupplementMessagePayload {
	if x != nil {
		if x, ok := x.TransportPayload.(*TransportPayload_Supplement); ok {
			return x.Supplement
		}
	}
	return nil
}

func (x *TransportPayload) GetFranking() *Franking {
	if x != nil {
		return x.Franking
	}
	return nil
}

func (x *TransportPayload) GetOpenEb() bool {
	if x != nil && x.OpenEb != nil {
		return *x.OpenEb
	}
	return false
}

func (x *TransportPayload) GetIsE2EeAttributed() bool {
	if x != nil && x.IsE2EeAttributed != nil {
		return *x.IsE2EeAttributed
	}
	return false
}

func (x *TransportPayload) GetPayloadCreator() PayloadCreator {
	if x != nil && x.PayloadCreator != nil {
		return *x.PayloadCreator
	}
	return PayloadCreator_PAYLOAD_CREATOR_UNSPECIFIED
}

type isTransportPayload_TransportPayload interface {
	isTransportPayload_TransportPayload()
}

type TransportPayload_Add struct {
	Add *instamadilloAddMessage.AddMessagePayload `protobuf:"bytes,1,opt,name=add,oneof"`
}

type TransportPayload_Delete struct {
	Delete *instamadilloDeleteMessage.DeleteMessagePayload `protobuf:"bytes,2,opt,name=delete,oneof"`
}

type TransportPayload_Supplement struct {
	Supplement *instamadilloSupplementMessage.SupplementMessagePayload `protobuf:"bytes,3,opt,name=supplement,oneof"`
}

func (*TransportPayload_Add) isTransportPayload_TransportPayload() {}

func (*TransportPayload_Delete) isTransportPayload_TransportPayload() {}

func (*TransportPayload_Supplement) isTransportPayload_TransportPayload() {}

type Franking struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	FrankingKey     []byte                 `protobuf:"bytes,1,opt,name=frankingKey" json:"frankingKey,omitempty"`
	FrankingVersion *int32                 `protobuf:"varint,2,opt,name=frankingVersion" json:"frankingVersion,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Franking) Reset() {
	*x = Franking{}
	mi := &file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Franking) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Franking) ProtoMessage() {}

func (x *Franking) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Franking.ProtoReflect.Descriptor instead.
func (*Franking) Descriptor() ([]byte, []int) {
	return file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDescGZIP(), []int{1}
}

func (x *Franking) GetFrankingKey() []byte {
	if x != nil {
		return x.FrankingKey
	}
	return nil
}

func (x *Franking) GetFrankingVersion() int32 {
	if x != nil && x.FrankingVersion != nil {
		return *x.FrankingVersion
	}
	return 0
}

var File_instamadilloTransportPayload_InstamadilloTransportPayload_proto protoreflect.FileDescriptor

const file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDesc = "" +
	"\n" +
	"?instamadilloTransportPayload/InstamadilloTransportPayload.proto\x12\x1cInstamadilloTransportPayload\x1a3instamadilloAddMessage/InstamadilloAddMessage.proto\x1a9instamadilloDeleteMessage/InstamadilloDeleteMessage.proto\x1aAinstamadilloSupplementMessage/InstamadilloSupplementMessage.proto\"\xe9\x03\n" +
	"\x10TransportPayload\x12=\n" +
	"\x03add\x18\x01 \x01(\v2).InstamadilloAddMessage.AddMessagePayloadH\x00R\x03add\x12I\n" +
	"\x06delete\x18\x02 \x01(\v2/.InstamadilloDeleteMessage.DeleteMessagePayloadH\x00R\x06delete\x12Y\n" +
	"\n" +
	"supplement\x18\x03 \x01(\v27.InstamadilloSupplementMessage.SupplementMessagePayloadH\x00R\n" +
	"supplement\x12B\n" +
	"\bfranking\x18\x04 \x01(\v2&.InstamadilloTransportPayload.FrankingR\bfranking\x12\x16\n" +
	"\x06openEb\x18\x05 \x01(\bR\x06openEb\x12*\n" +
	"\x10isE2EeAttributed\x18\x06 \x01(\bR\x10isE2EeAttributed\x12T\n" +
	"\x0epayloadCreator\x18\a \x01(\x0e2,.InstamadilloTransportPayload.PayloadCreatorR\x0epayloadCreatorB\x12\n" +
	"\x10transportPayload\"V\n" +
	"\bFranking\x12 \n" +
	"\vfrankingKey\x18\x01 \x01(\fR\vfrankingKey\x12(\n" +
	"\x0ffrankingVersion\x18\x02 \x01(\x05R\x0ffrankingVersion*\x9b\x01\n" +
	"\x0ePayloadCreator\x12\x1f\n" +
	"\x1bPAYLOAD_CREATOR_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15PAYLOAD_CREATOR_IGIOS\x10\x01\x12\x18\n" +
	"\x14PAYLOAD_CREATOR_IG4A\x10\x02\x12\x17\n" +
	"\x13PAYLOAD_CREATOR_WWW\x10\x03\x12\x1a\n" +
	"\x16PAYLOAD_CREATOR_IGLITE\x10\x04B8Z6go.mau.fi/whatsmeow/proto/instamadilloTransportPayload"

var (
	file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDescOnce sync.Once
	file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDescData []byte
)

func file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDescGZIP() []byte {
	file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDescOnce.Do(func() {
		file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDesc), len(file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDesc)))
	})
	return file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDescData
}

var file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_goTypes = []any{
	(PayloadCreator)(0),                                            // 0: InstamadilloTransportPayload.PayloadCreator
	(*TransportPayload)(nil),                                       // 1: InstamadilloTransportPayload.TransportPayload
	(*Franking)(nil),                                               // 2: InstamadilloTransportPayload.Franking
	(*instamadilloAddMessage.AddMessagePayload)(nil),               // 3: InstamadilloAddMessage.AddMessagePayload
	(*instamadilloDeleteMessage.DeleteMessagePayload)(nil),         // 4: InstamadilloDeleteMessage.DeleteMessagePayload
	(*instamadilloSupplementMessage.SupplementMessagePayload)(nil), // 5: InstamadilloSupplementMessage.SupplementMessagePayload
}
var file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_depIdxs = []int32{
	3, // 0: InstamadilloTransportPayload.TransportPayload.add:type_name -> InstamadilloAddMessage.AddMessagePayload
	4, // 1: InstamadilloTransportPayload.TransportPayload.delete:type_name -> InstamadilloDeleteMessage.DeleteMessagePayload
	5, // 2: InstamadilloTransportPayload.TransportPayload.supplement:type_name -> InstamadilloSupplementMessage.SupplementMessagePayload
	2, // 3: InstamadilloTransportPayload.TransportPayload.franking:type_name -> InstamadilloTransportPayload.Franking
	0, // 4: InstamadilloTransportPayload.TransportPayload.payloadCreator:type_name -> InstamadilloTransportPayload.PayloadCreator
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_init() }
func file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_init() {
	if File_instamadilloTransportPayload_InstamadilloTransportPayload_proto != nil {
		return
	}
	file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_msgTypes[0].OneofWrappers = []any{
		(*TransportPayload_Add)(nil),
		(*TransportPayload_Delete)(nil),
		(*TransportPayload_Supplement)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDesc), len(file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_goTypes,
		DependencyIndexes: file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_depIdxs,
		EnumInfos:         file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_enumTypes,
		MessageInfos:      file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_msgTypes,
	}.Build()
	File_instamadilloTransportPayload_InstamadilloTransportPayload_proto = out.File
	file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_goTypes = nil
	file_instamadilloTransportPayload_InstamadilloTransportPayload_proto_depIdxs = nil
}
