// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waHistorySync/WAWebProtobufsHistorySync.proto

package waHistorySync

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	waChatLockSettings "go.mau.fi/whatsmeow/proto/waChatLockSettings"
	waCommon "go.mau.fi/whatsmeow/proto/waCommon"
	waE2E "go.mau.fi/whatsmeow/proto/waE2E"
	waSyncAction "go.mau.fi/whatsmeow/proto/waSyncAction"
	waWeb "go.mau.fi/whatsmeow/proto/waWeb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MediaVisibility int32

const (
	MediaVisibility_DEFAULT MediaVisibility = 0
	MediaVisibility_OFF     MediaVisibility = 1
	MediaVisibility_ON      MediaVisibility = 2
)

// Enum value maps for MediaVisibility.
var (
	MediaVisibility_name = map[int32]string{
		0: "DEFAULT",
		1: "OFF",
		2: "ON",
	}
	MediaVisibility_value = map[string]int32{
		"DEFAULT": 0,
		"OFF":     1,
		"ON":      2,
	}
)

func (x MediaVisibility) Enum() *MediaVisibility {
	p := new(MediaVisibility)
	*p = x
	return p
}

func (x MediaVisibility) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MediaVisibility) Descriptor() protoreflect.EnumDescriptor {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[0].Descriptor()
}

func (MediaVisibility) Type() protoreflect.EnumType {
	return &file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[0]
}

func (x MediaVisibility) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MediaVisibility) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MediaVisibility(num)
	return nil
}

// Deprecated: Use MediaVisibility.Descriptor instead.
func (MediaVisibility) EnumDescriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{0}
}

type PrivacySystemMessage int32

const (
	PrivacySystemMessage_E2EE_MSG    PrivacySystemMessage = 1
	PrivacySystemMessage_NE2EE_SELF  PrivacySystemMessage = 2
	PrivacySystemMessage_NE2EE_OTHER PrivacySystemMessage = 3
)

// Enum value maps for PrivacySystemMessage.
var (
	PrivacySystemMessage_name = map[int32]string{
		1: "E2EE_MSG",
		2: "NE2EE_SELF",
		3: "NE2EE_OTHER",
	}
	PrivacySystemMessage_value = map[string]int32{
		"E2EE_MSG":    1,
		"NE2EE_SELF":  2,
		"NE2EE_OTHER": 3,
	}
)

func (x PrivacySystemMessage) Enum() *PrivacySystemMessage {
	p := new(PrivacySystemMessage)
	*p = x
	return p
}

func (x PrivacySystemMessage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PrivacySystemMessage) Descriptor() protoreflect.EnumDescriptor {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[1].Descriptor()
}

func (PrivacySystemMessage) Type() protoreflect.EnumType {
	return &file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[1]
}

func (x PrivacySystemMessage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PrivacySystemMessage) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PrivacySystemMessage(num)
	return nil
}

// Deprecated: Use PrivacySystemMessage.Descriptor instead.
func (PrivacySystemMessage) EnumDescriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{1}
}

type HistorySync_BotAIWaitListState int32

const (
	HistorySync_IN_WAITLIST  HistorySync_BotAIWaitListState = 0
	HistorySync_AI_AVAILABLE HistorySync_BotAIWaitListState = 1
)

// Enum value maps for HistorySync_BotAIWaitListState.
var (
	HistorySync_BotAIWaitListState_name = map[int32]string{
		0: "IN_WAITLIST",
		1: "AI_AVAILABLE",
	}
	HistorySync_BotAIWaitListState_value = map[string]int32{
		"IN_WAITLIST":  0,
		"AI_AVAILABLE": 1,
	}
)

func (x HistorySync_BotAIWaitListState) Enum() *HistorySync_BotAIWaitListState {
	p := new(HistorySync_BotAIWaitListState)
	*p = x
	return p
}

func (x HistorySync_BotAIWaitListState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HistorySync_BotAIWaitListState) Descriptor() protoreflect.EnumDescriptor {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[2].Descriptor()
}

func (HistorySync_BotAIWaitListState) Type() protoreflect.EnumType {
	return &file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[2]
}

func (x HistorySync_BotAIWaitListState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *HistorySync_BotAIWaitListState) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = HistorySync_BotAIWaitListState(num)
	return nil
}

// Deprecated: Use HistorySync_BotAIWaitListState.Descriptor instead.
func (HistorySync_BotAIWaitListState) EnumDescriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{0, 0}
}

type HistorySync_HistorySyncType int32

const (
	HistorySync_INITIAL_BOOTSTRAP HistorySync_HistorySyncType = 0
	HistorySync_INITIAL_STATUS_V3 HistorySync_HistorySyncType = 1
	HistorySync_FULL              HistorySync_HistorySyncType = 2
	HistorySync_RECENT            HistorySync_HistorySyncType = 3
	HistorySync_PUSH_NAME         HistorySync_HistorySyncType = 4
	HistorySync_NON_BLOCKING_DATA HistorySync_HistorySyncType = 5
	HistorySync_ON_DEMAND         HistorySync_HistorySyncType = 6
)

// Enum value maps for HistorySync_HistorySyncType.
var (
	HistorySync_HistorySyncType_name = map[int32]string{
		0: "INITIAL_BOOTSTRAP",
		1: "INITIAL_STATUS_V3",
		2: "FULL",
		3: "RECENT",
		4: "PUSH_NAME",
		5: "NON_BLOCKING_DATA",
		6: "ON_DEMAND",
	}
	HistorySync_HistorySyncType_value = map[string]int32{
		"INITIAL_BOOTSTRAP": 0,
		"INITIAL_STATUS_V3": 1,
		"FULL":              2,
		"RECENT":            3,
		"PUSH_NAME":         4,
		"NON_BLOCKING_DATA": 5,
		"ON_DEMAND":         6,
	}
)

func (x HistorySync_HistorySyncType) Enum() *HistorySync_HistorySyncType {
	p := new(HistorySync_HistorySyncType)
	*p = x
	return p
}

func (x HistorySync_HistorySyncType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HistorySync_HistorySyncType) Descriptor() protoreflect.EnumDescriptor {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[3].Descriptor()
}

func (HistorySync_HistorySyncType) Type() protoreflect.EnumType {
	return &file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[3]
}

func (x HistorySync_HistorySyncType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *HistorySync_HistorySyncType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = HistorySync_HistorySyncType(num)
	return nil
}

// Deprecated: Use HistorySync_HistorySyncType.Descriptor instead.
func (HistorySync_HistorySyncType) EnumDescriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{0, 1}
}

type Conversation_EndOfHistoryTransferType int32

const (
	Conversation_COMPLETE_BUT_MORE_MESSAGES_REMAIN_ON_PRIMARY           Conversation_EndOfHistoryTransferType = 0
	Conversation_COMPLETE_AND_NO_MORE_MESSAGE_REMAIN_ON_PRIMARY         Conversation_EndOfHistoryTransferType = 1
	Conversation_COMPLETE_ON_DEMAND_SYNC_BUT_MORE_MSG_REMAIN_ON_PRIMARY Conversation_EndOfHistoryTransferType = 2
)

// Enum value maps for Conversation_EndOfHistoryTransferType.
var (
	Conversation_EndOfHistoryTransferType_name = map[int32]string{
		0: "COMPLETE_BUT_MORE_MESSAGES_REMAIN_ON_PRIMARY",
		1: "COMPLETE_AND_NO_MORE_MESSAGE_REMAIN_ON_PRIMARY",
		2: "COMPLETE_ON_DEMAND_SYNC_BUT_MORE_MSG_REMAIN_ON_PRIMARY",
	}
	Conversation_EndOfHistoryTransferType_value = map[string]int32{
		"COMPLETE_BUT_MORE_MESSAGES_REMAIN_ON_PRIMARY":           0,
		"COMPLETE_AND_NO_MORE_MESSAGE_REMAIN_ON_PRIMARY":         1,
		"COMPLETE_ON_DEMAND_SYNC_BUT_MORE_MSG_REMAIN_ON_PRIMARY": 2,
	}
)

func (x Conversation_EndOfHistoryTransferType) Enum() *Conversation_EndOfHistoryTransferType {
	p := new(Conversation_EndOfHistoryTransferType)
	*p = x
	return p
}

func (x Conversation_EndOfHistoryTransferType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Conversation_EndOfHistoryTransferType) Descriptor() protoreflect.EnumDescriptor {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[4].Descriptor()
}

func (Conversation_EndOfHistoryTransferType) Type() protoreflect.EnumType {
	return &file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[4]
}

func (x Conversation_EndOfHistoryTransferType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Conversation_EndOfHistoryTransferType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Conversation_EndOfHistoryTransferType(num)
	return nil
}

// Deprecated: Use Conversation_EndOfHistoryTransferType.Descriptor instead.
func (Conversation_EndOfHistoryTransferType) EnumDescriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{1, 0}
}

type GroupParticipant_Rank int32

const (
	GroupParticipant_REGULAR    GroupParticipant_Rank = 0
	GroupParticipant_ADMIN      GroupParticipant_Rank = 1
	GroupParticipant_SUPERADMIN GroupParticipant_Rank = 2
)

// Enum value maps for GroupParticipant_Rank.
var (
	GroupParticipant_Rank_name = map[int32]string{
		0: "REGULAR",
		1: "ADMIN",
		2: "SUPERADMIN",
	}
	GroupParticipant_Rank_value = map[string]int32{
		"REGULAR":    0,
		"ADMIN":      1,
		"SUPERADMIN": 2,
	}
)

func (x GroupParticipant_Rank) Enum() *GroupParticipant_Rank {
	p := new(GroupParticipant_Rank)
	*p = x
	return p
}

func (x GroupParticipant_Rank) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GroupParticipant_Rank) Descriptor() protoreflect.EnumDescriptor {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[5].Descriptor()
}

func (GroupParticipant_Rank) Type() protoreflect.EnumType {
	return &file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[5]
}

func (x GroupParticipant_Rank) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *GroupParticipant_Rank) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = GroupParticipant_Rank(num)
	return nil
}

// Deprecated: Use GroupParticipant_Rank.Descriptor instead.
func (GroupParticipant_Rank) EnumDescriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{2, 0}
}

type PastParticipant_LeaveReason int32

const (
	PastParticipant_LEFT    PastParticipant_LeaveReason = 0
	PastParticipant_REMOVED PastParticipant_LeaveReason = 1
)

// Enum value maps for PastParticipant_LeaveReason.
var (
	PastParticipant_LeaveReason_name = map[int32]string{
		0: "LEFT",
		1: "REMOVED",
	}
	PastParticipant_LeaveReason_value = map[string]int32{
		"LEFT":    0,
		"REMOVED": 1,
	}
)

func (x PastParticipant_LeaveReason) Enum() *PastParticipant_LeaveReason {
	p := new(PastParticipant_LeaveReason)
	*p = x
	return p
}

func (x PastParticipant_LeaveReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PastParticipant_LeaveReason) Descriptor() protoreflect.EnumDescriptor {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[6].Descriptor()
}

func (PastParticipant_LeaveReason) Type() protoreflect.EnumType {
	return &file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes[6]
}

func (x PastParticipant_LeaveReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PastParticipant_LeaveReason) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PastParticipant_LeaveReason(num)
	return nil
}

// Deprecated: Use PastParticipant_LeaveReason.Descriptor instead.
func (PastParticipant_LeaveReason) EnumDescriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{3, 0}
}

type HistorySync struct {
	state                                protoimpl.MessageState          `protogen:"open.v1"`
	SyncType                             *HistorySync_HistorySyncType    `protobuf:"varint,1,req,name=syncType,enum=WAWebProtobufsHistorySync.HistorySync_HistorySyncType" json:"syncType,omitempty"`
	Conversations                        []*Conversation                 `protobuf:"bytes,2,rep,name=conversations" json:"conversations,omitempty"`
	StatusV3Messages                     []*waWeb.WebMessageInfo         `protobuf:"bytes,3,rep,name=statusV3Messages" json:"statusV3Messages,omitempty"`
	ChunkOrder                           *uint32                         `protobuf:"varint,5,opt,name=chunkOrder" json:"chunkOrder,omitempty"`
	Progress                             *uint32                         `protobuf:"varint,6,opt,name=progress" json:"progress,omitempty"`
	Pushnames                            []*Pushname                     `protobuf:"bytes,7,rep,name=pushnames" json:"pushnames,omitempty"`
	GlobalSettings                       *GlobalSettings                 `protobuf:"bytes,8,opt,name=globalSettings" json:"globalSettings,omitempty"`
	ThreadIDUserSecret                   []byte                          `protobuf:"bytes,9,opt,name=threadIDUserSecret" json:"threadIDUserSecret,omitempty"`
	ThreadDsTimeframeOffset              *uint32                         `protobuf:"varint,10,opt,name=threadDsTimeframeOffset" json:"threadDsTimeframeOffset,omitempty"`
	RecentStickers                       []*StickerMetadata              `protobuf:"bytes,11,rep,name=recentStickers" json:"recentStickers,omitempty"`
	PastParticipants                     []*PastParticipants             `protobuf:"bytes,12,rep,name=pastParticipants" json:"pastParticipants,omitempty"`
	CallLogRecords                       []*waSyncAction.CallLogRecord   `protobuf:"bytes,13,rep,name=callLogRecords" json:"callLogRecords,omitempty"`
	AiWaitListState                      *HistorySync_BotAIWaitListState `protobuf:"varint,14,opt,name=aiWaitListState,enum=WAWebProtobufsHistorySync.HistorySync_BotAIWaitListState" json:"aiWaitListState,omitempty"`
	PhoneNumberToLidMappings             []*PhoneNumberToLIDMapping      `protobuf:"bytes,15,rep,name=phoneNumberToLidMappings" json:"phoneNumberToLidMappings,omitempty"`
	CompanionMetaNonce                   *string                         `protobuf:"bytes,16,opt,name=companionMetaNonce" json:"companionMetaNonce,omitempty"`
	ShareableChatIdentifierEncryptionKey []byte                          `protobuf:"bytes,17,opt,name=shareableChatIdentifierEncryptionKey" json:"shareableChatIdentifierEncryptionKey,omitempty"`
	Accounts                             []*Account                      `protobuf:"bytes,18,rep,name=accounts" json:"accounts,omitempty"`
	unknownFields                        protoimpl.UnknownFields
	sizeCache                            protoimpl.SizeCache
}

func (x *HistorySync) Reset() {
	*x = HistorySync{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistorySync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistorySync) ProtoMessage() {}

func (x *HistorySync) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistorySync.ProtoReflect.Descriptor instead.
func (*HistorySync) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{0}
}

func (x *HistorySync) GetSyncType() HistorySync_HistorySyncType {
	if x != nil && x.SyncType != nil {
		return *x.SyncType
	}
	return HistorySync_INITIAL_BOOTSTRAP
}

func (x *HistorySync) GetConversations() []*Conversation {
	if x != nil {
		return x.Conversations
	}
	return nil
}

func (x *HistorySync) GetStatusV3Messages() []*waWeb.WebMessageInfo {
	if x != nil {
		return x.StatusV3Messages
	}
	return nil
}

func (x *HistorySync) GetChunkOrder() uint32 {
	if x != nil && x.ChunkOrder != nil {
		return *x.ChunkOrder
	}
	return 0
}

func (x *HistorySync) GetProgress() uint32 {
	if x != nil && x.Progress != nil {
		return *x.Progress
	}
	return 0
}

func (x *HistorySync) GetPushnames() []*Pushname {
	if x != nil {
		return x.Pushnames
	}
	return nil
}

func (x *HistorySync) GetGlobalSettings() *GlobalSettings {
	if x != nil {
		return x.GlobalSettings
	}
	return nil
}

func (x *HistorySync) GetThreadIDUserSecret() []byte {
	if x != nil {
		return x.ThreadIDUserSecret
	}
	return nil
}

func (x *HistorySync) GetThreadDsTimeframeOffset() uint32 {
	if x != nil && x.ThreadDsTimeframeOffset != nil {
		return *x.ThreadDsTimeframeOffset
	}
	return 0
}

func (x *HistorySync) GetRecentStickers() []*StickerMetadata {
	if x != nil {
		return x.RecentStickers
	}
	return nil
}

func (x *HistorySync) GetPastParticipants() []*PastParticipants {
	if x != nil {
		return x.PastParticipants
	}
	return nil
}

func (x *HistorySync) GetCallLogRecords() []*waSyncAction.CallLogRecord {
	if x != nil {
		return x.CallLogRecords
	}
	return nil
}

func (x *HistorySync) GetAiWaitListState() HistorySync_BotAIWaitListState {
	if x != nil && x.AiWaitListState != nil {
		return *x.AiWaitListState
	}
	return HistorySync_IN_WAITLIST
}

func (x *HistorySync) GetPhoneNumberToLidMappings() []*PhoneNumberToLIDMapping {
	if x != nil {
		return x.PhoneNumberToLidMappings
	}
	return nil
}

func (x *HistorySync) GetCompanionMetaNonce() string {
	if x != nil && x.CompanionMetaNonce != nil {
		return *x.CompanionMetaNonce
	}
	return ""
}

func (x *HistorySync) GetShareableChatIdentifierEncryptionKey() []byte {
	if x != nil {
		return x.ShareableChatIdentifierEncryptionKey
	}
	return nil
}

func (x *HistorySync) GetAccounts() []*Account {
	if x != nil {
		return x.Accounts
	}
	return nil
}

type Conversation struct {
	state                        protoimpl.MessageState                 `protogen:"open.v1"`
	ID                           *string                                `protobuf:"bytes,1,req,name=ID" json:"ID,omitempty"`
	Messages                     []*HistorySyncMsg                      `protobuf:"bytes,2,rep,name=messages" json:"messages,omitempty"`
	NewJID                       *string                                `protobuf:"bytes,3,opt,name=newJID" json:"newJID,omitempty"`
	OldJID                       *string                                `protobuf:"bytes,4,opt,name=oldJID" json:"oldJID,omitempty"`
	LastMsgTimestamp             *uint64                                `protobuf:"varint,5,opt,name=lastMsgTimestamp" json:"lastMsgTimestamp,omitempty"`
	UnreadCount                  *uint32                                `protobuf:"varint,6,opt,name=unreadCount" json:"unreadCount,omitempty"`
	ReadOnly                     *bool                                  `protobuf:"varint,7,opt,name=readOnly" json:"readOnly,omitempty"`
	EndOfHistoryTransfer         *bool                                  `protobuf:"varint,8,opt,name=endOfHistoryTransfer" json:"endOfHistoryTransfer,omitempty"`
	EphemeralExpiration          *uint32                                `protobuf:"varint,9,opt,name=ephemeralExpiration" json:"ephemeralExpiration,omitempty"`
	EphemeralSettingTimestamp    *int64                                 `protobuf:"varint,10,opt,name=ephemeralSettingTimestamp" json:"ephemeralSettingTimestamp,omitempty"`
	EndOfHistoryTransferType     *Conversation_EndOfHistoryTransferType `protobuf:"varint,11,opt,name=endOfHistoryTransferType,enum=WAWebProtobufsHistorySync.Conversation_EndOfHistoryTransferType" json:"endOfHistoryTransferType,omitempty"`
	ConversationTimestamp        *uint64                                `protobuf:"varint,12,opt,name=conversationTimestamp" json:"conversationTimestamp,omitempty"`
	Name                         *string                                `protobuf:"bytes,13,opt,name=name" json:"name,omitempty"`
	PHash                        *string                                `protobuf:"bytes,14,opt,name=pHash" json:"pHash,omitempty"`
	NotSpam                      *bool                                  `protobuf:"varint,15,opt,name=notSpam" json:"notSpam,omitempty"`
	Archived                     *bool                                  `protobuf:"varint,16,opt,name=archived" json:"archived,omitempty"`
	DisappearingMode             *waE2E.DisappearingMode                `protobuf:"bytes,17,opt,name=disappearingMode" json:"disappearingMode,omitempty"`
	UnreadMentionCount           *uint32                                `protobuf:"varint,18,opt,name=unreadMentionCount" json:"unreadMentionCount,omitempty"`
	MarkedAsUnread               *bool                                  `protobuf:"varint,19,opt,name=markedAsUnread" json:"markedAsUnread,omitempty"`
	Participant                  []*GroupParticipant                    `protobuf:"bytes,20,rep,name=participant" json:"participant,omitempty"`
	TcToken                      []byte                                 `protobuf:"bytes,21,opt,name=tcToken" json:"tcToken,omitempty"`
	TcTokenTimestamp             *uint64                                `protobuf:"varint,22,opt,name=tcTokenTimestamp" json:"tcTokenTimestamp,omitempty"`
	ContactPrimaryIdentityKey    []byte                                 `protobuf:"bytes,23,opt,name=contactPrimaryIdentityKey" json:"contactPrimaryIdentityKey,omitempty"`
	Pinned                       *uint32                                `protobuf:"varint,24,opt,name=pinned" json:"pinned,omitempty"`
	MuteEndTime                  *uint64                                `protobuf:"varint,25,opt,name=muteEndTime" json:"muteEndTime,omitempty"`
	Wallpaper                    *WallpaperSettings                     `protobuf:"bytes,26,opt,name=wallpaper" json:"wallpaper,omitempty"`
	MediaVisibility              *MediaVisibility                       `protobuf:"varint,27,opt,name=mediaVisibility,enum=WAWebProtobufsHistorySync.MediaVisibility" json:"mediaVisibility,omitempty"`
	TcTokenSenderTimestamp       *uint64                                `protobuf:"varint,28,opt,name=tcTokenSenderTimestamp" json:"tcTokenSenderTimestamp,omitempty"`
	Suspended                    *bool                                  `protobuf:"varint,29,opt,name=suspended" json:"suspended,omitempty"`
	Terminated                   *bool                                  `protobuf:"varint,30,opt,name=terminated" json:"terminated,omitempty"`
	CreatedAt                    *uint64                                `protobuf:"varint,31,opt,name=createdAt" json:"createdAt,omitempty"`
	CreatedBy                    *string                                `protobuf:"bytes,32,opt,name=createdBy" json:"createdBy,omitempty"`
	Description                  *string                                `protobuf:"bytes,33,opt,name=description" json:"description,omitempty"`
	Support                      *bool                                  `protobuf:"varint,34,opt,name=support" json:"support,omitempty"`
	IsParentGroup                *bool                                  `protobuf:"varint,35,opt,name=isParentGroup" json:"isParentGroup,omitempty"`
	ParentGroupID                *string                                `protobuf:"bytes,37,opt,name=parentGroupID" json:"parentGroupID,omitempty"`
	IsDefaultSubgroup            *bool                                  `protobuf:"varint,36,opt,name=isDefaultSubgroup" json:"isDefaultSubgroup,omitempty"`
	DisplayName                  *string                                `protobuf:"bytes,38,opt,name=displayName" json:"displayName,omitempty"`
	PnJID                        *string                                `protobuf:"bytes,39,opt,name=pnJID" json:"pnJID,omitempty"`
	ShareOwnPn                   *bool                                  `protobuf:"varint,40,opt,name=shareOwnPn" json:"shareOwnPn,omitempty"`
	PnhDuplicateLidThread        *bool                                  `protobuf:"varint,41,opt,name=pnhDuplicateLidThread" json:"pnhDuplicateLidThread,omitempty"`
	LidJID                       *string                                `protobuf:"bytes,42,opt,name=lidJID" json:"lidJID,omitempty"`
	Username                     *string                                `protobuf:"bytes,43,opt,name=username" json:"username,omitempty"`
	LidOriginType                *string                                `protobuf:"bytes,44,opt,name=lidOriginType" json:"lidOriginType,omitempty"`
	CommentsCount                *uint32                                `protobuf:"varint,45,opt,name=commentsCount" json:"commentsCount,omitempty"`
	Locked                       *bool                                  `protobuf:"varint,46,opt,name=locked" json:"locked,omitempty"`
	SystemMessageToInsert        *PrivacySystemMessage                  `protobuf:"varint,47,opt,name=systemMessageToInsert,enum=WAWebProtobufsHistorySync.PrivacySystemMessage" json:"systemMessageToInsert,omitempty"`
	CapiCreatedGroup             *bool                                  `protobuf:"varint,48,opt,name=capiCreatedGroup" json:"capiCreatedGroup,omitempty"`
	AccountLid                   *string                                `protobuf:"bytes,49,opt,name=accountLid" json:"accountLid,omitempty"`
	LimitSharing                 *bool                                  `protobuf:"varint,50,opt,name=limitSharing" json:"limitSharing,omitempty"`
	LimitSharingSettingTimestamp *int64                                 `protobuf:"varint,51,opt,name=limitSharingSettingTimestamp" json:"limitSharingSettingTimestamp,omitempty"`
	LimitSharingTrigger          *waCommon.LimitSharing_Trigger         `protobuf:"varint,52,opt,name=limitSharingTrigger,enum=WACommon.LimitSharing_Trigger" json:"limitSharingTrigger,omitempty"`
	LimitSharingInitiatedByMe    *bool                                  `protobuf:"varint,53,opt,name=limitSharingInitiatedByMe" json:"limitSharingInitiatedByMe,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *Conversation) Reset() {
	*x = Conversation{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Conversation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Conversation) ProtoMessage() {}

func (x *Conversation) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Conversation.ProtoReflect.Descriptor instead.
func (*Conversation) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{1}
}

func (x *Conversation) GetID() string {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return ""
}

func (x *Conversation) GetMessages() []*HistorySyncMsg {
	if x != nil {
		return x.Messages
	}
	return nil
}

func (x *Conversation) GetNewJID() string {
	if x != nil && x.NewJID != nil {
		return *x.NewJID
	}
	return ""
}

func (x *Conversation) GetOldJID() string {
	if x != nil && x.OldJID != nil {
		return *x.OldJID
	}
	return ""
}

func (x *Conversation) GetLastMsgTimestamp() uint64 {
	if x != nil && x.LastMsgTimestamp != nil {
		return *x.LastMsgTimestamp
	}
	return 0
}

func (x *Conversation) GetUnreadCount() uint32 {
	if x != nil && x.UnreadCount != nil {
		return *x.UnreadCount
	}
	return 0
}

func (x *Conversation) GetReadOnly() bool {
	if x != nil && x.ReadOnly != nil {
		return *x.ReadOnly
	}
	return false
}

func (x *Conversation) GetEndOfHistoryTransfer() bool {
	if x != nil && x.EndOfHistoryTransfer != nil {
		return *x.EndOfHistoryTransfer
	}
	return false
}

func (x *Conversation) GetEphemeralExpiration() uint32 {
	if x != nil && x.EphemeralExpiration != nil {
		return *x.EphemeralExpiration
	}
	return 0
}

func (x *Conversation) GetEphemeralSettingTimestamp() int64 {
	if x != nil && x.EphemeralSettingTimestamp != nil {
		return *x.EphemeralSettingTimestamp
	}
	return 0
}

func (x *Conversation) GetEndOfHistoryTransferType() Conversation_EndOfHistoryTransferType {
	if x != nil && x.EndOfHistoryTransferType != nil {
		return *x.EndOfHistoryTransferType
	}
	return Conversation_COMPLETE_BUT_MORE_MESSAGES_REMAIN_ON_PRIMARY
}

func (x *Conversation) GetConversationTimestamp() uint64 {
	if x != nil && x.ConversationTimestamp != nil {
		return *x.ConversationTimestamp
	}
	return 0
}

func (x *Conversation) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Conversation) GetPHash() string {
	if x != nil && x.PHash != nil {
		return *x.PHash
	}
	return ""
}

func (x *Conversation) GetNotSpam() bool {
	if x != nil && x.NotSpam != nil {
		return *x.NotSpam
	}
	return false
}

func (x *Conversation) GetArchived() bool {
	if x != nil && x.Archived != nil {
		return *x.Archived
	}
	return false
}

func (x *Conversation) GetDisappearingMode() *waE2E.DisappearingMode {
	if x != nil {
		return x.DisappearingMode
	}
	return nil
}

func (x *Conversation) GetUnreadMentionCount() uint32 {
	if x != nil && x.UnreadMentionCount != nil {
		return *x.UnreadMentionCount
	}
	return 0
}

func (x *Conversation) GetMarkedAsUnread() bool {
	if x != nil && x.MarkedAsUnread != nil {
		return *x.MarkedAsUnread
	}
	return false
}

func (x *Conversation) GetParticipant() []*GroupParticipant {
	if x != nil {
		return x.Participant
	}
	return nil
}

func (x *Conversation) GetTcToken() []byte {
	if x != nil {
		return x.TcToken
	}
	return nil
}

func (x *Conversation) GetTcTokenTimestamp() uint64 {
	if x != nil && x.TcTokenTimestamp != nil {
		return *x.TcTokenTimestamp
	}
	return 0
}

func (x *Conversation) GetContactPrimaryIdentityKey() []byte {
	if x != nil {
		return x.ContactPrimaryIdentityKey
	}
	return nil
}

func (x *Conversation) GetPinned() uint32 {
	if x != nil && x.Pinned != nil {
		return *x.Pinned
	}
	return 0
}

func (x *Conversation) GetMuteEndTime() uint64 {
	if x != nil && x.MuteEndTime != nil {
		return *x.MuteEndTime
	}
	return 0
}

func (x *Conversation) GetWallpaper() *WallpaperSettings {
	if x != nil {
		return x.Wallpaper
	}
	return nil
}

func (x *Conversation) GetMediaVisibility() MediaVisibility {
	if x != nil && x.MediaVisibility != nil {
		return *x.MediaVisibility
	}
	return MediaVisibility_DEFAULT
}

func (x *Conversation) GetTcTokenSenderTimestamp() uint64 {
	if x != nil && x.TcTokenSenderTimestamp != nil {
		return *x.TcTokenSenderTimestamp
	}
	return 0
}

func (x *Conversation) GetSuspended() bool {
	if x != nil && x.Suspended != nil {
		return *x.Suspended
	}
	return false
}

func (x *Conversation) GetTerminated() bool {
	if x != nil && x.Terminated != nil {
		return *x.Terminated
	}
	return false
}

func (x *Conversation) GetCreatedAt() uint64 {
	if x != nil && x.CreatedAt != nil {
		return *x.CreatedAt
	}
	return 0
}

func (x *Conversation) GetCreatedBy() string {
	if x != nil && x.CreatedBy != nil {
		return *x.CreatedBy
	}
	return ""
}

func (x *Conversation) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *Conversation) GetSupport() bool {
	if x != nil && x.Support != nil {
		return *x.Support
	}
	return false
}

func (x *Conversation) GetIsParentGroup() bool {
	if x != nil && x.IsParentGroup != nil {
		return *x.IsParentGroup
	}
	return false
}

func (x *Conversation) GetParentGroupID() string {
	if x != nil && x.ParentGroupID != nil {
		return *x.ParentGroupID
	}
	return ""
}

func (x *Conversation) GetIsDefaultSubgroup() bool {
	if x != nil && x.IsDefaultSubgroup != nil {
		return *x.IsDefaultSubgroup
	}
	return false
}

func (x *Conversation) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *Conversation) GetPnJID() string {
	if x != nil && x.PnJID != nil {
		return *x.PnJID
	}
	return ""
}

func (x *Conversation) GetShareOwnPn() bool {
	if x != nil && x.ShareOwnPn != nil {
		return *x.ShareOwnPn
	}
	return false
}

func (x *Conversation) GetPnhDuplicateLidThread() bool {
	if x != nil && x.PnhDuplicateLidThread != nil {
		return *x.PnhDuplicateLidThread
	}
	return false
}

func (x *Conversation) GetLidJID() string {
	if x != nil && x.LidJID != nil {
		return *x.LidJID
	}
	return ""
}

func (x *Conversation) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *Conversation) GetLidOriginType() string {
	if x != nil && x.LidOriginType != nil {
		return *x.LidOriginType
	}
	return ""
}

func (x *Conversation) GetCommentsCount() uint32 {
	if x != nil && x.CommentsCount != nil {
		return *x.CommentsCount
	}
	return 0
}

func (x *Conversation) GetLocked() bool {
	if x != nil && x.Locked != nil {
		return *x.Locked
	}
	return false
}

func (x *Conversation) GetSystemMessageToInsert() PrivacySystemMessage {
	if x != nil && x.SystemMessageToInsert != nil {
		return *x.SystemMessageToInsert
	}
	return PrivacySystemMessage_E2EE_MSG
}

func (x *Conversation) GetCapiCreatedGroup() bool {
	if x != nil && x.CapiCreatedGroup != nil {
		return *x.CapiCreatedGroup
	}
	return false
}

func (x *Conversation) GetAccountLid() string {
	if x != nil && x.AccountLid != nil {
		return *x.AccountLid
	}
	return ""
}

func (x *Conversation) GetLimitSharing() bool {
	if x != nil && x.LimitSharing != nil {
		return *x.LimitSharing
	}
	return false
}

func (x *Conversation) GetLimitSharingSettingTimestamp() int64 {
	if x != nil && x.LimitSharingSettingTimestamp != nil {
		return *x.LimitSharingSettingTimestamp
	}
	return 0
}

func (x *Conversation) GetLimitSharingTrigger() waCommon.LimitSharing_Trigger {
	if x != nil && x.LimitSharingTrigger != nil {
		return *x.LimitSharingTrigger
	}
	return waCommon.LimitSharing_Trigger(0)
}

func (x *Conversation) GetLimitSharingInitiatedByMe() bool {
	if x != nil && x.LimitSharingInitiatedByMe != nil {
		return *x.LimitSharingInitiatedByMe
	}
	return false
}

type GroupParticipant struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserJID       *string                `protobuf:"bytes,1,req,name=userJID" json:"userJID,omitempty"`
	Rank          *GroupParticipant_Rank `protobuf:"varint,2,opt,name=rank,enum=WAWebProtobufsHistorySync.GroupParticipant_Rank" json:"rank,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroupParticipant) Reset() {
	*x = GroupParticipant{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupParticipant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupParticipant) ProtoMessage() {}

func (x *GroupParticipant) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupParticipant.ProtoReflect.Descriptor instead.
func (*GroupParticipant) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{2}
}

func (x *GroupParticipant) GetUserJID() string {
	if x != nil && x.UserJID != nil {
		return *x.UserJID
	}
	return ""
}

func (x *GroupParticipant) GetRank() GroupParticipant_Rank {
	if x != nil && x.Rank != nil {
		return *x.Rank
	}
	return GroupParticipant_REGULAR
}

type PastParticipant struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	UserJID       *string                      `protobuf:"bytes,1,opt,name=userJID" json:"userJID,omitempty"`
	LeaveReason   *PastParticipant_LeaveReason `protobuf:"varint,2,opt,name=leaveReason,enum=WAWebProtobufsHistorySync.PastParticipant_LeaveReason" json:"leaveReason,omitempty"`
	LeaveTS       *uint64                      `protobuf:"varint,3,opt,name=leaveTS" json:"leaveTS,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PastParticipant) Reset() {
	*x = PastParticipant{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PastParticipant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PastParticipant) ProtoMessage() {}

func (x *PastParticipant) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PastParticipant.ProtoReflect.Descriptor instead.
func (*PastParticipant) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{3}
}

func (x *PastParticipant) GetUserJID() string {
	if x != nil && x.UserJID != nil {
		return *x.UserJID
	}
	return ""
}

func (x *PastParticipant) GetLeaveReason() PastParticipant_LeaveReason {
	if x != nil && x.LeaveReason != nil {
		return *x.LeaveReason
	}
	return PastParticipant_LEFT
}

func (x *PastParticipant) GetLeaveTS() uint64 {
	if x != nil && x.LeaveTS != nil {
		return *x.LeaveTS
	}
	return 0
}

type PhoneNumberToLIDMapping struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PnJID         *string                `protobuf:"bytes,1,opt,name=pnJID" json:"pnJID,omitempty"`
	LidJID        *string                `protobuf:"bytes,2,opt,name=lidJID" json:"lidJID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PhoneNumberToLIDMapping) Reset() {
	*x = PhoneNumberToLIDMapping{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PhoneNumberToLIDMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneNumberToLIDMapping) ProtoMessage() {}

func (x *PhoneNumberToLIDMapping) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneNumberToLIDMapping.ProtoReflect.Descriptor instead.
func (*PhoneNumberToLIDMapping) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{4}
}

func (x *PhoneNumberToLIDMapping) GetPnJID() string {
	if x != nil && x.PnJID != nil {
		return *x.PnJID
	}
	return ""
}

func (x *PhoneNumberToLIDMapping) GetLidJID() string {
	if x != nil && x.LidJID != nil {
		return *x.LidJID
	}
	return ""
}

type Account struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Lid               *string                `protobuf:"bytes,1,opt,name=lid" json:"lid,omitempty"`
	Username          *string                `protobuf:"bytes,2,opt,name=username" json:"username,omitempty"`
	CountryCode       *string                `protobuf:"bytes,3,opt,name=countryCode" json:"countryCode,omitempty"`
	IsUsernameDeleted *bool                  `protobuf:"varint,4,opt,name=isUsernameDeleted" json:"isUsernameDeleted,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Account) Reset() {
	*x = Account{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{5}
}

func (x *Account) GetLid() string {
	if x != nil && x.Lid != nil {
		return *x.Lid
	}
	return ""
}

func (x *Account) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *Account) GetCountryCode() string {
	if x != nil && x.CountryCode != nil {
		return *x.CountryCode
	}
	return ""
}

func (x *Account) GetIsUsernameDeleted() bool {
	if x != nil && x.IsUsernameDeleted != nil {
		return *x.IsUsernameDeleted
	}
	return false
}

type HistorySyncMsg struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       *waWeb.WebMessageInfo  `protobuf:"bytes,1,opt,name=message" json:"message,omitempty"`
	MsgOrderID    *uint64                `protobuf:"varint,2,opt,name=msgOrderID" json:"msgOrderID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistorySyncMsg) Reset() {
	*x = HistorySyncMsg{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistorySyncMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistorySyncMsg) ProtoMessage() {}

func (x *HistorySyncMsg) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistorySyncMsg.ProtoReflect.Descriptor instead.
func (*HistorySyncMsg) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{6}
}

func (x *HistorySyncMsg) GetMessage() *waWeb.WebMessageInfo {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *HistorySyncMsg) GetMsgOrderID() uint64 {
	if x != nil && x.MsgOrderID != nil {
		return *x.MsgOrderID
	}
	return 0
}

type Pushname struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ID            *string                `protobuf:"bytes,1,opt,name=ID" json:"ID,omitempty"`
	Pushname      *string                `protobuf:"bytes,2,opt,name=pushname" json:"pushname,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pushname) Reset() {
	*x = Pushname{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pushname) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pushname) ProtoMessage() {}

func (x *Pushname) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pushname.ProtoReflect.Descriptor instead.
func (*Pushname) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{7}
}

func (x *Pushname) GetID() string {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return ""
}

func (x *Pushname) GetPushname() string {
	if x != nil && x.Pushname != nil {
		return *x.Pushname
	}
	return ""
}

type WallpaperSettings struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Filename      *string                `protobuf:"bytes,1,opt,name=filename" json:"filename,omitempty"`
	Opacity       *uint32                `protobuf:"varint,2,opt,name=opacity" json:"opacity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WallpaperSettings) Reset() {
	*x = WallpaperSettings{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WallpaperSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WallpaperSettings) ProtoMessage() {}

func (x *WallpaperSettings) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WallpaperSettings.ProtoReflect.Descriptor instead.
func (*WallpaperSettings) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{8}
}

func (x *WallpaperSettings) GetFilename() string {
	if x != nil && x.Filename != nil {
		return *x.Filename
	}
	return ""
}

func (x *WallpaperSettings) GetOpacity() uint32 {
	if x != nil && x.Opacity != nil {
		return *x.Opacity
	}
	return 0
}

type GlobalSettings struct {
	state                              protoimpl.MessageState               `protogen:"open.v1"`
	LightThemeWallpaper                *WallpaperSettings                   `protobuf:"bytes,1,opt,name=lightThemeWallpaper" json:"lightThemeWallpaper,omitempty"`
	MediaVisibility                    *MediaVisibility                     `protobuf:"varint,2,opt,name=mediaVisibility,enum=WAWebProtobufsHistorySync.MediaVisibility" json:"mediaVisibility,omitempty"`
	DarkThemeWallpaper                 *WallpaperSettings                   `protobuf:"bytes,3,opt,name=darkThemeWallpaper" json:"darkThemeWallpaper,omitempty"`
	AutoDownloadWiFi                   *AutoDownloadSettings                `protobuf:"bytes,4,opt,name=autoDownloadWiFi" json:"autoDownloadWiFi,omitempty"`
	AutoDownloadCellular               *AutoDownloadSettings                `protobuf:"bytes,5,opt,name=autoDownloadCellular" json:"autoDownloadCellular,omitempty"`
	AutoDownloadRoaming                *AutoDownloadSettings                `protobuf:"bytes,6,opt,name=autoDownloadRoaming" json:"autoDownloadRoaming,omitempty"`
	ShowIndividualNotificationsPreview *bool                                `protobuf:"varint,7,opt,name=showIndividualNotificationsPreview" json:"showIndividualNotificationsPreview,omitempty"`
	ShowGroupNotificationsPreview      *bool                                `protobuf:"varint,8,opt,name=showGroupNotificationsPreview" json:"showGroupNotificationsPreview,omitempty"`
	DisappearingModeDuration           *int32                               `protobuf:"varint,9,opt,name=disappearingModeDuration" json:"disappearingModeDuration,omitempty"`
	DisappearingModeTimestamp          *int64                               `protobuf:"varint,10,opt,name=disappearingModeTimestamp" json:"disappearingModeTimestamp,omitempty"`
	AvatarUserSettings                 *AvatarUserSettings                  `protobuf:"bytes,11,opt,name=avatarUserSettings" json:"avatarUserSettings,omitempty"`
	FontSize                           *int32                               `protobuf:"varint,12,opt,name=fontSize" json:"fontSize,omitempty"`
	SecurityNotifications              *bool                                `protobuf:"varint,13,opt,name=securityNotifications" json:"securityNotifications,omitempty"`
	AutoUnarchiveChats                 *bool                                `protobuf:"varint,14,opt,name=autoUnarchiveChats" json:"autoUnarchiveChats,omitempty"`
	VideoQualityMode                   *int32                               `protobuf:"varint,15,opt,name=videoQualityMode" json:"videoQualityMode,omitempty"`
	PhotoQualityMode                   *int32                               `protobuf:"varint,16,opt,name=photoQualityMode" json:"photoQualityMode,omitempty"`
	IndividualNotificationSettings     *NotificationSettings                `protobuf:"bytes,17,opt,name=individualNotificationSettings" json:"individualNotificationSettings,omitempty"`
	GroupNotificationSettings          *NotificationSettings                `protobuf:"bytes,18,opt,name=groupNotificationSettings" json:"groupNotificationSettings,omitempty"`
	ChatLockSettings                   *waChatLockSettings.ChatLockSettings `protobuf:"bytes,19,opt,name=chatLockSettings" json:"chatLockSettings,omitempty"`
	ChatDbLidMigrationTimestamp        *int64                               `protobuf:"varint,20,opt,name=chatDbLidMigrationTimestamp" json:"chatDbLidMigrationTimestamp,omitempty"`
	unknownFields                      protoimpl.UnknownFields
	sizeCache                          protoimpl.SizeCache
}

func (x *GlobalSettings) Reset() {
	*x = GlobalSettings{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GlobalSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GlobalSettings) ProtoMessage() {}

func (x *GlobalSettings) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GlobalSettings.ProtoReflect.Descriptor instead.
func (*GlobalSettings) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{9}
}

func (x *GlobalSettings) GetLightThemeWallpaper() *WallpaperSettings {
	if x != nil {
		return x.LightThemeWallpaper
	}
	return nil
}

func (x *GlobalSettings) GetMediaVisibility() MediaVisibility {
	if x != nil && x.MediaVisibility != nil {
		return *x.MediaVisibility
	}
	return MediaVisibility_DEFAULT
}

func (x *GlobalSettings) GetDarkThemeWallpaper() *WallpaperSettings {
	if x != nil {
		return x.DarkThemeWallpaper
	}
	return nil
}

func (x *GlobalSettings) GetAutoDownloadWiFi() *AutoDownloadSettings {
	if x != nil {
		return x.AutoDownloadWiFi
	}
	return nil
}

func (x *GlobalSettings) GetAutoDownloadCellular() *AutoDownloadSettings {
	if x != nil {
		return x.AutoDownloadCellular
	}
	return nil
}

func (x *GlobalSettings) GetAutoDownloadRoaming() *AutoDownloadSettings {
	if x != nil {
		return x.AutoDownloadRoaming
	}
	return nil
}

func (x *GlobalSettings) GetShowIndividualNotificationsPreview() bool {
	if x != nil && x.ShowIndividualNotificationsPreview != nil {
		return *x.ShowIndividualNotificationsPreview
	}
	return false
}

func (x *GlobalSettings) GetShowGroupNotificationsPreview() bool {
	if x != nil && x.ShowGroupNotificationsPreview != nil {
		return *x.ShowGroupNotificationsPreview
	}
	return false
}

func (x *GlobalSettings) GetDisappearingModeDuration() int32 {
	if x != nil && x.DisappearingModeDuration != nil {
		return *x.DisappearingModeDuration
	}
	return 0
}

func (x *GlobalSettings) GetDisappearingModeTimestamp() int64 {
	if x != nil && x.DisappearingModeTimestamp != nil {
		return *x.DisappearingModeTimestamp
	}
	return 0
}

func (x *GlobalSettings) GetAvatarUserSettings() *AvatarUserSettings {
	if x != nil {
		return x.AvatarUserSettings
	}
	return nil
}

func (x *GlobalSettings) GetFontSize() int32 {
	if x != nil && x.FontSize != nil {
		return *x.FontSize
	}
	return 0
}

func (x *GlobalSettings) GetSecurityNotifications() bool {
	if x != nil && x.SecurityNotifications != nil {
		return *x.SecurityNotifications
	}
	return false
}

func (x *GlobalSettings) GetAutoUnarchiveChats() bool {
	if x != nil && x.AutoUnarchiveChats != nil {
		return *x.AutoUnarchiveChats
	}
	return false
}

func (x *GlobalSettings) GetVideoQualityMode() int32 {
	if x != nil && x.VideoQualityMode != nil {
		return *x.VideoQualityMode
	}
	return 0
}

func (x *GlobalSettings) GetPhotoQualityMode() int32 {
	if x != nil && x.PhotoQualityMode != nil {
		return *x.PhotoQualityMode
	}
	return 0
}

func (x *GlobalSettings) GetIndividualNotificationSettings() *NotificationSettings {
	if x != nil {
		return x.IndividualNotificationSettings
	}
	return nil
}

func (x *GlobalSettings) GetGroupNotificationSettings() *NotificationSettings {
	if x != nil {
		return x.GroupNotificationSettings
	}
	return nil
}

func (x *GlobalSettings) GetChatLockSettings() *waChatLockSettings.ChatLockSettings {
	if x != nil {
		return x.ChatLockSettings
	}
	return nil
}

func (x *GlobalSettings) GetChatDbLidMigrationTimestamp() int64 {
	if x != nil && x.ChatDbLidMigrationTimestamp != nil {
		return *x.ChatDbLidMigrationTimestamp
	}
	return 0
}

type AutoDownloadSettings struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	DownloadImages    *bool                  `protobuf:"varint,1,opt,name=downloadImages" json:"downloadImages,omitempty"`
	DownloadAudio     *bool                  `protobuf:"varint,2,opt,name=downloadAudio" json:"downloadAudio,omitempty"`
	DownloadVideo     *bool                  `protobuf:"varint,3,opt,name=downloadVideo" json:"downloadVideo,omitempty"`
	DownloadDocuments *bool                  `protobuf:"varint,4,opt,name=downloadDocuments" json:"downloadDocuments,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AutoDownloadSettings) Reset() {
	*x = AutoDownloadSettings{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AutoDownloadSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoDownloadSettings) ProtoMessage() {}

func (x *AutoDownloadSettings) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoDownloadSettings.ProtoReflect.Descriptor instead.
func (*AutoDownloadSettings) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{10}
}

func (x *AutoDownloadSettings) GetDownloadImages() bool {
	if x != nil && x.DownloadImages != nil {
		return *x.DownloadImages
	}
	return false
}

func (x *AutoDownloadSettings) GetDownloadAudio() bool {
	if x != nil && x.DownloadAudio != nil {
		return *x.DownloadAudio
	}
	return false
}

func (x *AutoDownloadSettings) GetDownloadVideo() bool {
	if x != nil && x.DownloadVideo != nil {
		return *x.DownloadVideo
	}
	return false
}

func (x *AutoDownloadSettings) GetDownloadDocuments() bool {
	if x != nil && x.DownloadDocuments != nil {
		return *x.DownloadDocuments
	}
	return false
}

type StickerMetadata struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	URL               *string                `protobuf:"bytes,1,opt,name=URL" json:"URL,omitempty"`
	FileSHA256        []byte                 `protobuf:"bytes,2,opt,name=fileSHA256" json:"fileSHA256,omitempty"`
	FileEncSHA256     []byte                 `protobuf:"bytes,3,opt,name=fileEncSHA256" json:"fileEncSHA256,omitempty"`
	MediaKey          []byte                 `protobuf:"bytes,4,opt,name=mediaKey" json:"mediaKey,omitempty"`
	Mimetype          *string                `protobuf:"bytes,5,opt,name=mimetype" json:"mimetype,omitempty"`
	Height            *uint32                `protobuf:"varint,6,opt,name=height" json:"height,omitempty"`
	Width             *uint32                `protobuf:"varint,7,opt,name=width" json:"width,omitempty"`
	DirectPath        *string                `protobuf:"bytes,8,opt,name=directPath" json:"directPath,omitempty"`
	FileLength        *uint64                `protobuf:"varint,9,opt,name=fileLength" json:"fileLength,omitempty"`
	Weight            *float32               `protobuf:"fixed32,10,opt,name=weight" json:"weight,omitempty"`
	LastStickerSentTS *int64                 `protobuf:"varint,11,opt,name=lastStickerSentTS" json:"lastStickerSentTS,omitempty"`
	IsLottie          *bool                  `protobuf:"varint,12,opt,name=isLottie" json:"isLottie,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StickerMetadata) Reset() {
	*x = StickerMetadata{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerMetadata) ProtoMessage() {}

func (x *StickerMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerMetadata.ProtoReflect.Descriptor instead.
func (*StickerMetadata) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{11}
}

func (x *StickerMetadata) GetURL() string {
	if x != nil && x.URL != nil {
		return *x.URL
	}
	return ""
}

func (x *StickerMetadata) GetFileSHA256() []byte {
	if x != nil {
		return x.FileSHA256
	}
	return nil
}

func (x *StickerMetadata) GetFileEncSHA256() []byte {
	if x != nil {
		return x.FileEncSHA256
	}
	return nil
}

func (x *StickerMetadata) GetMediaKey() []byte {
	if x != nil {
		return x.MediaKey
	}
	return nil
}

func (x *StickerMetadata) GetMimetype() string {
	if x != nil && x.Mimetype != nil {
		return *x.Mimetype
	}
	return ""
}

func (x *StickerMetadata) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *StickerMetadata) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *StickerMetadata) GetDirectPath() string {
	if x != nil && x.DirectPath != nil {
		return *x.DirectPath
	}
	return ""
}

func (x *StickerMetadata) GetFileLength() uint64 {
	if x != nil && x.FileLength != nil {
		return *x.FileLength
	}
	return 0
}

func (x *StickerMetadata) GetWeight() float32 {
	if x != nil && x.Weight != nil {
		return *x.Weight
	}
	return 0
}

func (x *StickerMetadata) GetLastStickerSentTS() int64 {
	if x != nil && x.LastStickerSentTS != nil {
		return *x.LastStickerSentTS
	}
	return 0
}

func (x *StickerMetadata) GetIsLottie() bool {
	if x != nil && x.IsLottie != nil {
		return *x.IsLottie
	}
	return false
}

type PastParticipants struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	GroupJID         *string                `protobuf:"bytes,1,opt,name=groupJID" json:"groupJID,omitempty"`
	PastParticipants []*PastParticipant     `protobuf:"bytes,2,rep,name=pastParticipants" json:"pastParticipants,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PastParticipants) Reset() {
	*x = PastParticipants{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PastParticipants) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PastParticipants) ProtoMessage() {}

func (x *PastParticipants) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PastParticipants.ProtoReflect.Descriptor instead.
func (*PastParticipants) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{12}
}

func (x *PastParticipants) GetGroupJID() string {
	if x != nil && x.GroupJID != nil {
		return *x.GroupJID
	}
	return ""
}

func (x *PastParticipants) GetPastParticipants() []*PastParticipant {
	if x != nil {
		return x.PastParticipants
	}
	return nil
}

type AvatarUserSettings struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FBID          *string                `protobuf:"bytes,1,opt,name=FBID" json:"FBID,omitempty"`
	Password      *string                `protobuf:"bytes,2,opt,name=password" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvatarUserSettings) Reset() {
	*x = AvatarUserSettings{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvatarUserSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvatarUserSettings) ProtoMessage() {}

func (x *AvatarUserSettings) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvatarUserSettings.ProtoReflect.Descriptor instead.
func (*AvatarUserSettings) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{13}
}

func (x *AvatarUserSettings) GetFBID() string {
	if x != nil && x.FBID != nil {
		return *x.FBID
	}
	return ""
}

func (x *AvatarUserSettings) GetPassword() string {
	if x != nil && x.Password != nil {
		return *x.Password
	}
	return ""
}

type NotificationSettings struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	MessageVibrate           *string                `protobuf:"bytes,1,opt,name=messageVibrate" json:"messageVibrate,omitempty"`
	MessagePopup             *string                `protobuf:"bytes,2,opt,name=messagePopup" json:"messagePopup,omitempty"`
	MessageLight             *string                `protobuf:"bytes,3,opt,name=messageLight" json:"messageLight,omitempty"`
	LowPriorityNotifications *bool                  `protobuf:"varint,4,opt,name=lowPriorityNotifications" json:"lowPriorityNotifications,omitempty"`
	ReactionsMuted           *bool                  `protobuf:"varint,5,opt,name=reactionsMuted" json:"reactionsMuted,omitempty"`
	CallVibrate              *string                `protobuf:"bytes,6,opt,name=callVibrate" json:"callVibrate,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *NotificationSettings) Reset() {
	*x = NotificationSettings{}
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationSettings) ProtoMessage() {}

func (x *NotificationSettings) ProtoReflect() protoreflect.Message {
	mi := &file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationSettings.ProtoReflect.Descriptor instead.
func (*NotificationSettings) Descriptor() ([]byte, []int) {
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP(), []int{14}
}

func (x *NotificationSettings) GetMessageVibrate() string {
	if x != nil && x.MessageVibrate != nil {
		return *x.MessageVibrate
	}
	return ""
}

func (x *NotificationSettings) GetMessagePopup() string {
	if x != nil && x.MessagePopup != nil {
		return *x.MessagePopup
	}
	return ""
}

func (x *NotificationSettings) GetMessageLight() string {
	if x != nil && x.MessageLight != nil {
		return *x.MessageLight
	}
	return ""
}

func (x *NotificationSettings) GetLowPriorityNotifications() bool {
	if x != nil && x.LowPriorityNotifications != nil {
		return *x.LowPriorityNotifications
	}
	return false
}

func (x *NotificationSettings) GetReactionsMuted() bool {
	if x != nil && x.ReactionsMuted != nil {
		return *x.ReactionsMuted
	}
	return false
}

func (x *NotificationSettings) GetCallVibrate() string {
	if x != nil && x.CallVibrate != nil {
		return *x.CallVibrate
	}
	return ""
}

var File_waHistorySync_WAWebProtobufsHistorySync_proto protoreflect.FileDescriptor

const file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDesc = "" +
	"\n" +
	"-waHistorySync/WAWebProtobufsHistorySync.proto\x12\x19WAWebProtobufsHistorySync\x1a\x1fwaSyncAction/WASyncAction.proto\x1a4waChatLockSettings/WAProtobufsChatLockSettings.proto\x1a\x1dwaE2E/WAWebProtobufsE2E.proto\x1a\x17waCommon/WACommon.proto\x1a\x1dwaWeb/WAWebProtobufsWeb.proto\"\x8c\v\n" +
	"\vHistorySync\x12R\n" +
	"\bsyncType\x18\x01 \x02(\x0e26.WAWebProtobufsHistorySync.HistorySync.HistorySyncTypeR\bsyncType\x12M\n" +
	"\rconversations\x18\x02 \x03(\v2'.WAWebProtobufsHistorySync.ConversationR\rconversations\x12M\n" +
	"\x10statusV3Messages\x18\x03 \x03(\v2!.WAWebProtobufsWeb.WebMessageInfoR\x10statusV3Messages\x12\x1e\n" +
	"\n" +
	"chunkOrder\x18\x05 \x01(\rR\n" +
	"chunkOrder\x12\x1a\n" +
	"\bprogress\x18\x06 \x01(\rR\bprogress\x12A\n" +
	"\tpushnames\x18\a \x03(\v2#.WAWebProtobufsHistorySync.PushnameR\tpushnames\x12Q\n" +
	"\x0eglobalSettings\x18\b \x01(\v2).WAWebProtobufsHistorySync.GlobalSettingsR\x0eglobalSettings\x12.\n" +
	"\x12threadIDUserSecret\x18\t \x01(\fR\x12threadIDUserSecret\x128\n" +
	"\x17threadDsTimeframeOffset\x18\n" +
	" \x01(\rR\x17threadDsTimeframeOffset\x12R\n" +
	"\x0erecentStickers\x18\v \x03(\v2*.WAWebProtobufsHistorySync.StickerMetadataR\x0erecentStickers\x12W\n" +
	"\x10pastParticipants\x18\f \x03(\v2+.WAWebProtobufsHistorySync.PastParticipantsR\x10pastParticipants\x12C\n" +
	"\x0ecallLogRecords\x18\r \x03(\v2\x1b.WASyncAction.CallLogRecordR\x0ecallLogRecords\x12c\n" +
	"\x0faiWaitListState\x18\x0e \x01(\x0e29.WAWebProtobufsHistorySync.HistorySync.BotAIWaitListStateR\x0faiWaitListState\x12n\n" +
	"\x18phoneNumberToLidMappings\x18\x0f \x03(\v22.WAWebProtobufsHistorySync.PhoneNumberToLIDMappingR\x18phoneNumberToLidMappings\x12.\n" +
	"\x12companionMetaNonce\x18\x10 \x01(\tR\x12companionMetaNonce\x12R\n" +
	"$shareableChatIdentifierEncryptionKey\x18\x11 \x01(\fR$shareableChatIdentifierEncryptionKey\x12>\n" +
	"\baccounts\x18\x12 \x03(\v2\".WAWebProtobufsHistorySync.AccountR\baccounts\"7\n" +
	"\x12BotAIWaitListState\x12\x0f\n" +
	"\vIN_WAITLIST\x10\x00\x12\x10\n" +
	"\fAI_AVAILABLE\x10\x01\"\x8a\x01\n" +
	"\x0fHistorySyncType\x12\x15\n" +
	"\x11INITIAL_BOOTSTRAP\x10\x00\x12\x15\n" +
	"\x11INITIAL_STATUS_V3\x10\x01\x12\b\n" +
	"\x04FULL\x10\x02\x12\n" +
	"\n" +
	"\x06RECENT\x10\x03\x12\r\n" +
	"\tPUSH_NAME\x10\x04\x12\x15\n" +
	"\x11NON_BLOCKING_DATA\x10\x05\x12\r\n" +
	"\tON_DEMAND\x10\x06\"\x87\x14\n" +
	"\fConversation\x12\x0e\n" +
	"\x02ID\x18\x01 \x02(\tR\x02ID\x12E\n" +
	"\bmessages\x18\x02 \x03(\v2).WAWebProtobufsHistorySync.HistorySyncMsgR\bmessages\x12\x16\n" +
	"\x06newJID\x18\x03 \x01(\tR\x06newJID\x12\x16\n" +
	"\x06oldJID\x18\x04 \x01(\tR\x06oldJID\x12*\n" +
	"\x10lastMsgTimestamp\x18\x05 \x01(\x04R\x10lastMsgTimestamp\x12 \n" +
	"\vunreadCount\x18\x06 \x01(\rR\vunreadCount\x12\x1a\n" +
	"\breadOnly\x18\a \x01(\bR\breadOnly\x122\n" +
	"\x14endOfHistoryTransfer\x18\b \x01(\bR\x14endOfHistoryTransfer\x120\n" +
	"\x13ephemeralExpiration\x18\t \x01(\rR\x13ephemeralExpiration\x12<\n" +
	"\x19ephemeralSettingTimestamp\x18\n" +
	" \x01(\x03R\x19ephemeralSettingTimestamp\x12|\n" +
	"\x18endOfHistoryTransferType\x18\v \x01(\<EMAIL>\x18endOfHistoryTransferType\x124\n" +
	"\x15conversationTimestamp\x18\f \x01(\x04R\x15conversationTimestamp\x12\x12\n" +
	"\x04name\x18\r \x01(\tR\x04name\x12\x14\n" +
	"\x05pHash\x18\x0e \x01(\tR\x05pHash\x12\x18\n" +
	"\anotSpam\x18\x0f \x01(\bR\anotSpam\x12\x1a\n" +
	"\barchived\x18\x10 \x01(\bR\barchived\x12O\n" +
	"\x10disappearingMode\x18\x11 \x01(\v2#.WAWebProtobufsE2E.DisappearingModeR\x10disappearingMode\x12.\n" +
	"\x12unreadMentionCount\x18\x12 \x01(\rR\x12unreadMentionCount\x12&\n" +
	"\x0emarkedAsUnread\x18\x13 \x01(\bR\x0emarkedAsUnread\x12M\n" +
	"\vparticipant\x18\x14 \x03(\v2+.WAWebProtobufsHistorySync.GroupParticipantR\vparticipant\x12\x18\n" +
	"\atcToken\x18\x15 \x01(\fR\atcToken\x12*\n" +
	"\x10tcTokenTimestamp\x18\x16 \x01(\x04R\x10tcTokenTimestamp\x12<\n" +
	"\x19contactPrimaryIdentityKey\x18\x17 \x01(\fR\x19contactPrimaryIdentityKey\x12\x16\n" +
	"\x06pinned\x18\x18 \x01(\rR\x06pinned\x12 \n" +
	"\vmuteEndTime\x18\x19 \x01(\x04R\vmuteEndTime\x12J\n" +
	"\twallpaper\x18\x1a \x01(\v2,.WAWebProtobufsHistorySync.WallpaperSettingsR\twallpaper\x12T\n" +
	"\x0fmediaVisibility\x18\x1b \x01(\x0e2*.WAWebProtobufsHistorySync.MediaVisibilityR\x0fmediaVisibility\x126\n" +
	"\x16tcTokenSenderTimestamp\x18\x1c \x01(\x04R\x16tcTokenSenderTimestamp\x12\x1c\n" +
	"\tsuspended\x18\x1d \x01(\bR\tsuspended\x12\x1e\n" +
	"\n" +
	"terminated\x18\x1e \x01(\bR\n" +
	"terminated\x12\x1c\n" +
	"\tcreatedAt\x18\x1f \x01(\x04R\tcreatedAt\x12\x1c\n" +
	"\tcreatedBy\x18  \x01(\tR\tcreatedBy\x12 \n" +
	"\vdescription\x18! \x01(\tR\vdescription\x12\x18\n" +
	"\asupport\x18\" \x01(\bR\asupport\x12$\n" +
	"\risParentGroup\x18# \x01(\bR\risParentGroup\x12$\n" +
	"\rparentGroupID\x18% \x01(\tR\rparentGroupID\x12,\n" +
	"\x11isDefaultSubgroup\x18$ \x01(\bR\x11isDefaultSubgroup\x12 \n" +
	"\vdisplayName\x18& \x01(\tR\vdisplayName\x12\x14\n" +
	"\x05pnJID\x18' \x01(\tR\x05pnJID\x12\x1e\n" +
	"\n" +
	"shareOwnPn\x18( \x01(\bR\n" +
	"shareOwnPn\x124\n" +
	"\x15pnhDuplicateLidThread\x18) \x01(\bR\x15pnhDuplicateLidThread\x12\x16\n" +
	"\x06lidJID\x18* \x01(\tR\x06lidJID\x12\x1a\n" +
	"\busername\x18+ \x01(\tR\busername\x12$\n" +
	"\rlidOriginType\x18, \x01(\tR\rlidOriginType\x12$\n" +
	"\rcommentsCount\x18- \x01(\rR\rcommentsCount\x12\x16\n" +
	"\x06locked\x18. \x01(\bR\x06locked\x12e\n" +
	"\x15systemMessageToInsert\x18/ \x01(\x0e2/.WAWebProtobufsHistorySync.PrivacySystemMessageR\x15systemMessageToInsert\x12*\n" +
	"\x10capiCreatedGroup\x180 \x01(\bR\x10capiCreatedGroup\x12\x1e\n" +
	"\n" +
	"accountLid\x181 \x01(\tR\n" +
	"accountLid\x12\"\n" +
	"\flimitSharing\x182 \x01(\bR\flimitSharing\x12B\n" +
	"\x1climitSharingSettingTimestamp\x183 \x01(\x03R\x1climitSharingSettingTimestamp\x12P\n" +
	"\x13limitSharingTrigger\x184 \x01(\x0e2\x1e.WACommon.LimitSharing.TriggerR\x13limitSharingTrigger\x12<\n" +
	"\x19limitSharingInitiatedByMe\x185 \x01(\bR\x19limitSharingInitiatedByMe\"\xbc\x01\n" +
	"\x18EndOfHistoryTransferType\x120\n" +
	",COMPLETE_BUT_MORE_MESSAGES_REMAIN_ON_PRIMARY\x10\x00\x122\n" +
	".COMPLETE_AND_NO_MORE_MESSAGE_REMAIN_ON_PRIMARY\x10\x01\x12:\n" +
	"6COMPLETE_ON_DEMAND_SYNC_BUT_MORE_MSG_REMAIN_ON_PRIMARY\x10\x02\"\xa2\x01\n" +
	"\x10GroupParticipant\x12\x18\n" +
	"\auserJID\x18\x01 \x02(\tR\auserJID\x12D\n" +
	"\x04rank\x18\x02 \x01(\x0e20.WAWebProtobufsHistorySync.GroupParticipant.RankR\x04rank\".\n" +
	"\x04Rank\x12\v\n" +
	"\aREGULAR\x10\x00\x12\t\n" +
	"\x05ADMIN\x10\x01\x12\x0e\n" +
	"\n" +
	"SUPERADMIN\x10\x02\"\xc5\x01\n" +
	"\x0fPastParticipant\x12\x18\n" +
	"\auserJID\x18\x01 \x01(\tR\auserJID\x12X\n" +
	"\vleaveReason\x18\x02 \x01(\x0e26.WAWebProtobufsHistorySync.PastParticipant.LeaveReasonR\vleaveReason\x12\x18\n" +
	"\aleaveTS\x18\x03 \x01(\x04R\aleaveTS\"$\n" +
	"\vLeaveReason\x12\b\n" +
	"\x04LEFT\x10\x00\x12\v\n" +
	"\aREMOVED\x10\x01\"G\n" +
	"\x17PhoneNumberToLIDMapping\x12\x14\n" +
	"\x05pnJID\x18\x01 \x01(\tR\x05pnJID\x12\x16\n" +
	"\x06lidJID\x18\x02 \x01(\tR\x06lidJID\"\x87\x01\n" +
	"\aAccount\x12\x10\n" +
	"\x03lid\x18\x01 \x01(\tR\x03lid\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12 \n" +
	"\vcountryCode\x18\x03 \x01(\tR\vcountryCode\x12,\n" +
	"\x11isUsernameDeleted\x18\x04 \x01(\bR\x11isUsernameDeleted\"m\n" +
	"\x0eHistorySyncMsg\x12;\n" +
	"\amessage\x18\x01 \x01(\v2!.WAWebProtobufsWeb.WebMessageInfoR\amessage\x12\x1e\n" +
	"\n" +
	"msgOrderID\x18\x02 \x01(\x04R\n" +
	"msgOrderID\"6\n" +
	"\bPushname\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\x12\x1a\n" +
	"\bpushname\x18\x02 \x01(\tR\bpushname\"I\n" +
	"\x11WallpaperSettings\x12\x1a\n" +
	"\bfilename\x18\x01 \x01(\tR\bfilename\x12\x18\n" +
	"\aopacity\x18\x02 \x01(\rR\aopacity\"\x97\f\n" +
	"\x0eGlobalSettings\x12^\n" +
	"\x13lightThemeWallpaper\x18\x01 \x01(\v2,.WAWebProtobufsHistorySync.WallpaperSettingsR\x13lightThemeWallpaper\x12T\n" +
	"\x0fmediaVisibility\x18\x02 \x01(\x0e2*.WAWebProtobufsHistorySync.MediaVisibilityR\x0fmediaVisibility\x12\\\n" +
	"\x12darkThemeWallpaper\x18\x03 \x01(\v2,.WAWebProtobufsHistorySync.WallpaperSettingsR\x12darkThemeWallpaper\x12[\n" +
	"\x10autoDownloadWiFi\x18\x04 \x01(\v2/.WAWebProtobufsHistorySync.AutoDownloadSettingsR\x10autoDownloadWiFi\x12c\n" +
	"\x14autoDownloadCellular\x18\x05 \x01(\v2/.WAWebProtobufsHistorySync.AutoDownloadSettingsR\x14autoDownloadCellular\x12a\n" +
	"\x13autoDownloadRoaming\x18\x06 \x01(\v2/.WAWebProtobufsHistorySync.AutoDownloadSettingsR\x13autoDownloadRoaming\x12N\n" +
	"\"showIndividualNotificationsPreview\x18\a \x01(\bR\"showIndividualNotificationsPreview\x12D\n" +
	"\x1dshowGroupNotificationsPreview\x18\b \x01(\bR\x1dshowGroupNotificationsPreview\x12:\n" +
	"\x18disappearingModeDuration\x18\t \x01(\x05R\x18disappearingModeDuration\x12<\n" +
	"\x19disappearingModeTimestamp\x18\n" +
	" \x01(\x03R\x19disappearingModeTimestamp\x12]\n" +
	"\x12avatarUserSettings\x18\v \x01(\v2-.WAWebProtobufsHistorySync.AvatarUserSettingsR\x12avatarUserSettings\x12\x1a\n" +
	"\bfontSize\x18\f \x01(\x05R\bfontSize\x124\n" +
	"\x15securityNotifications\x18\r \x01(\bR\x15securityNotifications\x12.\n" +
	"\x12autoUnarchiveChats\x18\x0e \x01(\bR\x12autoUnarchiveChats\x12*\n" +
	"\x10videoQualityMode\x18\x0f \x01(\x05R\x10videoQualityMode\x12*\n" +
	"\x10photoQualityMode\x18\x10 \x01(\x05R\x10photoQualityMode\x12w\n" +
	"\x1eindividualNotificationSettings\x18\x11 \x01(\v2/.WAWebProtobufsHistorySync.NotificationSettingsR\x1eindividualNotificationSettings\x12m\n" +
	"\x19groupNotificationSettings\x18\x12 \x01(\v2/.WAWebProtobufsHistorySync.NotificationSettingsR\x19groupNotificationSettings\x12Y\n" +
	"\x10chatLockSettings\x18\x13 \x01(\v2-.WAProtobufsChatLockSettings.ChatLockSettingsR\x10chatLockSettings\x12@\n" +
	"\x1bchatDbLidMigrationTimestamp\x18\x14 \x01(\x03R\x1bchatDbLidMigrationTimestamp\"\xb8\x01\n" +
	"\x14AutoDownloadSettings\x12&\n" +
	"\x0edownloadImages\x18\x01 \x01(\bR\x0edownloadImages\x12$\n" +
	"\rdownloadAudio\x18\x02 \x01(\bR\rdownloadAudio\x12$\n" +
	"\rdownloadVideo\x18\x03 \x01(\bR\rdownloadVideo\x12,\n" +
	"\x11downloadDocuments\x18\x04 \x01(\bR\x11downloadDocuments\"\xf1\x02\n" +
	"\x0fStickerMetadata\x12\x10\n" +
	"\x03URL\x18\x01 \x01(\tR\x03URL\x12\x1e\n" +
	"\n" +
	"fileSHA256\x18\x02 \x01(\fR\n" +
	"fileSHA256\x12$\n" +
	"\rfileEncSHA256\x18\x03 \x01(\fR\rfileEncSHA256\x12\x1a\n" +
	"\bmediaKey\x18\x04 \x01(\fR\bmediaKey\x12\x1a\n" +
	"\bmimetype\x18\x05 \x01(\tR\bmimetype\x12\x16\n" +
	"\x06height\x18\x06 \x01(\rR\x06height\x12\x14\n" +
	"\x05width\x18\a \x01(\rR\x05width\x12\x1e\n" +
	"\n" +
	"directPath\x18\b \x01(\tR\n" +
	"directPath\x12\x1e\n" +
	"\n" +
	"fileLength\x18\t \x01(\x04R\n" +
	"fileLength\x12\x16\n" +
	"\x06weight\x18\n" +
	" \x01(\x02R\x06weight\x12,\n" +
	"\x11lastStickerSentTS\x18\v \x01(\x03R\x11lastStickerSentTS\x12\x1a\n" +
	"\bisLottie\x18\f \x01(\bR\bisLottie\"\x86\x01\n" +
	"\x10PastParticipants\x12\x1a\n" +
	"\bgroupJID\x18\x01 \x01(\tR\bgroupJID\x12V\n" +
	"\x10pastParticipants\x18\x02 \x03(\v2*.WAWebProtobufsHistorySync.PastParticipantR\x10pastParticipants\"D\n" +
	"\x12AvatarUserSettings\x12\x12\n" +
	"\x04FBID\x18\x01 \x01(\tR\x04FBID\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\"\x8c\x02\n" +
	"\x14NotificationSettings\x12&\n" +
	"\x0emessageVibrate\x18\x01 \x01(\tR\x0emessageVibrate\x12\"\n" +
	"\fmessagePopup\x18\x02 \x01(\tR\fmessagePopup\x12\"\n" +
	"\fmessageLight\x18\x03 \x01(\tR\fmessageLight\x12:\n" +
	"\x18lowPriorityNotifications\x18\x04 \x01(\bR\x18lowPriorityNotifications\x12&\n" +
	"\x0ereactionsMuted\x18\x05 \x01(\bR\x0ereactionsMuted\x12 \n" +
	"\vcallVibrate\x18\x06 \x01(\tR\vcallVibrate*/\n" +
	"\x0fMediaVisibility\x12\v\n" +
	"\aDEFAULT\x10\x00\x12\a\n" +
	"\x03OFF\x10\x01\x12\x06\n" +
	"\x02ON\x10\x02*E\n" +
	"\x14PrivacySystemMessage\x12\f\n" +
	"\bE2EE_MSG\x10\x01\x12\x0e\n" +
	"\n" +
	"NE2EE_SELF\x10\x02\x12\x0f\n" +
	"\vNE2EE_OTHER\x10\x03B)Z'go.mau.fi/whatsmeow/proto/waHistorySync"

var (
	file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescOnce sync.Once
	file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescData []byte
)

func file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescGZIP() []byte {
	file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescOnce.Do(func() {
		file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDesc), len(file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDesc)))
	})
	return file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDescData
}

var file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_waHistorySync_WAWebProtobufsHistorySync_proto_goTypes = []any{
	(MediaVisibility)(0),                        // 0: WAWebProtobufsHistorySync.MediaVisibility
	(PrivacySystemMessage)(0),                   // 1: WAWebProtobufsHistorySync.PrivacySystemMessage
	(HistorySync_BotAIWaitListState)(0),         // 2: WAWebProtobufsHistorySync.HistorySync.BotAIWaitListState
	(HistorySync_HistorySyncType)(0),            // 3: WAWebProtobufsHistorySync.HistorySync.HistorySyncType
	(Conversation_EndOfHistoryTransferType)(0),  // 4: WAWebProtobufsHistorySync.Conversation.EndOfHistoryTransferType
	(GroupParticipant_Rank)(0),                  // 5: WAWebProtobufsHistorySync.GroupParticipant.Rank
	(PastParticipant_LeaveReason)(0),            // 6: WAWebProtobufsHistorySync.PastParticipant.LeaveReason
	(*HistorySync)(nil),                         // 7: WAWebProtobufsHistorySync.HistorySync
	(*Conversation)(nil),                        // 8: WAWebProtobufsHistorySync.Conversation
	(*GroupParticipant)(nil),                    // 9: WAWebProtobufsHistorySync.GroupParticipant
	(*PastParticipant)(nil),                     // 10: WAWebProtobufsHistorySync.PastParticipant
	(*PhoneNumberToLIDMapping)(nil),             // 11: WAWebProtobufsHistorySync.PhoneNumberToLIDMapping
	(*Account)(nil),                             // 12: WAWebProtobufsHistorySync.Account
	(*HistorySyncMsg)(nil),                      // 13: WAWebProtobufsHistorySync.HistorySyncMsg
	(*Pushname)(nil),                            // 14: WAWebProtobufsHistorySync.Pushname
	(*WallpaperSettings)(nil),                   // 15: WAWebProtobufsHistorySync.WallpaperSettings
	(*GlobalSettings)(nil),                      // 16: WAWebProtobufsHistorySync.GlobalSettings
	(*AutoDownloadSettings)(nil),                // 17: WAWebProtobufsHistorySync.AutoDownloadSettings
	(*StickerMetadata)(nil),                     // 18: WAWebProtobufsHistorySync.StickerMetadata
	(*PastParticipants)(nil),                    // 19: WAWebProtobufsHistorySync.PastParticipants
	(*AvatarUserSettings)(nil),                  // 20: WAWebProtobufsHistorySync.AvatarUserSettings
	(*NotificationSettings)(nil),                // 21: WAWebProtobufsHistorySync.NotificationSettings
	(*waWeb.WebMessageInfo)(nil),                // 22: WAWebProtobufsWeb.WebMessageInfo
	(*waSyncAction.CallLogRecord)(nil),          // 23: WASyncAction.CallLogRecord
	(*waE2E.DisappearingMode)(nil),              // 24: WAWebProtobufsE2E.DisappearingMode
	(waCommon.LimitSharing_Trigger)(0),          // 25: WACommon.LimitSharing.Trigger
	(*waChatLockSettings.ChatLockSettings)(nil), // 26: WAProtobufsChatLockSettings.ChatLockSettings
}
var file_waHistorySync_WAWebProtobufsHistorySync_proto_depIdxs = []int32{
	3,  // 0: WAWebProtobufsHistorySync.HistorySync.syncType:type_name -> WAWebProtobufsHistorySync.HistorySync.HistorySyncType
	8,  // 1: WAWebProtobufsHistorySync.HistorySync.conversations:type_name -> WAWebProtobufsHistorySync.Conversation
	22, // 2: WAWebProtobufsHistorySync.HistorySync.statusV3Messages:type_name -> WAWebProtobufsWeb.WebMessageInfo
	14, // 3: WAWebProtobufsHistorySync.HistorySync.pushnames:type_name -> WAWebProtobufsHistorySync.Pushname
	16, // 4: WAWebProtobufsHistorySync.HistorySync.globalSettings:type_name -> WAWebProtobufsHistorySync.GlobalSettings
	18, // 5: WAWebProtobufsHistorySync.HistorySync.recentStickers:type_name -> WAWebProtobufsHistorySync.StickerMetadata
	19, // 6: WAWebProtobufsHistorySync.HistorySync.pastParticipants:type_name -> WAWebProtobufsHistorySync.PastParticipants
	23, // 7: WAWebProtobufsHistorySync.HistorySync.callLogRecords:type_name -> WASyncAction.CallLogRecord
	2,  // 8: WAWebProtobufsHistorySync.HistorySync.aiWaitListState:type_name -> WAWebProtobufsHistorySync.HistorySync.BotAIWaitListState
	11, // 9: WAWebProtobufsHistorySync.HistorySync.phoneNumberToLidMappings:type_name -> WAWebProtobufsHistorySync.PhoneNumberToLIDMapping
	12, // 10: WAWebProtobufsHistorySync.HistorySync.accounts:type_name -> WAWebProtobufsHistorySync.Account
	13, // 11: WAWebProtobufsHistorySync.Conversation.messages:type_name -> WAWebProtobufsHistorySync.HistorySyncMsg
	4,  // 12: WAWebProtobufsHistorySync.Conversation.endOfHistoryTransferType:type_name -> WAWebProtobufsHistorySync.Conversation.EndOfHistoryTransferType
	24, // 13: WAWebProtobufsHistorySync.Conversation.disappearingMode:type_name -> WAWebProtobufsE2E.DisappearingMode
	9,  // 14: WAWebProtobufsHistorySync.Conversation.participant:type_name -> WAWebProtobufsHistorySync.GroupParticipant
	15, // 15: WAWebProtobufsHistorySync.Conversation.wallpaper:type_name -> WAWebProtobufsHistorySync.WallpaperSettings
	0,  // 16: WAWebProtobufsHistorySync.Conversation.mediaVisibility:type_name -> WAWebProtobufsHistorySync.MediaVisibility
	1,  // 17: WAWebProtobufsHistorySync.Conversation.systemMessageToInsert:type_name -> WAWebProtobufsHistorySync.PrivacySystemMessage
	25, // 18: WAWebProtobufsHistorySync.Conversation.limitSharingTrigger:type_name -> WACommon.LimitSharing.Trigger
	5,  // 19: WAWebProtobufsHistorySync.GroupParticipant.rank:type_name -> WAWebProtobufsHistorySync.GroupParticipant.Rank
	6,  // 20: WAWebProtobufsHistorySync.PastParticipant.leaveReason:type_name -> WAWebProtobufsHistorySync.PastParticipant.LeaveReason
	22, // 21: WAWebProtobufsHistorySync.HistorySyncMsg.message:type_name -> WAWebProtobufsWeb.WebMessageInfo
	15, // 22: WAWebProtobufsHistorySync.GlobalSettings.lightThemeWallpaper:type_name -> WAWebProtobufsHistorySync.WallpaperSettings
	0,  // 23: WAWebProtobufsHistorySync.GlobalSettings.mediaVisibility:type_name -> WAWebProtobufsHistorySync.MediaVisibility
	15, // 24: WAWebProtobufsHistorySync.GlobalSettings.darkThemeWallpaper:type_name -> WAWebProtobufsHistorySync.WallpaperSettings
	17, // 25: WAWebProtobufsHistorySync.GlobalSettings.autoDownloadWiFi:type_name -> WAWebProtobufsHistorySync.AutoDownloadSettings
	17, // 26: WAWebProtobufsHistorySync.GlobalSettings.autoDownloadCellular:type_name -> WAWebProtobufsHistorySync.AutoDownloadSettings
	17, // 27: WAWebProtobufsHistorySync.GlobalSettings.autoDownloadRoaming:type_name -> WAWebProtobufsHistorySync.AutoDownloadSettings
	20, // 28: WAWebProtobufsHistorySync.GlobalSettings.avatarUserSettings:type_name -> WAWebProtobufsHistorySync.AvatarUserSettings
	21, // 29: WAWebProtobufsHistorySync.GlobalSettings.individualNotificationSettings:type_name -> WAWebProtobufsHistorySync.NotificationSettings
	21, // 30: WAWebProtobufsHistorySync.GlobalSettings.groupNotificationSettings:type_name -> WAWebProtobufsHistorySync.NotificationSettings
	26, // 31: WAWebProtobufsHistorySync.GlobalSettings.chatLockSettings:type_name -> WAProtobufsChatLockSettings.ChatLockSettings
	10, // 32: WAWebProtobufsHistorySync.PastParticipants.pastParticipants:type_name -> WAWebProtobufsHistorySync.PastParticipant
	33, // [33:33] is the sub-list for method output_type
	33, // [33:33] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_waHistorySync_WAWebProtobufsHistorySync_proto_init() }
func file_waHistorySync_WAWebProtobufsHistorySync_proto_init() {
	if File_waHistorySync_WAWebProtobufsHistorySync_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDesc), len(file_waHistorySync_WAWebProtobufsHistorySync_proto_rawDesc)),
			NumEnums:      7,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waHistorySync_WAWebProtobufsHistorySync_proto_goTypes,
		DependencyIndexes: file_waHistorySync_WAWebProtobufsHistorySync_proto_depIdxs,
		EnumInfos:         file_waHistorySync_WAWebProtobufsHistorySync_proto_enumTypes,
		MessageInfos:      file_waHistorySync_WAWebProtobufsHistorySync_proto_msgTypes,
	}.Build()
	File_waHistorySync_WAWebProtobufsHistorySync_proto = out.File
	file_waHistorySync_WAWebProtobufsHistorySync_proto_goTypes = nil
	file_waHistorySync_WAWebProtobufsHistorySync_proto_depIdxs = nil
}
