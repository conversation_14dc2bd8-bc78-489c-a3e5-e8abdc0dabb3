// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: instamadilloSupplementMessage/InstamadilloSupplementMessage.proto

package instamadilloSupplementMessage

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	instamadilloCoreTypeMedia "go.mau.fi/whatsmeow/proto/instamadilloCoreTypeMedia"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SupplementMessagePayload struct {
	state                          protoimpl.MessageState    `protogen:"open.v1"`
	TargetMessageOtid              *string                   `protobuf:"bytes,1,opt,name=targetMessageOtid" json:"targetMessageOtid,omitempty"`
	UniquingKeyForSupplementalData *string                   `protobuf:"bytes,2,opt,name=uniquingKeyForSupplementalData" json:"uniquingKeyForSupplementalData,omitempty"`
	Content                        *SupplementMessageContent `protobuf:"bytes,3,opt,name=content" json:"content,omitempty"`
	TargetMessageWaServerTimeSec   *string                   `protobuf:"bytes,4,opt,name=targetMessageWaServerTimeSec" json:"targetMessageWaServerTimeSec,omitempty"`
	TargetWaThreadID               *string                   `protobuf:"bytes,5,opt,name=targetWaThreadID" json:"targetWaThreadID,omitempty"`
	unknownFields                  protoimpl.UnknownFields
	sizeCache                      protoimpl.SizeCache
}

func (x *SupplementMessagePayload) Reset() {
	*x = SupplementMessagePayload{}
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SupplementMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupplementMessagePayload) ProtoMessage() {}

func (x *SupplementMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupplementMessagePayload.ProtoReflect.Descriptor instead.
func (*SupplementMessagePayload) Descriptor() ([]byte, []int) {
	return file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescGZIP(), []int{0}
}

func (x *SupplementMessagePayload) GetTargetMessageOtid() string {
	if x != nil && x.TargetMessageOtid != nil {
		return *x.TargetMessageOtid
	}
	return ""
}

func (x *SupplementMessagePayload) GetUniquingKeyForSupplementalData() string {
	if x != nil && x.UniquingKeyForSupplementalData != nil {
		return *x.UniquingKeyForSupplementalData
	}
	return ""
}

func (x *SupplementMessagePayload) GetContent() *SupplementMessageContent {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *SupplementMessagePayload) GetTargetMessageWaServerTimeSec() string {
	if x != nil && x.TargetMessageWaServerTimeSec != nil {
		return *x.TargetMessageWaServerTimeSec
	}
	return ""
}

func (x *SupplementMessagePayload) GetTargetWaThreadID() string {
	if x != nil && x.TargetWaThreadID != nil {
		return *x.TargetWaThreadID
	}
	return ""
}

type SupplementMessageContent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to SupplementMessageContent:
	//
	//	*SupplementMessageContent_Reaction
	//	*SupplementMessageContent_ContentView
	//	*SupplementMessageContent_EditText
	//	*SupplementMessageContent_MediaReaction
	//	*SupplementMessageContent_OriginalTransportPayload
	//	*SupplementMessageContent_MediaInterventions
	SupplementMessageContent isSupplementMessageContent_SupplementMessageContent `protobuf_oneof:"supplementMessageContent"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *SupplementMessageContent) Reset() {
	*x = SupplementMessageContent{}
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SupplementMessageContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupplementMessageContent) ProtoMessage() {}

func (x *SupplementMessageContent) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupplementMessageContent.ProtoReflect.Descriptor instead.
func (*SupplementMessageContent) Descriptor() ([]byte, []int) {
	return file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescGZIP(), []int{1}
}

func (x *SupplementMessageContent) GetSupplementMessageContent() isSupplementMessageContent_SupplementMessageContent {
	if x != nil {
		return x.SupplementMessageContent
	}
	return nil
}

func (x *SupplementMessageContent) GetReaction() *Reaction {
	if x != nil {
		if x, ok := x.SupplementMessageContent.(*SupplementMessageContent_Reaction); ok {
			return x.Reaction
		}
	}
	return nil
}

func (x *SupplementMessageContent) GetContentView() *ContentView {
	if x != nil {
		if x, ok := x.SupplementMessageContent.(*SupplementMessageContent_ContentView); ok {
			return x.ContentView
		}
	}
	return nil
}

func (x *SupplementMessageContent) GetEditText() *EditText {
	if x != nil {
		if x, ok := x.SupplementMessageContent.(*SupplementMessageContent_EditText); ok {
			return x.EditText
		}
	}
	return nil
}

func (x *SupplementMessageContent) GetMediaReaction() *MediaReaction {
	if x != nil {
		if x, ok := x.SupplementMessageContent.(*SupplementMessageContent_MediaReaction); ok {
			return x.MediaReaction
		}
	}
	return nil
}

func (x *SupplementMessageContent) GetOriginalTransportPayload() *OriginalTransportPayload {
	if x != nil {
		if x, ok := x.SupplementMessageContent.(*SupplementMessageContent_OriginalTransportPayload); ok {
			return x.OriginalTransportPayload
		}
	}
	return nil
}

func (x *SupplementMessageContent) GetMediaInterventions() *MediaInterventions {
	if x != nil {
		if x, ok := x.SupplementMessageContent.(*SupplementMessageContent_MediaInterventions); ok {
			return x.MediaInterventions
		}
	}
	return nil
}

type isSupplementMessageContent_SupplementMessageContent interface {
	isSupplementMessageContent_SupplementMessageContent()
}

type SupplementMessageContent_Reaction struct {
	Reaction *Reaction `protobuf:"bytes,1,opt,name=reaction,oneof"`
}

type SupplementMessageContent_ContentView struct {
	ContentView *ContentView `protobuf:"bytes,2,opt,name=contentView,oneof"`
}

type SupplementMessageContent_EditText struct {
	EditText *EditText `protobuf:"bytes,3,opt,name=editText,oneof"`
}

type SupplementMessageContent_MediaReaction struct {
	MediaReaction *MediaReaction `protobuf:"bytes,4,opt,name=mediaReaction,oneof"`
}

type SupplementMessageContent_OriginalTransportPayload struct {
	OriginalTransportPayload *OriginalTransportPayload `protobuf:"bytes,5,opt,name=originalTransportPayload,oneof"`
}

type SupplementMessageContent_MediaInterventions struct {
	MediaInterventions *MediaInterventions `protobuf:"bytes,6,opt,name=mediaInterventions,oneof"`
}

func (*SupplementMessageContent_Reaction) isSupplementMessageContent_SupplementMessageContent() {}

func (*SupplementMessageContent_ContentView) isSupplementMessageContent_SupplementMessageContent() {}

func (*SupplementMessageContent_EditText) isSupplementMessageContent_SupplementMessageContent() {}

func (*SupplementMessageContent_MediaReaction) isSupplementMessageContent_SupplementMessageContent() {
}

func (*SupplementMessageContent_OriginalTransportPayload) isSupplementMessageContent_SupplementMessageContent() {
}

func (*SupplementMessageContent_MediaInterventions) isSupplementMessageContent_SupplementMessageContent() {
}

type MediaReaction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MediaID       *string                `protobuf:"bytes,1,opt,name=mediaID" json:"mediaID,omitempty"`
	Reaction      *Reaction              `protobuf:"bytes,2,opt,name=reaction" json:"reaction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MediaReaction) Reset() {
	*x = MediaReaction{}
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaReaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaReaction) ProtoMessage() {}

func (x *MediaReaction) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaReaction.ProtoReflect.Descriptor instead.
func (*MediaReaction) Descriptor() ([]byte, []int) {
	return file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescGZIP(), []int{2}
}

func (x *MediaReaction) GetMediaID() string {
	if x != nil && x.MediaID != nil {
		return *x.MediaID
	}
	return ""
}

func (x *MediaReaction) GetReaction() *Reaction {
	if x != nil {
		return x.Reaction
	}
	return nil
}

type Reaction struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ReactionType   *string                `protobuf:"bytes,1,opt,name=reactionType" json:"reactionType,omitempty"`
	ReactionStatus *string                `protobuf:"bytes,2,opt,name=reactionStatus" json:"reactionStatus,omitempty"`
	Emoji          *string                `protobuf:"bytes,3,opt,name=emoji" json:"emoji,omitempty"`
	SuperReactType *string                `protobuf:"bytes,4,opt,name=superReactType" json:"superReactType,omitempty"`
	ActionLogOtid  *string                `protobuf:"bytes,5,opt,name=actionLogOtid" json:"actionLogOtid,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Reaction) Reset() {
	*x = Reaction{}
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Reaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reaction) ProtoMessage() {}

func (x *Reaction) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reaction.ProtoReflect.Descriptor instead.
func (*Reaction) Descriptor() ([]byte, []int) {
	return file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescGZIP(), []int{3}
}

func (x *Reaction) GetReactionType() string {
	if x != nil && x.ReactionType != nil {
		return *x.ReactionType
	}
	return ""
}

func (x *Reaction) GetReactionStatus() string {
	if x != nil && x.ReactionStatus != nil {
		return *x.ReactionStatus
	}
	return ""
}

func (x *Reaction) GetEmoji() string {
	if x != nil && x.Emoji != nil {
		return *x.Emoji
	}
	return ""
}

func (x *Reaction) GetSuperReactType() string {
	if x != nil && x.SuperReactType != nil {
		return *x.SuperReactType
	}
	return ""
}

func (x *Reaction) GetActionLogOtid() string {
	if x != nil && x.ActionLogOtid != nil {
		return *x.ActionLogOtid
	}
	return ""
}

type ContentView struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Seen          *bool                  `protobuf:"varint,1,opt,name=seen" json:"seen,omitempty"`
	Screenshotted *bool                  `protobuf:"varint,2,opt,name=screenshotted" json:"screenshotted,omitempty"`
	Replayed      *bool                  `protobuf:"varint,3,opt,name=replayed" json:"replayed,omitempty"`
	Mimetype      *string                `protobuf:"bytes,4,opt,name=mimetype" json:"mimetype,omitempty"`
	ObjectID      *string                `protobuf:"bytes,5,opt,name=objectID" json:"objectID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ContentView) Reset() {
	*x = ContentView{}
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContentView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentView) ProtoMessage() {}

func (x *ContentView) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentView.ProtoReflect.Descriptor instead.
func (*ContentView) Descriptor() ([]byte, []int) {
	return file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescGZIP(), []int{4}
}

func (x *ContentView) GetSeen() bool {
	if x != nil && x.Seen != nil {
		return *x.Seen
	}
	return false
}

func (x *ContentView) GetScreenshotted() bool {
	if x != nil && x.Screenshotted != nil {
		return *x.Screenshotted
	}
	return false
}

func (x *ContentView) GetReplayed() bool {
	if x != nil && x.Replayed != nil {
		return *x.Replayed
	}
	return false
}

func (x *ContentView) GetMimetype() string {
	if x != nil && x.Mimetype != nil {
		return *x.Mimetype
	}
	return ""
}

func (x *ContentView) GetObjectID() string {
	if x != nil && x.ObjectID != nil {
		return *x.ObjectID
	}
	return ""
}

type EditText struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NewContent    *string                `protobuf:"bytes,1,opt,name=newContent" json:"newContent,omitempty"`
	EditCount     *int32                 `protobuf:"varint,2,opt,name=editCount" json:"editCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EditText) Reset() {
	*x = EditText{}
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EditText) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditText) ProtoMessage() {}

func (x *EditText) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditText.ProtoReflect.Descriptor instead.
func (*EditText) Descriptor() ([]byte, []int) {
	return file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescGZIP(), []int{5}
}

func (x *EditText) GetNewContent() string {
	if x != nil && x.NewContent != nil {
		return *x.NewContent
	}
	return ""
}

func (x *EditText) GetEditCount() int32 {
	if x != nil && x.EditCount != nil {
		return *x.EditCount
	}
	return 0
}

type OriginalTransportPayload struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	OriginalTransportPayload []byte                 `protobuf:"bytes,1,opt,name=originalTransportPayload" json:"originalTransportPayload,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *OriginalTransportPayload) Reset() {
	*x = OriginalTransportPayload{}
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OriginalTransportPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OriginalTransportPayload) ProtoMessage() {}

func (x *OriginalTransportPayload) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OriginalTransportPayload.ProtoReflect.Descriptor instead.
func (*OriginalTransportPayload) Descriptor() ([]byte, []int) {
	return file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescGZIP(), []int{6}
}

func (x *OriginalTransportPayload) GetOriginalTransportPayload() []byte {
	if x != nil {
		return x.OriginalTransportPayload
	}
	return nil
}

type MediaInterventions struct {
	state            protoimpl.MessageState                            `protogen:"open.v1"`
	MediaID          *string                                           `protobuf:"bytes,1,opt,name=mediaID" json:"mediaID,omitempty"`
	InterventionType *instamadilloCoreTypeMedia.Media_InterventionType `protobuf:"varint,2,opt,name=interventionType,enum=InstamadilloCoreTypeMedia.Media_InterventionType" json:"interventionType,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *MediaInterventions) Reset() {
	*x = MediaInterventions{}
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MediaInterventions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MediaInterventions) ProtoMessage() {}

func (x *MediaInterventions) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MediaInterventions.ProtoReflect.Descriptor instead.
func (*MediaInterventions) Descriptor() ([]byte, []int) {
	return file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescGZIP(), []int{7}
}

func (x *MediaInterventions) GetMediaID() string {
	if x != nil && x.MediaID != nil {
		return *x.MediaID
	}
	return ""
}

func (x *MediaInterventions) GetInterventionType() instamadilloCoreTypeMedia.Media_InterventionType {
	if x != nil && x.InterventionType != nil {
		return *x.InterventionType
	}
	return instamadilloCoreTypeMedia.Media_InterventionType(0)
}

var File_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto protoreflect.FileDescriptor

const file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDesc = "" +
	"\n" +
	"AinstamadilloSupplementMessage/InstamadilloSupplementMessage.proto\x12\x1dInstamadilloSupplementMessage\x1a9instamadilloCoreTypeMedia/InstamadilloCoreTypeMedia.proto\"\xd3\x02\n" +
	"\x18SupplementMessagePayload\x12,\n" +
	"\x11targetMessageOtid\x18\x01 \x01(\tR\x11targetMessageOtid\x12F\n" +
	"\x1euniquingKeyForSupplementalData\x18\x02 \x01(\tR\x1euniquingKeyForSupplementalData\x12Q\n" +
	"\acontent\x18\x03 \x01(\v27.InstamadilloSupplementMessage.SupplementMessageContentR\acontent\x12B\n" +
	"\x1ctargetMessageWaServerTimeSec\x18\x04 \x01(\tR\x1ctargetMessageWaServerTimeSec\x12*\n" +
	"\x10targetWaThreadID\x18\x05 \x01(\tR\x10targetWaThreadID\"\xc6\x04\n" +
	"\x18SupplementMessageContent\x12E\n" +
	"\breaction\x18\x01 \x01(\v2'.InstamadilloSupplementMessage.ReactionH\x00R\breaction\x12N\n" +
	"\vcontentView\x18\x02 \x01(\v2*.InstamadilloSupplementMessage.ContentViewH\x00R\vcontentView\x12E\n" +
	"\beditText\x18\x03 \x01(\v2'.InstamadilloSupplementMessage.EditTextH\x00R\beditText\x12T\n" +
	"\rmediaReaction\x18\x04 \x01(\v2,.InstamadilloSupplementMessage.MediaReactionH\x00R\rmediaReaction\x12u\n" +
	"\x18originalTransportPayload\x18\x05 \x01(\v27.InstamadilloSupplementMessage.OriginalTransportPayloadH\x00R\x18originalTransportPayload\x12c\n" +
	"\x12mediaInterventions\x18\x06 \x01(\v21.InstamadilloSupplementMessage.MediaInterventionsH\x00R\x12mediaInterventionsB\x1a\n" +
	"\x18supplementMessageContent\"n\n" +
	"\rMediaReaction\x12\x18\n" +
	"\amediaID\x18\x01 \x01(\tR\amediaID\x12C\n" +
	"\breaction\x18\x02 \x01(\v2'.InstamadilloSupplementMessage.ReactionR\breaction\"\xba\x01\n" +
	"\bReaction\x12\"\n" +
	"\freactionType\x18\x01 \x01(\tR\freactionType\x12&\n" +
	"\x0ereactionStatus\x18\x02 \x01(\tR\x0ereactionStatus\x12\x14\n" +
	"\x05emoji\x18\x03 \x01(\tR\x05emoji\x12&\n" +
	"\x0esuperReactType\x18\x04 \x01(\tR\x0esuperReactType\x12$\n" +
	"\ractionLogOtid\x18\x05 \x01(\tR\ractionLogOtid\"\x9b\x01\n" +
	"\vContentView\x12\x12\n" +
	"\x04seen\x18\x01 \x01(\bR\x04seen\x12$\n" +
	"\rscreenshotted\x18\x02 \x01(\bR\rscreenshotted\x12\x1a\n" +
	"\breplayed\x18\x03 \x01(\bR\breplayed\x12\x1a\n" +
	"\bmimetype\x18\x04 \x01(\tR\bmimetype\x12\x1a\n" +
	"\bobjectID\x18\x05 \x01(\tR\bobjectID\"H\n" +
	"\bEditText\x12\x1e\n" +
	"\n" +
	"newContent\x18\x01 \x01(\tR\n" +
	"newContent\x12\x1c\n" +
	"\teditCount\x18\x02 \x01(\x05R\teditCount\"V\n" +
	"\x18OriginalTransportPayload\x12:\n" +
	"\x18originalTransportPayload\x18\x01 \x01(\fR\x18originalTransportPayload\"\x8d\x01\n" +
	"\x12MediaInterventions\x12\x18\n" +
	"\amediaID\x18\x01 \x01(\tR\amediaID\x12]\n" +
	"\x10interventionType\x18\x02 \x01(\x0e21.InstamadilloCoreTypeMedia.Media.InterventionTypeR\x10interventionTypeB9Z7go.mau.fi/whatsmeow/proto/instamadilloSupplementMessage"

var (
	file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescOnce sync.Once
	file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescData []byte
)

func file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescGZIP() []byte {
	file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescOnce.Do(func() {
		file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDesc), len(file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDesc)))
	})
	return file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDescData
}

var file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_goTypes = []any{
	(*SupplementMessagePayload)(nil),                      // 0: InstamadilloSupplementMessage.SupplementMessagePayload
	(*SupplementMessageContent)(nil),                      // 1: InstamadilloSupplementMessage.SupplementMessageContent
	(*MediaReaction)(nil),                                 // 2: InstamadilloSupplementMessage.MediaReaction
	(*Reaction)(nil),                                      // 3: InstamadilloSupplementMessage.Reaction
	(*ContentView)(nil),                                   // 4: InstamadilloSupplementMessage.ContentView
	(*EditText)(nil),                                      // 5: InstamadilloSupplementMessage.EditText
	(*OriginalTransportPayload)(nil),                      // 6: InstamadilloSupplementMessage.OriginalTransportPayload
	(*MediaInterventions)(nil),                            // 7: InstamadilloSupplementMessage.MediaInterventions
	(instamadilloCoreTypeMedia.Media_InterventionType)(0), // 8: InstamadilloCoreTypeMedia.Media.InterventionType
}
var file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_depIdxs = []int32{
	1, // 0: InstamadilloSupplementMessage.SupplementMessagePayload.content:type_name -> InstamadilloSupplementMessage.SupplementMessageContent
	3, // 1: InstamadilloSupplementMessage.SupplementMessageContent.reaction:type_name -> InstamadilloSupplementMessage.Reaction
	4, // 2: InstamadilloSupplementMessage.SupplementMessageContent.contentView:type_name -> InstamadilloSupplementMessage.ContentView
	5, // 3: InstamadilloSupplementMessage.SupplementMessageContent.editText:type_name -> InstamadilloSupplementMessage.EditText
	2, // 4: InstamadilloSupplementMessage.SupplementMessageContent.mediaReaction:type_name -> InstamadilloSupplementMessage.MediaReaction
	6, // 5: InstamadilloSupplementMessage.SupplementMessageContent.originalTransportPayload:type_name -> InstamadilloSupplementMessage.OriginalTransportPayload
	7, // 6: InstamadilloSupplementMessage.SupplementMessageContent.mediaInterventions:type_name -> InstamadilloSupplementMessage.MediaInterventions
	3, // 7: InstamadilloSupplementMessage.MediaReaction.reaction:type_name -> InstamadilloSupplementMessage.Reaction
	8, // 8: InstamadilloSupplementMessage.MediaInterventions.interventionType:type_name -> InstamadilloCoreTypeMedia.Media.InterventionType
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_init() }
func file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_init() {
	if File_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto != nil {
		return
	}
	file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes[1].OneofWrappers = []any{
		(*SupplementMessageContent_Reaction)(nil),
		(*SupplementMessageContent_ContentView)(nil),
		(*SupplementMessageContent_EditText)(nil),
		(*SupplementMessageContent_MediaReaction)(nil),
		(*SupplementMessageContent_OriginalTransportPayload)(nil),
		(*SupplementMessageContent_MediaInterventions)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDesc), len(file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_goTypes,
		DependencyIndexes: file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_depIdxs,
		MessageInfos:      file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_msgTypes,
	}.Build()
	File_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto = out.File
	file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_goTypes = nil
	file_instamadilloSupplementMessage_InstamadilloSupplementMessage_proto_depIdxs = nil
}
