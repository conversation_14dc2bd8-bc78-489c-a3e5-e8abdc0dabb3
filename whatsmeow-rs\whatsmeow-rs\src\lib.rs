//! # whatsmeow-rs
//!
//! A Rust implementation of the WhatsApp Web multidevice API.
//!
//! This library provides a comprehensive client for interacting with WhatsApp's
//! Web API, supporting message sending/receiving, group management, media handling,
//! and more.

pub mod appstate;
pub mod client;
pub mod error;
pub mod events;
pub mod group;
pub mod media;
pub mod message;
pub mod presence;
pub mod receipt;

// Re-export commonly used types
pub use client::{Client, ClientConfig};
pub use error::{Result, WhatsAppError};
pub use events::{Event, EventHandler};

// Re-export types from other crates
pub use whatsmeow_store::{DeviceStore, PreKeyStore, SenderKeyStore, SessionStore};
pub use whatsmeow_types::*;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {
        // Basic compilation test
        assert_eq!(2 + 2, 4);
    }
}
