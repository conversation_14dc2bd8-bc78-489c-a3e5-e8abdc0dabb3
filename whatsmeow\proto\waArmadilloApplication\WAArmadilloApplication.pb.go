// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waArmadilloApplication/WAArmadilloApplication.proto

package waArmadilloApplication

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	waArmadilloXMA "go.mau.fi/whatsmeow/proto/waArmadilloXMA"
	waCommon "go.mau.fi/whatsmeow/proto/waCommon"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus int32

const (
	Armadillo_Signal_EncryptedBackupsSecrets_Epoch_ES_OPEN  Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus = 1
	Armadillo_Signal_EncryptedBackupsSecrets_Epoch_ES_CLOSE Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus = 2
)

// Enum value maps for Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus.
var (
	Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus_name = map[int32]string{
		1: "ES_OPEN",
		2: "ES_CLOSE",
	}
	Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus_value = map[string]int32{
		"ES_OPEN":  1,
		"ES_CLOSE": 2,
	}
)

func (x Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus) Enum() *Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus {
	p := new(Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus)
	*p = x
	return p
}

func (x Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[0].Descriptor()
}

func (Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus) Type() protoreflect.EnumType {
	return &file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[0]
}

func (x Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus(num)
	return nil
}

// Deprecated: Use Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus.Descriptor instead.
func (Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 3, 0, 0, 0}
}

type Armadillo_Content_PaymentsTransactionMessage_PaymentStatus int32

const (
	Armadillo_Content_PaymentsTransactionMessage_PAYMENT_UNKNOWN                                         Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 0
	Armadillo_Content_PaymentsTransactionMessage_REQUEST_INITED                                          Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 4
	Armadillo_Content_PaymentsTransactionMessage_REQUEST_DECLINED                                        Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 5
	Armadillo_Content_PaymentsTransactionMessage_REQUEST_TRANSFER_INITED                                 Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 6
	Armadillo_Content_PaymentsTransactionMessage_REQUEST_TRANSFER_COMPLETED                              Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 7
	Armadillo_Content_PaymentsTransactionMessage_REQUEST_TRANSFER_FAILED                                 Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 8
	Armadillo_Content_PaymentsTransactionMessage_REQUEST_CANCELED                                        Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 9
	Armadillo_Content_PaymentsTransactionMessage_REQUEST_EXPIRED                                         Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 10
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_INITED                                         Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 11
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_PENDING                                        Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 12
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_PENDING_RECIPIENT_VERIFICATION                 Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 13
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_CANCELED                                       Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 14
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_COMPLETED                                      Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 15
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_NO_RECEIVER_CREDENTIAL_NO_RTS_PENDING_CANCELED Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 16
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_NO_RECEIVER_CREDENTIAL_NO_RTS_PENDING_OTHER    Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 17
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_REFUNDED                                       Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 18
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_PARTIAL_REFUND                                 Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 19
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_CHARGED_BACK                                   Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 20
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_EXPIRED                                        Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 21
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_DECLINED                                       Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 22
	Armadillo_Content_PaymentsTransactionMessage_TRANSFER_UNAVAILABLE                                    Armadillo_Content_PaymentsTransactionMessage_PaymentStatus = 23
)

// Enum value maps for Armadillo_Content_PaymentsTransactionMessage_PaymentStatus.
var (
	Armadillo_Content_PaymentsTransactionMessage_PaymentStatus_name = map[int32]string{
		0:  "PAYMENT_UNKNOWN",
		4:  "REQUEST_INITED",
		5:  "REQUEST_DECLINED",
		6:  "REQUEST_TRANSFER_INITED",
		7:  "REQUEST_TRANSFER_COMPLETED",
		8:  "REQUEST_TRANSFER_FAILED",
		9:  "REQUEST_CANCELED",
		10: "REQUEST_EXPIRED",
		11: "TRANSFER_INITED",
		12: "TRANSFER_PENDING",
		13: "TRANSFER_PENDING_RECIPIENT_VERIFICATION",
		14: "TRANSFER_CANCELED",
		15: "TRANSFER_COMPLETED",
		16: "TRANSFER_NO_RECEIVER_CREDENTIAL_NO_RTS_PENDING_CANCELED",
		17: "TRANSFER_NO_RECEIVER_CREDENTIAL_NO_RTS_PENDING_OTHER",
		18: "TRANSFER_REFUNDED",
		19: "TRANSFER_PARTIAL_REFUND",
		20: "TRANSFER_CHARGED_BACK",
		21: "TRANSFER_EXPIRED",
		22: "TRANSFER_DECLINED",
		23: "TRANSFER_UNAVAILABLE",
	}
	Armadillo_Content_PaymentsTransactionMessage_PaymentStatus_value = map[string]int32{
		"PAYMENT_UNKNOWN":                         0,
		"REQUEST_INITED":                          4,
		"REQUEST_DECLINED":                        5,
		"REQUEST_TRANSFER_INITED":                 6,
		"REQUEST_TRANSFER_COMPLETED":              7,
		"REQUEST_TRANSFER_FAILED":                 8,
		"REQUEST_CANCELED":                        9,
		"REQUEST_EXPIRED":                         10,
		"TRANSFER_INITED":                         11,
		"TRANSFER_PENDING":                        12,
		"TRANSFER_PENDING_RECIPIENT_VERIFICATION": 13,
		"TRANSFER_CANCELED":                       14,
		"TRANSFER_COMPLETED":                      15,
		"TRANSFER_NO_RECEIVER_CREDENTIAL_NO_RTS_PENDING_CANCELED": 16,
		"TRANSFER_NO_RECEIVER_CREDENTIAL_NO_RTS_PENDING_OTHER":    17,
		"TRANSFER_REFUNDED":       18,
		"TRANSFER_PARTIAL_REFUND": 19,
		"TRANSFER_CHARGED_BACK":   20,
		"TRANSFER_EXPIRED":        21,
		"TRANSFER_DECLINED":       22,
		"TRANSFER_UNAVAILABLE":    23,
	}
)

func (x Armadillo_Content_PaymentsTransactionMessage_PaymentStatus) Enum() *Armadillo_Content_PaymentsTransactionMessage_PaymentStatus {
	p := new(Armadillo_Content_PaymentsTransactionMessage_PaymentStatus)
	*p = x
	return p
}

func (x Armadillo_Content_PaymentsTransactionMessage_PaymentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Armadillo_Content_PaymentsTransactionMessage_PaymentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[1].Descriptor()
}

func (Armadillo_Content_PaymentsTransactionMessage_PaymentStatus) Type() protoreflect.EnumType {
	return &file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[1]
}

func (x Armadillo_Content_PaymentsTransactionMessage_PaymentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Armadillo_Content_PaymentsTransactionMessage_PaymentStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Armadillo_Content_PaymentsTransactionMessage_PaymentStatus(num)
	return nil
}

// Deprecated: Use Armadillo_Content_PaymentsTransactionMessage_PaymentStatus.Descriptor instead.
func (Armadillo_Content_PaymentsTransactionMessage_PaymentStatus) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 0, 0}
}

type Armadillo_Content_ScreenshotAction_ScreenshotType int32

const (
	Armadillo_Content_ScreenshotAction_SCREENSHOT_IMAGE Armadillo_Content_ScreenshotAction_ScreenshotType = 1
	Armadillo_Content_ScreenshotAction_SCREEN_RECORDING Armadillo_Content_ScreenshotAction_ScreenshotType = 2
)

// Enum value maps for Armadillo_Content_ScreenshotAction_ScreenshotType.
var (
	Armadillo_Content_ScreenshotAction_ScreenshotType_name = map[int32]string{
		1: "SCREENSHOT_IMAGE",
		2: "SCREEN_RECORDING",
	}
	Armadillo_Content_ScreenshotAction_ScreenshotType_value = map[string]int32{
		"SCREENSHOT_IMAGE": 1,
		"SCREEN_RECORDING": 2,
	}
)

func (x Armadillo_Content_ScreenshotAction_ScreenshotType) Enum() *Armadillo_Content_ScreenshotAction_ScreenshotType {
	p := new(Armadillo_Content_ScreenshotAction_ScreenshotType)
	*p = x
	return p
}

func (x Armadillo_Content_ScreenshotAction_ScreenshotType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Armadillo_Content_ScreenshotAction_ScreenshotType) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[2].Descriptor()
}

func (Armadillo_Content_ScreenshotAction_ScreenshotType) Type() protoreflect.EnumType {
	return &file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[2]
}

func (x Armadillo_Content_ScreenshotAction_ScreenshotType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Armadillo_Content_ScreenshotAction_ScreenshotType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Armadillo_Content_ScreenshotAction_ScreenshotType(num)
	return nil
}

// Deprecated: Use Armadillo_Content_ScreenshotAction_ScreenshotType.Descriptor instead.
func (Armadillo_Content_ScreenshotAction_ScreenshotType) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 5, 0}
}

type Armadillo_Content_RavenActionNotifMessage_ActionType int32

const (
	Armadillo_Content_RavenActionNotifMessage_PLAYED        Armadillo_Content_RavenActionNotifMessage_ActionType = 0
	Armadillo_Content_RavenActionNotifMessage_SCREENSHOT    Armadillo_Content_RavenActionNotifMessage_ActionType = 1
	Armadillo_Content_RavenActionNotifMessage_FORCE_DISABLE Armadillo_Content_RavenActionNotifMessage_ActionType = 2
)

// Enum value maps for Armadillo_Content_RavenActionNotifMessage_ActionType.
var (
	Armadillo_Content_RavenActionNotifMessage_ActionType_name = map[int32]string{
		0: "PLAYED",
		1: "SCREENSHOT",
		2: "FORCE_DISABLE",
	}
	Armadillo_Content_RavenActionNotifMessage_ActionType_value = map[string]int32{
		"PLAYED":        0,
		"SCREENSHOT":    1,
		"FORCE_DISABLE": 2,
	}
)

func (x Armadillo_Content_RavenActionNotifMessage_ActionType) Enum() *Armadillo_Content_RavenActionNotifMessage_ActionType {
	p := new(Armadillo_Content_RavenActionNotifMessage_ActionType)
	*p = x
	return p
}

func (x Armadillo_Content_RavenActionNotifMessage_ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Armadillo_Content_RavenActionNotifMessage_ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[3].Descriptor()
}

func (Armadillo_Content_RavenActionNotifMessage_ActionType) Type() protoreflect.EnumType {
	return &file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[3]
}

func (x Armadillo_Content_RavenActionNotifMessage_ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Armadillo_Content_RavenActionNotifMessage_ActionType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Armadillo_Content_RavenActionNotifMessage_ActionType(num)
	return nil
}

// Deprecated: Use Armadillo_Content_RavenActionNotifMessage_ActionType.Descriptor instead.
func (Armadillo_Content_RavenActionNotifMessage_ActionType) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 7, 0}
}

type Armadillo_Content_RavenMessage_EphemeralType int32

const (
	Armadillo_Content_RavenMessage_VIEW_ONCE    Armadillo_Content_RavenMessage_EphemeralType = 0
	Armadillo_Content_RavenMessage_ALLOW_REPLAY Armadillo_Content_RavenMessage_EphemeralType = 1
	Armadillo_Content_RavenMessage_KEEP_IN_CHAT Armadillo_Content_RavenMessage_EphemeralType = 2
)

// Enum value maps for Armadillo_Content_RavenMessage_EphemeralType.
var (
	Armadillo_Content_RavenMessage_EphemeralType_name = map[int32]string{
		0: "VIEW_ONCE",
		1: "ALLOW_REPLAY",
		2: "KEEP_IN_CHAT",
	}
	Armadillo_Content_RavenMessage_EphemeralType_value = map[string]int32{
		"VIEW_ONCE":    0,
		"ALLOW_REPLAY": 1,
		"KEEP_IN_CHAT": 2,
	}
)

func (x Armadillo_Content_RavenMessage_EphemeralType) Enum() *Armadillo_Content_RavenMessage_EphemeralType {
	p := new(Armadillo_Content_RavenMessage_EphemeralType)
	*p = x
	return p
}

func (x Armadillo_Content_RavenMessage_EphemeralType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Armadillo_Content_RavenMessage_EphemeralType) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[4].Descriptor()
}

func (Armadillo_Content_RavenMessage_EphemeralType) Type() protoreflect.EnumType {
	return &file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[4]
}

func (x Armadillo_Content_RavenMessage_EphemeralType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Armadillo_Content_RavenMessage_EphemeralType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Armadillo_Content_RavenMessage_EphemeralType(num)
	return nil
}

// Deprecated: Use Armadillo_Content_RavenMessage_EphemeralType.Descriptor instead.
func (Armadillo_Content_RavenMessage_EphemeralType) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 8, 0}
}

type Armadillo_Content_CommonSticker_StickerType int32

const (
	Armadillo_Content_CommonSticker_SMALL_LIKE  Armadillo_Content_CommonSticker_StickerType = 1
	Armadillo_Content_CommonSticker_MEDIUM_LIKE Armadillo_Content_CommonSticker_StickerType = 2
	Armadillo_Content_CommonSticker_LARGE_LIKE  Armadillo_Content_CommonSticker_StickerType = 3
)

// Enum value maps for Armadillo_Content_CommonSticker_StickerType.
var (
	Armadillo_Content_CommonSticker_StickerType_name = map[int32]string{
		1: "SMALL_LIKE",
		2: "MEDIUM_LIKE",
		3: "LARGE_LIKE",
	}
	Armadillo_Content_CommonSticker_StickerType_value = map[string]int32{
		"SMALL_LIKE":  1,
		"MEDIUM_LIKE": 2,
		"LARGE_LIKE":  3,
	}
)

func (x Armadillo_Content_CommonSticker_StickerType) Enum() *Armadillo_Content_CommonSticker_StickerType {
	p := new(Armadillo_Content_CommonSticker_StickerType)
	*p = x
	return p
}

func (x Armadillo_Content_CommonSticker_StickerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Armadillo_Content_CommonSticker_StickerType) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[5].Descriptor()
}

func (Armadillo_Content_CommonSticker_StickerType) Type() protoreflect.EnumType {
	return &file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes[5]
}

func (x Armadillo_Content_CommonSticker_StickerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Armadillo_Content_CommonSticker_StickerType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Armadillo_Content_CommonSticker_StickerType(num)
	return nil
}

// Deprecated: Use Armadillo_Content_CommonSticker_StickerType.Descriptor instead.
func (Armadillo_Content_CommonSticker_StickerType) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 9, 0}
}

type Armadillo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Payload       *Armadillo_Payload     `protobuf:"bytes,1,opt,name=payload" json:"payload,omitempty"`
	Metadata      *Armadillo_Metadata    `protobuf:"bytes,2,opt,name=metadata" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo) Reset() {
	*x = Armadillo{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo) ProtoMessage() {}

func (x *Armadillo) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo.ProtoReflect.Descriptor instead.
func (*Armadillo) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0}
}

func (x *Armadillo) GetPayload() *Armadillo_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *Armadillo) GetMetadata() *Armadillo_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type Armadillo_Metadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_Metadata) Reset() {
	*x = Armadillo_Metadata{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Metadata) ProtoMessage() {}

func (x *Armadillo_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Metadata.ProtoReflect.Descriptor instead.
func (*Armadillo_Metadata) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 0}
}

type Armadillo_Payload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Payload:
	//
	//	*Armadillo_Payload_Content
	//	*Armadillo_Payload_ApplicationData
	//	*Armadillo_Payload_Signal
	//	*Armadillo_Payload_SubProtocol
	Payload       isArmadillo_Payload_Payload `protobuf_oneof:"payload"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_Payload) Reset() {
	*x = Armadillo_Payload{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Payload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Payload) ProtoMessage() {}

func (x *Armadillo_Payload) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Payload.ProtoReflect.Descriptor instead.
func (*Armadillo_Payload) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 1}
}

func (x *Armadillo_Payload) GetPayload() isArmadillo_Payload_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *Armadillo_Payload) GetContent() *Armadillo_Content {
	if x != nil {
		if x, ok := x.Payload.(*Armadillo_Payload_Content); ok {
			return x.Content
		}
	}
	return nil
}

func (x *Armadillo_Payload) GetApplicationData() *Armadillo_ApplicationData {
	if x != nil {
		if x, ok := x.Payload.(*Armadillo_Payload_ApplicationData); ok {
			return x.ApplicationData
		}
	}
	return nil
}

func (x *Armadillo_Payload) GetSignal() *Armadillo_Signal {
	if x != nil {
		if x, ok := x.Payload.(*Armadillo_Payload_Signal); ok {
			return x.Signal
		}
	}
	return nil
}

func (x *Armadillo_Payload) GetSubProtocol() *Armadillo_SubProtocolPayload {
	if x != nil {
		if x, ok := x.Payload.(*Armadillo_Payload_SubProtocol); ok {
			return x.SubProtocol
		}
	}
	return nil
}

type isArmadillo_Payload_Payload interface {
	isArmadillo_Payload_Payload()
}

type Armadillo_Payload_Content struct {
	Content *Armadillo_Content `protobuf:"bytes,1,opt,name=content,oneof"`
}

type Armadillo_Payload_ApplicationData struct {
	ApplicationData *Armadillo_ApplicationData `protobuf:"bytes,2,opt,name=applicationData,oneof"`
}

type Armadillo_Payload_Signal struct {
	Signal *Armadillo_Signal `protobuf:"bytes,3,opt,name=signal,oneof"`
}

type Armadillo_Payload_SubProtocol struct {
	SubProtocol *Armadillo_SubProtocolPayload `protobuf:"bytes,4,opt,name=subProtocol,oneof"`
}

func (*Armadillo_Payload_Content) isArmadillo_Payload_Payload() {}

func (*Armadillo_Payload_ApplicationData) isArmadillo_Payload_Payload() {}

func (*Armadillo_Payload_Signal) isArmadillo_Payload_Payload() {}

func (*Armadillo_Payload_SubProtocol) isArmadillo_Payload_Payload() {}

type Armadillo_SubProtocolPayload struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	FutureProof   *waCommon.FutureProofBehavior `protobuf:"varint,1,opt,name=futureProof,enum=WACommon.FutureProofBehavior" json:"futureProof,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_SubProtocolPayload) Reset() {
	*x = Armadillo_SubProtocolPayload{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_SubProtocolPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_SubProtocolPayload) ProtoMessage() {}

func (x *Armadillo_SubProtocolPayload) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_SubProtocolPayload.ProtoReflect.Descriptor instead.
func (*Armadillo_SubProtocolPayload) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 2}
}

func (x *Armadillo_SubProtocolPayload) GetFutureProof() waCommon.FutureProofBehavior {
	if x != nil && x.FutureProof != nil {
		return *x.FutureProof
	}
	return waCommon.FutureProofBehavior(0)
}

type Armadillo_Signal struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Signal:
	//
	//	*Armadillo_Signal_EncryptedBackupsSecrets_
	Signal        isArmadillo_Signal_Signal `protobuf_oneof:"signal"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_Signal) Reset() {
	*x = Armadillo_Signal{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Signal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Signal) ProtoMessage() {}

func (x *Armadillo_Signal) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Signal.ProtoReflect.Descriptor instead.
func (*Armadillo_Signal) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 3}
}

func (x *Armadillo_Signal) GetSignal() isArmadillo_Signal_Signal {
	if x != nil {
		return x.Signal
	}
	return nil
}

func (x *Armadillo_Signal) GetEncryptedBackupsSecrets() *Armadillo_Signal_EncryptedBackupsSecrets {
	if x != nil {
		if x, ok := x.Signal.(*Armadillo_Signal_EncryptedBackupsSecrets_); ok {
			return x.EncryptedBackupsSecrets
		}
	}
	return nil
}

type isArmadillo_Signal_Signal interface {
	isArmadillo_Signal_Signal()
}

type Armadillo_Signal_EncryptedBackupsSecrets_ struct {
	EncryptedBackupsSecrets *Armadillo_Signal_EncryptedBackupsSecrets `protobuf:"bytes,1,opt,name=encryptedBackupsSecrets,oneof"`
}

func (*Armadillo_Signal_EncryptedBackupsSecrets_) isArmadillo_Signal_Signal() {}

type Armadillo_ApplicationData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to ApplicationData:
	//
	//	*Armadillo_ApplicationData_MetadataSync
	//	*Armadillo_ApplicationData_AiBotResponse
	//	*Armadillo_ApplicationData_MessageHistoryDocumentMessage_
	ApplicationData isArmadillo_ApplicationData_ApplicationData `protobuf_oneof:"applicationData"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData) Reset() {
	*x = Armadillo_ApplicationData{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData) ProtoMessage() {}

func (x *Armadillo_ApplicationData) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4}
}

func (x *Armadillo_ApplicationData) GetApplicationData() isArmadillo_ApplicationData_ApplicationData {
	if x != nil {
		return x.ApplicationData
	}
	return nil
}

func (x *Armadillo_ApplicationData) GetMetadataSync() *Armadillo_ApplicationData_MetadataSyncNotification {
	if x != nil {
		if x, ok := x.ApplicationData.(*Armadillo_ApplicationData_MetadataSync); ok {
			return x.MetadataSync
		}
	}
	return nil
}

func (x *Armadillo_ApplicationData) GetAiBotResponse() *Armadillo_ApplicationData_AIBotResponseMessage {
	if x != nil {
		if x, ok := x.ApplicationData.(*Armadillo_ApplicationData_AiBotResponse); ok {
			return x.AiBotResponse
		}
	}
	return nil
}

func (x *Armadillo_ApplicationData) GetMessageHistoryDocumentMessage() *Armadillo_ApplicationData_MessageHistoryDocumentMessage {
	if x != nil {
		if x, ok := x.ApplicationData.(*Armadillo_ApplicationData_MessageHistoryDocumentMessage_); ok {
			return x.MessageHistoryDocumentMessage
		}
	}
	return nil
}

type isArmadillo_ApplicationData_ApplicationData interface {
	isArmadillo_ApplicationData_ApplicationData()
}

type Armadillo_ApplicationData_MetadataSync struct {
	MetadataSync *Armadillo_ApplicationData_MetadataSyncNotification `protobuf:"bytes,1,opt,name=metadataSync,oneof"`
}

type Armadillo_ApplicationData_AiBotResponse struct {
	AiBotResponse *Armadillo_ApplicationData_AIBotResponseMessage `protobuf:"bytes,2,opt,name=aiBotResponse,oneof"`
}

type Armadillo_ApplicationData_MessageHistoryDocumentMessage_ struct {
	MessageHistoryDocumentMessage *Armadillo_ApplicationData_MessageHistoryDocumentMessage `protobuf:"bytes,3,opt,name=messageHistoryDocumentMessage,oneof"`
}

func (*Armadillo_ApplicationData_MetadataSync) isArmadillo_ApplicationData_ApplicationData() {}

func (*Armadillo_ApplicationData_AiBotResponse) isArmadillo_ApplicationData_ApplicationData() {}

func (*Armadillo_ApplicationData_MessageHistoryDocumentMessage_) isArmadillo_ApplicationData_ApplicationData() {
}

type Armadillo_Content struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Content:
	//
	//	*Armadillo_Content_CommonSticker_
	//	*Armadillo_Content_ScreenshotAction_
	//	*Armadillo_Content_ExtendedContentMessage
	//	*Armadillo_Content_RavenMessage_
	//	*Armadillo_Content_RavenActionNotifMessage_
	//	*Armadillo_Content_ExtendedMessageContentWithSear
	//	*Armadillo_Content_ImageGalleryMessage_
	//	*Armadillo_Content_PaymentsTransactionMessage_
	//	*Armadillo_Content_BumpExistingMessage_
	//	*Armadillo_Content_NoteReplyMessage_
	//	*Armadillo_Content_RavenMessageMsgr
	//	*Armadillo_Content_NetworkVerificationMessage_
	Content       isArmadillo_Content_Content `protobuf_oneof:"content"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_Content) Reset() {
	*x = Armadillo_Content{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Content) ProtoMessage() {}

func (x *Armadillo_Content) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Content.ProtoReflect.Descriptor instead.
func (*Armadillo_Content) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5}
}

func (x *Armadillo_Content) GetContent() isArmadillo_Content_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *Armadillo_Content) GetCommonSticker() *Armadillo_Content_CommonSticker {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_CommonSticker_); ok {
			return x.CommonSticker
		}
	}
	return nil
}

func (x *Armadillo_Content) GetScreenshotAction() *Armadillo_Content_ScreenshotAction {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_ScreenshotAction_); ok {
			return x.ScreenshotAction
		}
	}
	return nil
}

func (x *Armadillo_Content) GetExtendedContentMessage() *waArmadilloXMA.ExtendedContentMessage {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_ExtendedContentMessage); ok {
			return x.ExtendedContentMessage
		}
	}
	return nil
}

func (x *Armadillo_Content) GetRavenMessage() *Armadillo_Content_RavenMessage {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_RavenMessage_); ok {
			return x.RavenMessage
		}
	}
	return nil
}

func (x *Armadillo_Content) GetRavenActionNotifMessage() *Armadillo_Content_RavenActionNotifMessage {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_RavenActionNotifMessage_); ok {
			return x.RavenActionNotifMessage
		}
	}
	return nil
}

func (x *Armadillo_Content) GetExtendedMessageContentWithSear() *Armadillo_Content_ExtendedContentMessageWithSear {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_ExtendedMessageContentWithSear); ok {
			return x.ExtendedMessageContentWithSear
		}
	}
	return nil
}

func (x *Armadillo_Content) GetImageGalleryMessage() *Armadillo_Content_ImageGalleryMessage {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_ImageGalleryMessage_); ok {
			return x.ImageGalleryMessage
		}
	}
	return nil
}

func (x *Armadillo_Content) GetPaymentsTransactionMessage() *Armadillo_Content_PaymentsTransactionMessage {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_PaymentsTransactionMessage_); ok {
			return x.PaymentsTransactionMessage
		}
	}
	return nil
}

func (x *Armadillo_Content) GetBumpExistingMessage() *Armadillo_Content_BumpExistingMessage {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_BumpExistingMessage_); ok {
			return x.BumpExistingMessage
		}
	}
	return nil
}

func (x *Armadillo_Content) GetNoteReplyMessage() *Armadillo_Content_NoteReplyMessage {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_NoteReplyMessage_); ok {
			return x.NoteReplyMessage
		}
	}
	return nil
}

func (x *Armadillo_Content) GetRavenMessageMsgr() *Armadillo_Content_RavenMessage {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_RavenMessageMsgr); ok {
			return x.RavenMessageMsgr
		}
	}
	return nil
}

func (x *Armadillo_Content) GetNetworkVerificationMessage() *Armadillo_Content_NetworkVerificationMessage {
	if x != nil {
		if x, ok := x.Content.(*Armadillo_Content_NetworkVerificationMessage_); ok {
			return x.NetworkVerificationMessage
		}
	}
	return nil
}

type isArmadillo_Content_Content interface {
	isArmadillo_Content_Content()
}

type Armadillo_Content_CommonSticker_ struct {
	CommonSticker *Armadillo_Content_CommonSticker `protobuf:"bytes,1,opt,name=commonSticker,oneof"`
}

type Armadillo_Content_ScreenshotAction_ struct {
	ScreenshotAction *Armadillo_Content_ScreenshotAction `protobuf:"bytes,3,opt,name=screenshotAction,oneof"`
}

type Armadillo_Content_ExtendedContentMessage struct {
	ExtendedContentMessage *waArmadilloXMA.ExtendedContentMessage `protobuf:"bytes,4,opt,name=extendedContentMessage,oneof"`
}

type Armadillo_Content_RavenMessage_ struct {
	RavenMessage *Armadillo_Content_RavenMessage `protobuf:"bytes,5,opt,name=ravenMessage,oneof"`
}

type Armadillo_Content_RavenActionNotifMessage_ struct {
	RavenActionNotifMessage *Armadillo_Content_RavenActionNotifMessage `protobuf:"bytes,6,opt,name=ravenActionNotifMessage,oneof"`
}

type Armadillo_Content_ExtendedMessageContentWithSear struct {
	ExtendedMessageContentWithSear *Armadillo_Content_ExtendedContentMessageWithSear `protobuf:"bytes,7,opt,name=extendedMessageContentWithSear,oneof"`
}

type Armadillo_Content_ImageGalleryMessage_ struct {
	ImageGalleryMessage *Armadillo_Content_ImageGalleryMessage `protobuf:"bytes,8,opt,name=imageGalleryMessage,oneof"`
}

type Armadillo_Content_PaymentsTransactionMessage_ struct {
	PaymentsTransactionMessage *Armadillo_Content_PaymentsTransactionMessage `protobuf:"bytes,10,opt,name=paymentsTransactionMessage,oneof"`
}

type Armadillo_Content_BumpExistingMessage_ struct {
	BumpExistingMessage *Armadillo_Content_BumpExistingMessage `protobuf:"bytes,11,opt,name=bumpExistingMessage,oneof"`
}

type Armadillo_Content_NoteReplyMessage_ struct {
	NoteReplyMessage *Armadillo_Content_NoteReplyMessage `protobuf:"bytes,13,opt,name=noteReplyMessage,oneof"`
}

type Armadillo_Content_RavenMessageMsgr struct {
	RavenMessageMsgr *Armadillo_Content_RavenMessage `protobuf:"bytes,14,opt,name=ravenMessageMsgr,oneof"`
}

type Armadillo_Content_NetworkVerificationMessage_ struct {
	NetworkVerificationMessage *Armadillo_Content_NetworkVerificationMessage `protobuf:"bytes,15,opt,name=networkVerificationMessage,oneof"`
}

func (*Armadillo_Content_CommonSticker_) isArmadillo_Content_Content() {}

func (*Armadillo_Content_ScreenshotAction_) isArmadillo_Content_Content() {}

func (*Armadillo_Content_ExtendedContentMessage) isArmadillo_Content_Content() {}

func (*Armadillo_Content_RavenMessage_) isArmadillo_Content_Content() {}

func (*Armadillo_Content_RavenActionNotifMessage_) isArmadillo_Content_Content() {}

func (*Armadillo_Content_ExtendedMessageContentWithSear) isArmadillo_Content_Content() {}

func (*Armadillo_Content_ImageGalleryMessage_) isArmadillo_Content_Content() {}

func (*Armadillo_Content_PaymentsTransactionMessage_) isArmadillo_Content_Content() {}

func (*Armadillo_Content_BumpExistingMessage_) isArmadillo_Content_Content() {}

func (*Armadillo_Content_NoteReplyMessage_) isArmadillo_Content_Content() {}

func (*Armadillo_Content_RavenMessageMsgr) isArmadillo_Content_Content() {}

func (*Armadillo_Content_NetworkVerificationMessage_) isArmadillo_Content_Content() {}

type Armadillo_Signal_EncryptedBackupsSecrets struct {
	state                    protoimpl.MessageState                            `protogen:"open.v1"`
	BackupID                 *uint64                                           `protobuf:"varint,1,opt,name=backupID" json:"backupID,omitempty"`
	ServerDataID             *uint64                                           `protobuf:"varint,2,opt,name=serverDataID" json:"serverDataID,omitempty"`
	Epoch                    []*Armadillo_Signal_EncryptedBackupsSecrets_Epoch `protobuf:"bytes,3,rep,name=epoch" json:"epoch,omitempty"`
	TempOcmfClientState      []byte                                            `protobuf:"bytes,4,opt,name=tempOcmfClientState" json:"tempOcmfClientState,omitempty"`
	MailboxRootKey           []byte                                            `protobuf:"bytes,5,opt,name=mailboxRootKey" json:"mailboxRootKey,omitempty"`
	ObliviousValidationToken []byte                                            `protobuf:"bytes,6,opt,name=obliviousValidationToken" json:"obliviousValidationToken,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets) Reset() {
	*x = Armadillo_Signal_EncryptedBackupsSecrets{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Signal_EncryptedBackupsSecrets) ProtoMessage() {}

func (x *Armadillo_Signal_EncryptedBackupsSecrets) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Signal_EncryptedBackupsSecrets.ProtoReflect.Descriptor instead.
func (*Armadillo_Signal_EncryptedBackupsSecrets) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 3, 0}
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets) GetBackupID() uint64 {
	if x != nil && x.BackupID != nil {
		return *x.BackupID
	}
	return 0
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets) GetServerDataID() uint64 {
	if x != nil && x.ServerDataID != nil {
		return *x.ServerDataID
	}
	return 0
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets) GetEpoch() []*Armadillo_Signal_EncryptedBackupsSecrets_Epoch {
	if x != nil {
		return x.Epoch
	}
	return nil
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets) GetTempOcmfClientState() []byte {
	if x != nil {
		return x.TempOcmfClientState
	}
	return nil
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets) GetMailboxRootKey() []byte {
	if x != nil {
		return x.MailboxRootKey
	}
	return nil
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets) GetObliviousValidationToken() []byte {
	if x != nil {
		return x.ObliviousValidationToken
	}
	return nil
}

type Armadillo_Signal_EncryptedBackupsSecrets_Epoch struct {
	state         protoimpl.MessageState                                      `protogen:"open.v1"`
	ID            *uint64                                                     `protobuf:"varint,1,opt,name=ID" json:"ID,omitempty"`
	AnonID        []byte                                                      `protobuf:"bytes,2,opt,name=anonID" json:"anonID,omitempty"`
	RootKey       []byte                                                      `protobuf:"bytes,3,opt,name=rootKey" json:"rootKey,omitempty"`
	Status        *Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus `protobuf:"varint,4,opt,name=status,enum=WAArmadilloApplication.Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets_Epoch) Reset() {
	*x = Armadillo_Signal_EncryptedBackupsSecrets_Epoch{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets_Epoch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Signal_EncryptedBackupsSecrets_Epoch) ProtoMessage() {}

func (x *Armadillo_Signal_EncryptedBackupsSecrets_Epoch) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Signal_EncryptedBackupsSecrets_Epoch.ProtoReflect.Descriptor instead.
func (*Armadillo_Signal_EncryptedBackupsSecrets_Epoch) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 3, 0, 0}
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets_Epoch) GetID() uint64 {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return 0
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets_Epoch) GetAnonID() []byte {
	if x != nil {
		return x.AnonID
	}
	return nil
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets_Epoch) GetRootKey() []byte {
	if x != nil {
		return x.RootKey
	}
	return nil
}

func (x *Armadillo_Signal_EncryptedBackupsSecrets_Epoch) GetStatus() Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return Armadillo_Signal_EncryptedBackupsSecrets_Epoch_ES_OPEN
}

type Armadillo_ApplicationData_MessageHistoryDocumentMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Document      *waCommon.SubProtocol  `protobuf:"bytes,1,opt,name=document" json:"document,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_MessageHistoryDocumentMessage) Reset() {
	*x = Armadillo_ApplicationData_MessageHistoryDocumentMessage{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_MessageHistoryDocumentMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_MessageHistoryDocumentMessage) ProtoMessage() {}

func (x *Armadillo_ApplicationData_MessageHistoryDocumentMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_MessageHistoryDocumentMessage.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_MessageHistoryDocumentMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 0}
}

func (x *Armadillo_ApplicationData_MessageHistoryDocumentMessage) GetDocument() *waCommon.SubProtocol {
	if x != nil {
		return x.Document
	}
	return nil
}

type Armadillo_ApplicationData_AIBotResponseMessage struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	SummonToken      *string                `protobuf:"bytes,1,opt,name=summonToken" json:"summonToken,omitempty"`
	MessageText      *string                `protobuf:"bytes,2,opt,name=messageText" json:"messageText,omitempty"`
	SerializedExtras *string                `protobuf:"bytes,3,opt,name=serializedExtras" json:"serializedExtras,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_AIBotResponseMessage) Reset() {
	*x = Armadillo_ApplicationData_AIBotResponseMessage{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_AIBotResponseMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_AIBotResponseMessage) ProtoMessage() {}

func (x *Armadillo_ApplicationData_AIBotResponseMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_AIBotResponseMessage.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_AIBotResponseMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 1}
}

func (x *Armadillo_ApplicationData_AIBotResponseMessage) GetSummonToken() string {
	if x != nil && x.SummonToken != nil {
		return *x.SummonToken
	}
	return ""
}

func (x *Armadillo_ApplicationData_AIBotResponseMessage) GetMessageText() string {
	if x != nil && x.MessageText != nil {
		return *x.MessageText
	}
	return ""
}

func (x *Armadillo_ApplicationData_AIBotResponseMessage) GetSerializedExtras() string {
	if x != nil && x.SerializedExtras != nil {
		return *x.SerializedExtras
	}
	return ""
}

type Armadillo_ApplicationData_MetadataSyncAction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to ActionType:
	//
	//	*Armadillo_ApplicationData_MetadataSyncAction_ChatAction
	//	*Armadillo_ApplicationData_MetadataSyncAction_MessageAction
	ActionType      isArmadillo_ApplicationData_MetadataSyncAction_ActionType `protobuf_oneof:"actionType"`
	ActionTimestamp *int64                                                    `protobuf:"varint,1,opt,name=actionTimestamp" json:"actionTimestamp,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_MetadataSyncAction) Reset() {
	*x = Armadillo_ApplicationData_MetadataSyncAction{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_MetadataSyncAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_MetadataSyncAction) ProtoMessage() {}

func (x *Armadillo_ApplicationData_MetadataSyncAction) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_MetadataSyncAction.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_MetadataSyncAction) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 2}
}

func (x *Armadillo_ApplicationData_MetadataSyncAction) GetActionType() isArmadillo_ApplicationData_MetadataSyncAction_ActionType {
	if x != nil {
		return x.ActionType
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction) GetChatAction() *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction {
	if x != nil {
		if x, ok := x.ActionType.(*Armadillo_ApplicationData_MetadataSyncAction_ChatAction); ok {
			return x.ChatAction
		}
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction) GetMessageAction() *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction {
	if x != nil {
		if x, ok := x.ActionType.(*Armadillo_ApplicationData_MetadataSyncAction_MessageAction); ok {
			return x.MessageAction
		}
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction) GetActionTimestamp() int64 {
	if x != nil && x.ActionTimestamp != nil {
		return *x.ActionTimestamp
	}
	return 0
}

type isArmadillo_ApplicationData_MetadataSyncAction_ActionType interface {
	isArmadillo_ApplicationData_MetadataSyncAction_ActionType()
}

type Armadillo_ApplicationData_MetadataSyncAction_ChatAction struct {
	ChatAction *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction `protobuf:"bytes,101,opt,name=chatAction,oneof"`
}

type Armadillo_ApplicationData_MetadataSyncAction_MessageAction struct {
	MessageAction *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction `protobuf:"bytes,102,opt,name=messageAction,oneof"`
}

func (*Armadillo_ApplicationData_MetadataSyncAction_ChatAction) isArmadillo_ApplicationData_MetadataSyncAction_ActionType() {
}

func (*Armadillo_ApplicationData_MetadataSyncAction_MessageAction) isArmadillo_ApplicationData_MetadataSyncAction_ActionType() {
}

type Armadillo_ApplicationData_MetadataSyncNotification struct {
	state         protoimpl.MessageState                          `protogen:"open.v1"`
	Actions       []*Armadillo_ApplicationData_MetadataSyncAction `protobuf:"bytes,2,rep,name=actions" json:"actions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_MetadataSyncNotification) Reset() {
	*x = Armadillo_ApplicationData_MetadataSyncNotification{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_MetadataSyncNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_MetadataSyncNotification) ProtoMessage() {}

func (x *Armadillo_ApplicationData_MetadataSyncNotification) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_MetadataSyncNotification.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_MetadataSyncNotification) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 3}
}

func (x *Armadillo_ApplicationData_MetadataSyncNotification) GetActions() []*Armadillo_ApplicationData_MetadataSyncAction {
	if x != nil {
		return x.Actions
	}
	return nil
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Action:
	//
	//	*Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_MessageDelete
	Action        isArmadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_Action `protobuf_oneof:"action"`
	Key           *waCommon.MessageKey                                                    `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction) Reset() {
	*x = Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction) ProtoMessage() {}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 2, 0}
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction) GetAction() isArmadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction) GetMessageDelete() *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_ActionMessageDelete {
	if x != nil {
		if x, ok := x.Action.(*Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_MessageDelete); ok {
			return x.MessageDelete
		}
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction) GetKey() *waCommon.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type isArmadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_Action interface {
	isArmadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_Action()
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_MessageDelete struct {
	MessageDelete *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_ActionMessageDelete `protobuf:"bytes,101,opt,name=messageDelete,oneof"`
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_MessageDelete) isArmadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_Action() {
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Action:
	//
	//	*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatArchive
	//	*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatDelete
	//	*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatRead
	Action        isArmadillo_ApplicationData_MetadataSyncAction_SyncChatAction_Action `protobuf_oneof:"action"`
	ChatID        *string                                                              `protobuf:"bytes,1,opt,name=chatID" json:"chatID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction) Reset() {
	*x = Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction) ProtoMessage() {}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 2, 1}
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction) GetAction() isArmadillo_ApplicationData_MetadataSyncAction_SyncChatAction_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction) GetChatArchive() *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive {
	if x != nil {
		if x, ok := x.Action.(*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatArchive); ok {
			return x.ChatArchive
		}
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction) GetChatDelete() *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete {
	if x != nil {
		if x, ok := x.Action.(*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatDelete); ok {
			return x.ChatDelete
		}
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction) GetChatRead() *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead {
	if x != nil {
		if x, ok := x.Action.(*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatRead); ok {
			return x.ChatRead
		}
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction) GetChatID() string {
	if x != nil && x.ChatID != nil {
		return *x.ChatID
	}
	return ""
}

type isArmadillo_ApplicationData_MetadataSyncAction_SyncChatAction_Action interface {
	isArmadillo_ApplicationData_MetadataSyncAction_SyncChatAction_Action()
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatArchive struct {
	ChatArchive *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive `protobuf:"bytes,101,opt,name=chatArchive,oneof"`
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatDelete struct {
	ChatDelete *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete `protobuf:"bytes,102,opt,name=chatDelete,oneof"`
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatRead struct {
	ChatRead *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead `protobuf:"bytes,103,opt,name=chatRead,oneof"`
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatArchive) isArmadillo_ApplicationData_MetadataSyncAction_SyncChatAction_Action() {
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatDelete) isArmadillo_ApplicationData_MetadataSyncAction_SyncChatAction_Action() {
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatRead) isArmadillo_ApplicationData_MetadataSyncAction_SyncChatAction_Action() {
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           *waCommon.MessageKey   `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Timestamp     *int64                 `protobuf:"varint,2,opt,name=timestamp" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage) Reset() {
	*x = Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage) ProtoMessage() {}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 2, 2}
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage) GetKey() *waCommon.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange struct {
	state                      protoimpl.MessageState                                            `protogen:"open.v1"`
	LastMessageTimestamp       *int64                                                            `protobuf:"varint,1,opt,name=lastMessageTimestamp" json:"lastMessageTimestamp,omitempty"`
	LastSystemMessageTimestamp *int64                                                            `protobuf:"varint,2,opt,name=lastSystemMessageTimestamp" json:"lastSystemMessageTimestamp,omitempty"`
	Messages                   []*Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage `protobuf:"bytes,3,rep,name=messages" json:"messages,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange) Reset() {
	*x = Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange) ProtoMessage() {}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 2, 3}
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange) GetLastMessageTimestamp() int64 {
	if x != nil && x.LastMessageTimestamp != nil {
		return *x.LastMessageTimestamp
	}
	return 0
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange) GetLastSystemMessageTimestamp() int64 {
	if x != nil && x.LastSystemMessageTimestamp != nil {
		return *x.LastSystemMessageTimestamp
	}
	return 0
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange) GetMessages() []*Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage {
	if x != nil {
		return x.Messages
	}
	return nil
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_ActionMessageDelete struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_ActionMessageDelete) Reset() {
	*x = Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_ActionMessageDelete{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_ActionMessageDelete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_ActionMessageDelete) ProtoMessage() {
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_ActionMessageDelete) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_ActionMessageDelete.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_ActionMessageDelete) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 2, 0, 0}
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead struct {
	state         protoimpl.MessageState                                               `protogen:"open.v1"`
	MessageRange  *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange `protobuf:"bytes,1,opt,name=messageRange" json:"messageRange,omitempty"`
	Read          *bool                                                                `protobuf:"varint,2,opt,name=read" json:"read,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead) Reset() {
	*x = Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead) ProtoMessage() {}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 2, 1, 0}
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead) GetMessageRange() *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange {
	if x != nil {
		return x.MessageRange
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead) GetRead() bool {
	if x != nil && x.Read != nil {
		return *x.Read
	}
	return false
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete struct {
	state         protoimpl.MessageState                                               `protogen:"open.v1"`
	MessageRange  *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange `protobuf:"bytes,1,opt,name=messageRange" json:"messageRange,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete) Reset() {
	*x = Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete) ProtoMessage() {}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 2, 1, 1}
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete) GetMessageRange() *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange {
	if x != nil {
		return x.MessageRange
	}
	return nil
}

type Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive struct {
	state         protoimpl.MessageState                                               `protogen:"open.v1"`
	MessageRange  *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange `protobuf:"bytes,1,opt,name=messageRange" json:"messageRange,omitempty"`
	Archived      *bool                                                                `protobuf:"varint,2,opt,name=archived" json:"archived,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive) Reset() {
	*x = Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive) ProtoMessage() {
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive.ProtoReflect.Descriptor instead.
func (*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 4, 2, 1, 2}
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive) GetMessageRange() *Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange {
	if x != nil {
		return x.MessageRange
	}
	return nil
}

func (x *Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive) GetArchived() bool {
	if x != nil && x.Archived != nil {
		return *x.Archived
	}
	return false
}

type Armadillo_Content_PaymentsTransactionMessage struct {
	state                  protoimpl.MessageState                                      `protogen:"open.v1"`
	TransactionID          *uint64                                                     `protobuf:"varint,1,opt,name=transactionID" json:"transactionID,omitempty"`
	Amount                 *string                                                     `protobuf:"bytes,2,opt,name=amount" json:"amount,omitempty"`
	Currency               *string                                                     `protobuf:"bytes,3,opt,name=currency" json:"currency,omitempty"`
	PaymentStatus          *Armadillo_Content_PaymentsTransactionMessage_PaymentStatus `protobuf:"varint,4,opt,name=paymentStatus,enum=WAArmadilloApplication.Armadillo_Content_PaymentsTransactionMessage_PaymentStatus" json:"paymentStatus,omitempty"`
	ExtendedContentMessage *waArmadilloXMA.ExtendedContentMessage                      `protobuf:"bytes,5,opt,name=extendedContentMessage" json:"extendedContentMessage,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *Armadillo_Content_PaymentsTransactionMessage) Reset() {
	*x = Armadillo_Content_PaymentsTransactionMessage{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Content_PaymentsTransactionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Content_PaymentsTransactionMessage) ProtoMessage() {}

func (x *Armadillo_Content_PaymentsTransactionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Content_PaymentsTransactionMessage.ProtoReflect.Descriptor instead.
func (*Armadillo_Content_PaymentsTransactionMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 0}
}

func (x *Armadillo_Content_PaymentsTransactionMessage) GetTransactionID() uint64 {
	if x != nil && x.TransactionID != nil {
		return *x.TransactionID
	}
	return 0
}

func (x *Armadillo_Content_PaymentsTransactionMessage) GetAmount() string {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return ""
}

func (x *Armadillo_Content_PaymentsTransactionMessage) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *Armadillo_Content_PaymentsTransactionMessage) GetPaymentStatus() Armadillo_Content_PaymentsTransactionMessage_PaymentStatus {
	if x != nil && x.PaymentStatus != nil {
		return *x.PaymentStatus
	}
	return Armadillo_Content_PaymentsTransactionMessage_PAYMENT_UNKNOWN
}

func (x *Armadillo_Content_PaymentsTransactionMessage) GetExtendedContentMessage() *waArmadilloXMA.ExtendedContentMessage {
	if x != nil {
		return x.ExtendedContentMessage
	}
	return nil
}

type Armadillo_Content_NetworkVerificationMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CodeText      *string                `protobuf:"bytes,1,opt,name=codeText" json:"codeText,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_Content_NetworkVerificationMessage) Reset() {
	*x = Armadillo_Content_NetworkVerificationMessage{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Content_NetworkVerificationMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Content_NetworkVerificationMessage) ProtoMessage() {}

func (x *Armadillo_Content_NetworkVerificationMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Content_NetworkVerificationMessage.ProtoReflect.Descriptor instead.
func (*Armadillo_Content_NetworkVerificationMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 1}
}

func (x *Armadillo_Content_NetworkVerificationMessage) GetCodeText() string {
	if x != nil && x.CodeText != nil {
		return *x.CodeText
	}
	return ""
}

type Armadillo_Content_NoteReplyMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to NoteReplyContent:
	//
	//	*Armadillo_Content_NoteReplyMessage_TextContent
	//	*Armadillo_Content_NoteReplyMessage_StickerContent
	//	*Armadillo_Content_NoteReplyMessage_VideoContent
	NoteReplyContent isArmadillo_Content_NoteReplyMessage_NoteReplyContent `protobuf_oneof:"noteReplyContent"`
	NoteID           *string                                               `protobuf:"bytes,1,opt,name=noteID" json:"noteID,omitempty"`
	NoteText         *waCommon.MessageText                                 `protobuf:"bytes,2,opt,name=noteText" json:"noteText,omitempty"`
	NoteTimestampMS  *int64                                                `protobuf:"varint,3,opt,name=noteTimestampMS" json:"noteTimestampMS,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Armadillo_Content_NoteReplyMessage) Reset() {
	*x = Armadillo_Content_NoteReplyMessage{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Content_NoteReplyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Content_NoteReplyMessage) ProtoMessage() {}

func (x *Armadillo_Content_NoteReplyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Content_NoteReplyMessage.ProtoReflect.Descriptor instead.
func (*Armadillo_Content_NoteReplyMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 2}
}

func (x *Armadillo_Content_NoteReplyMessage) GetNoteReplyContent() isArmadillo_Content_NoteReplyMessage_NoteReplyContent {
	if x != nil {
		return x.NoteReplyContent
	}
	return nil
}

func (x *Armadillo_Content_NoteReplyMessage) GetTextContent() *waCommon.MessageText {
	if x != nil {
		if x, ok := x.NoteReplyContent.(*Armadillo_Content_NoteReplyMessage_TextContent); ok {
			return x.TextContent
		}
	}
	return nil
}

func (x *Armadillo_Content_NoteReplyMessage) GetStickerContent() *waCommon.SubProtocol {
	if x != nil {
		if x, ok := x.NoteReplyContent.(*Armadillo_Content_NoteReplyMessage_StickerContent); ok {
			return x.StickerContent
		}
	}
	return nil
}

func (x *Armadillo_Content_NoteReplyMessage) GetVideoContent() *waCommon.SubProtocol {
	if x != nil {
		if x, ok := x.NoteReplyContent.(*Armadillo_Content_NoteReplyMessage_VideoContent); ok {
			return x.VideoContent
		}
	}
	return nil
}

func (x *Armadillo_Content_NoteReplyMessage) GetNoteID() string {
	if x != nil && x.NoteID != nil {
		return *x.NoteID
	}
	return ""
}

func (x *Armadillo_Content_NoteReplyMessage) GetNoteText() *waCommon.MessageText {
	if x != nil {
		return x.NoteText
	}
	return nil
}

func (x *Armadillo_Content_NoteReplyMessage) GetNoteTimestampMS() int64 {
	if x != nil && x.NoteTimestampMS != nil {
		return *x.NoteTimestampMS
	}
	return 0
}

type isArmadillo_Content_NoteReplyMessage_NoteReplyContent interface {
	isArmadillo_Content_NoteReplyMessage_NoteReplyContent()
}

type Armadillo_Content_NoteReplyMessage_TextContent struct {
	TextContent *waCommon.MessageText `protobuf:"bytes,4,opt,name=textContent,oneof"`
}

type Armadillo_Content_NoteReplyMessage_StickerContent struct {
	StickerContent *waCommon.SubProtocol `protobuf:"bytes,5,opt,name=stickerContent,oneof"`
}

type Armadillo_Content_NoteReplyMessage_VideoContent struct {
	VideoContent *waCommon.SubProtocol `protobuf:"bytes,6,opt,name=videoContent,oneof"`
}

func (*Armadillo_Content_NoteReplyMessage_TextContent) isArmadillo_Content_NoteReplyMessage_NoteReplyContent() {
}

func (*Armadillo_Content_NoteReplyMessage_StickerContent) isArmadillo_Content_NoteReplyMessage_NoteReplyContent() {
}

func (*Armadillo_Content_NoteReplyMessage_VideoContent) isArmadillo_Content_NoteReplyMessage_NoteReplyContent() {
}

type Armadillo_Content_BumpExistingMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           *waCommon.MessageKey   `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_Content_BumpExistingMessage) Reset() {
	*x = Armadillo_Content_BumpExistingMessage{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Content_BumpExistingMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Content_BumpExistingMessage) ProtoMessage() {}

func (x *Armadillo_Content_BumpExistingMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Content_BumpExistingMessage.ProtoReflect.Descriptor instead.
func (*Armadillo_Content_BumpExistingMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 3}
}

func (x *Armadillo_Content_BumpExistingMessage) GetKey() *waCommon.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

type Armadillo_Content_ImageGalleryMessage struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Images        []*waCommon.SubProtocol `protobuf:"bytes,1,rep,name=images" json:"images,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_Content_ImageGalleryMessage) Reset() {
	*x = Armadillo_Content_ImageGalleryMessage{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Content_ImageGalleryMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Content_ImageGalleryMessage) ProtoMessage() {}

func (x *Armadillo_Content_ImageGalleryMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Content_ImageGalleryMessage.ProtoReflect.Descriptor instead.
func (*Armadillo_Content_ImageGalleryMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 4}
}

func (x *Armadillo_Content_ImageGalleryMessage) GetImages() []*waCommon.SubProtocol {
	if x != nil {
		return x.Images
	}
	return nil
}

type Armadillo_Content_ScreenshotAction struct {
	state          protoimpl.MessageState                             `protogen:"open.v1"`
	ScreenshotType *Armadillo_Content_ScreenshotAction_ScreenshotType `protobuf:"varint,1,opt,name=screenshotType,enum=WAArmadilloApplication.Armadillo_Content_ScreenshotAction_ScreenshotType" json:"screenshotType,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Armadillo_Content_ScreenshotAction) Reset() {
	*x = Armadillo_Content_ScreenshotAction{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Content_ScreenshotAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Content_ScreenshotAction) ProtoMessage() {}

func (x *Armadillo_Content_ScreenshotAction) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Content_ScreenshotAction.ProtoReflect.Descriptor instead.
func (*Armadillo_Content_ScreenshotAction) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 5}
}

func (x *Armadillo_Content_ScreenshotAction) GetScreenshotType() Armadillo_Content_ScreenshotAction_ScreenshotType {
	if x != nil && x.ScreenshotType != nil {
		return *x.ScreenshotType
	}
	return Armadillo_Content_ScreenshotAction_SCREENSHOT_IMAGE
}

type Armadillo_Content_ExtendedContentMessageWithSear struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	SearID                *string                `protobuf:"bytes,1,opt,name=searID" json:"searID,omitempty"`
	Payload               []byte                 `protobuf:"bytes,2,opt,name=payload" json:"payload,omitempty"`
	NativeURL             *string                `protobuf:"bytes,3,opt,name=nativeURL" json:"nativeURL,omitempty"`
	SearAssociatedMessage *waCommon.SubProtocol  `protobuf:"bytes,4,opt,name=searAssociatedMessage" json:"searAssociatedMessage,omitempty"`
	SearSentWithMessageID *string                `protobuf:"bytes,5,opt,name=searSentWithMessageID" json:"searSentWithMessageID,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *Armadillo_Content_ExtendedContentMessageWithSear) Reset() {
	*x = Armadillo_Content_ExtendedContentMessageWithSear{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Content_ExtendedContentMessageWithSear) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Content_ExtendedContentMessageWithSear) ProtoMessage() {}

func (x *Armadillo_Content_ExtendedContentMessageWithSear) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Content_ExtendedContentMessageWithSear.ProtoReflect.Descriptor instead.
func (*Armadillo_Content_ExtendedContentMessageWithSear) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 6}
}

func (x *Armadillo_Content_ExtendedContentMessageWithSear) GetSearID() string {
	if x != nil && x.SearID != nil {
		return *x.SearID
	}
	return ""
}

func (x *Armadillo_Content_ExtendedContentMessageWithSear) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *Armadillo_Content_ExtendedContentMessageWithSear) GetNativeURL() string {
	if x != nil && x.NativeURL != nil {
		return *x.NativeURL
	}
	return ""
}

func (x *Armadillo_Content_ExtendedContentMessageWithSear) GetSearAssociatedMessage() *waCommon.SubProtocol {
	if x != nil {
		return x.SearAssociatedMessage
	}
	return nil
}

func (x *Armadillo_Content_ExtendedContentMessageWithSear) GetSearSentWithMessageID() string {
	if x != nil && x.SearSentWithMessageID != nil {
		return *x.SearSentWithMessageID
	}
	return ""
}

type Armadillo_Content_RavenActionNotifMessage struct {
	state           protoimpl.MessageState                                `protogen:"open.v1"`
	Key             *waCommon.MessageKey                                  `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	ActionTimestamp *int64                                                `protobuf:"varint,2,opt,name=actionTimestamp" json:"actionTimestamp,omitempty"`
	ActionType      *Armadillo_Content_RavenActionNotifMessage_ActionType `protobuf:"varint,3,opt,name=actionType,enum=WAArmadilloApplication.Armadillo_Content_RavenActionNotifMessage_ActionType" json:"actionType,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Armadillo_Content_RavenActionNotifMessage) Reset() {
	*x = Armadillo_Content_RavenActionNotifMessage{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Content_RavenActionNotifMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Content_RavenActionNotifMessage) ProtoMessage() {}

func (x *Armadillo_Content_RavenActionNotifMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Content_RavenActionNotifMessage.ProtoReflect.Descriptor instead.
func (*Armadillo_Content_RavenActionNotifMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 7}
}

func (x *Armadillo_Content_RavenActionNotifMessage) GetKey() *waCommon.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *Armadillo_Content_RavenActionNotifMessage) GetActionTimestamp() int64 {
	if x != nil && x.ActionTimestamp != nil {
		return *x.ActionTimestamp
	}
	return 0
}

func (x *Armadillo_Content_RavenActionNotifMessage) GetActionType() Armadillo_Content_RavenActionNotifMessage_ActionType {
	if x != nil && x.ActionType != nil {
		return *x.ActionType
	}
	return Armadillo_Content_RavenActionNotifMessage_PLAYED
}

type Armadillo_Content_RavenMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to MediaContent:
	//
	//	*Armadillo_Content_RavenMessage_ImageMessage
	//	*Armadillo_Content_RavenMessage_VideoMessage
	MediaContent  isArmadillo_Content_RavenMessage_MediaContent `protobuf_oneof:"mediaContent"`
	EphemeralType *Armadillo_Content_RavenMessage_EphemeralType `protobuf:"varint,1,opt,name=ephemeralType,enum=WAArmadilloApplication.Armadillo_Content_RavenMessage_EphemeralType" json:"ephemeralType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_Content_RavenMessage) Reset() {
	*x = Armadillo_Content_RavenMessage{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Content_RavenMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Content_RavenMessage) ProtoMessage() {}

func (x *Armadillo_Content_RavenMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Content_RavenMessage.ProtoReflect.Descriptor instead.
func (*Armadillo_Content_RavenMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 8}
}

func (x *Armadillo_Content_RavenMessage) GetMediaContent() isArmadillo_Content_RavenMessage_MediaContent {
	if x != nil {
		return x.MediaContent
	}
	return nil
}

func (x *Armadillo_Content_RavenMessage) GetImageMessage() *waCommon.SubProtocol {
	if x != nil {
		if x, ok := x.MediaContent.(*Armadillo_Content_RavenMessage_ImageMessage); ok {
			return x.ImageMessage
		}
	}
	return nil
}

func (x *Armadillo_Content_RavenMessage) GetVideoMessage() *waCommon.SubProtocol {
	if x != nil {
		if x, ok := x.MediaContent.(*Armadillo_Content_RavenMessage_VideoMessage); ok {
			return x.VideoMessage
		}
	}
	return nil
}

func (x *Armadillo_Content_RavenMessage) GetEphemeralType() Armadillo_Content_RavenMessage_EphemeralType {
	if x != nil && x.EphemeralType != nil {
		return *x.EphemeralType
	}
	return Armadillo_Content_RavenMessage_VIEW_ONCE
}

type isArmadillo_Content_RavenMessage_MediaContent interface {
	isArmadillo_Content_RavenMessage_MediaContent()
}

type Armadillo_Content_RavenMessage_ImageMessage struct {
	ImageMessage *waCommon.SubProtocol `protobuf:"bytes,2,opt,name=imageMessage,oneof"`
}

type Armadillo_Content_RavenMessage_VideoMessage struct {
	VideoMessage *waCommon.SubProtocol `protobuf:"bytes,3,opt,name=videoMessage,oneof"`
}

func (*Armadillo_Content_RavenMessage_ImageMessage) isArmadillo_Content_RavenMessage_MediaContent() {}

func (*Armadillo_Content_RavenMessage_VideoMessage) isArmadillo_Content_RavenMessage_MediaContent() {}

type Armadillo_Content_CommonSticker struct {
	state         protoimpl.MessageState                       `protogen:"open.v1"`
	StickerType   *Armadillo_Content_CommonSticker_StickerType `protobuf:"varint,1,opt,name=stickerType,enum=WAArmadilloApplication.Armadillo_Content_CommonSticker_StickerType" json:"stickerType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Armadillo_Content_CommonSticker) Reset() {
	*x = Armadillo_Content_CommonSticker{}
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Armadillo_Content_CommonSticker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Armadillo_Content_CommonSticker) ProtoMessage() {}

func (x *Armadillo_Content_CommonSticker) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Armadillo_Content_CommonSticker.ProtoReflect.Descriptor instead.
func (*Armadillo_Content_CommonSticker) Descriptor() ([]byte, []int) {
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP(), []int{0, 5, 9}
}

func (x *Armadillo_Content_CommonSticker) GetStickerType() Armadillo_Content_CommonSticker_StickerType {
	if x != nil && x.StickerType != nil {
		return *x.StickerType
	}
	return Armadillo_Content_CommonSticker_SMALL_LIKE
}

var File_waArmadilloApplication_WAArmadilloApplication_proto protoreflect.FileDescriptor

const file_waArmadilloApplication_WAArmadilloApplication_proto_rawDesc = "" +
	"\n" +
	"3waArmadilloApplication/WAArmadilloApplication.proto\x12\x16WAArmadilloApplication\x1a#waArmadilloXMA/WAArmadilloXMA.proto\x1a\x17waCommon/WACommon.proto\"\x97?\n" +
	"\tArmadillo\x12C\n" +
	"\apayload\x18\x01 \x01(\v2).WAArmadilloApplication.Armadillo.PayloadR\apayload\x12F\n" +
	"\bmetadata\x18\x02 \x01(\v2*.WAArmadilloApplication.Armadillo.MetadataR\bmetadata\x1a\n" +
	"\n" +
	"\bMetadata\x1a\xd8\x02\n" +
	"\aPayload\x12E\n" +
	"\acontent\x18\x01 \x01(\v2).WAArmadilloApplication.Armadillo.ContentH\x00R\acontent\x12]\n" +
	"\x0fapplicationData\x18\x02 \x01(\v21.WAArmadilloApplication.Armadillo.ApplicationDataH\x00R\x0fapplicationData\x12B\n" +
	"\x06signal\x18\x03 \x01(\v2(.WAArmadilloApplication.Armadillo.SignalH\x00R\x06signal\x12X\n" +
	"\vsubProtocol\x18\x04 \x01(\v24.WAArmadilloApplication.Armadillo.SubProtocolPayloadH\x00R\vsubProtocolB\t\n" +
	"\apayload\x1aU\n" +
	"\x12SubProtocolPayload\x12?\n" +
	"\vfutureProof\x18\x01 \x01(\x0e2\x1d.WACommon.FutureProofBehaviorR\vfutureProof\x1a\xc2\x05\n" +
	"\x06Signal\x12|\n" +
	"\x17encryptedBackupsSecrets\x18\x01 \x01(\<EMAIL>\x00R\x17encryptedBackupsSecrets\x1a\xaf\x04\n" +
	"\x17EncryptedBackupsSecrets\x12\x1a\n" +
	"\bbackupID\x18\x01 \x01(\x04R\bbackupID\x12\"\n" +
	"\fserverDataID\x18\x02 \x01(\x04R\fserverDataID\x12\\\n" +
	"\x05epoch\x18\x03 \x03(\v2F.WAArmadilloApplication.Armadillo.Signal.EncryptedBackupsSecrets.EpochR\x05epoch\x120\n" +
	"\x13tempOcmfClientState\x18\x04 \x01(\fR\x13tempOcmfClientState\x12&\n" +
	"\x0emailboxRootKey\x18\x05 \x01(\fR\x0emailboxRootKey\x12:\n" +
	"\x18obliviousValidationToken\x18\x06 \x01(\fR\x18obliviousValidationToken\x1a\xdf\x01\n" +
	"\x05Epoch\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\x04R\x02ID\x12\x16\n" +
	"\x06anonID\x18\x02 \x01(\fR\x06anonID\x12\x18\n" +
	"\arootKey\x18\x03 \x01(\fR\arootKey\x12j\n" +
	"\x06status\x18\x04 \x01(\x0e2R.WAArmadilloApplication.Armadillo.Signal.EncryptedBackupsSecrets.Epoch.EpochStatusR\x06status\"(\n" +
	"\vEpochStatus\x12\v\n" +
	"\aES_OPEN\x10\x01\x12\f\n" +
	"\bES_CLOSE\x10\x02B\b\n" +
	"\x06signal\x1a\xcf\x14\n" +
	"\x0fApplicationData\x12p\n" +
	"\fmetadataSync\x18\x01 \x01(\v2J.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncNotificationH\x00R\fmetadataSync\x12n\n" +
	"\raiBotResponse\x18\x02 \x01(\v2F.WAArmadilloApplication.Armadillo.ApplicationData.AIBotResponseMessageH\x00R\raiBotResponse\x12\x97\x01\n" +
	"\x1dmessageHistoryDocumentMessage\x18\x03 \x01(\v2O.WAArmadilloApplication.Armadillo.ApplicationData.MessageHistoryDocumentMessageH\x00R\x1dmessageHistoryDocumentMessage\x1aR\n" +
	"\x1dMessageHistoryDocumentMessage\x121\n" +
	"\bdocument\x18\x01 \x01(\v2\x15.WACommon.SubProtocolR\bdocument\x1a\x86\x01\n" +
	"\x14AIBotResponseMessage\x12 \n" +
	"\vsummonToken\x18\x01 \x01(\tR\vsummonToken\x12 \n" +
	"\vmessageText\x18\x02 \x01(\tR\vmessageText\x12*\n" +
	"\x10serializedExtras\x18\x03 \x01(\tR\x10serializedExtras\x1a\xd3\x0e\n" +
	"\x12MetadataSyncAction\x12u\n" +
	"\n" +
	"chatAction\x18e \x01(\v2S.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatActionH\x00R\n" +
	"chatAction\x12~\n" +
	"\rmessageAction\x18f \x01(\v2V.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncMessageActionH\x00R\rmessageAction\x12(\n" +
	"\x0factionTimestamp\x18\x01 \x01(\x03R\x0factionTimestamp\x1a\xf1\x01\n" +
	"\x11SyncMessageAction\x12\x92\x01\n" +
	"\rmessageDelete\x18e \x01(\v2j.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncMessageAction.ActionMessageDeleteH\x00R\rmessageDelete\x12&\n" +
	"\x03key\x18\x01 \x01(\v2\x14.WACommon.MessageKeyR\x03key\x1a\x15\n" +
	"\x13ActionMessageDeleteB\b\n" +
	"\x06action\x1a\xbb\a\n" +
	"\x0eSyncChatAction\x12\x89\x01\n" +
	"\vchatArchive\x18e \x01(\v2e.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatArchiveH\x00R\vchatArchive\x12\x86\x01\n" +
	"\n" +
	"chatDelete\x18f \x01(\v2d.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatDeleteH\x00R\n" +
	"chatDelete\x12\x80\x01\n" +
	"\bchatRead\x18g \x01(\v2b.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatReadH\x00R\bchatRead\x12\x16\n" +
	"\x06chatID\x18\x01 \x01(\tR\x06chatID\x1a\xa5\x01\n" +
	"\x0eActionChatRead\x12\x7f\n" +
	"\fmessageRange\x18\x01 \x01(\v2[.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessageRangeR\fmessageRange\x12\x12\n" +
	"\x04read\x18\x02 \x01(\bR\x04read\x1a\x93\x01\n" +
	"\x10ActionChatDelete\x12\x7f\n" +
	"\fmessageRange\x18\x01 \x01(\v2[.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessageRangeR\fmessageRange\x1a\xb0\x01\n" +
	"\x11ActionChatArchive\x12\x7f\n" +
	"\fmessageRange\x18\x01 \x01(\v2[.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessageRangeR\fmessageRange\x12\x1a\n" +
	"\barchived\x18\x02 \x01(\bR\barchivedB\b\n" +
	"\x06action\x1aY\n" +
	"\x11SyncActionMessage\x12&\n" +
	"\x03key\x18\x01 \x01(\v2\x14.WACommon.MessageKeyR\x03key\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x1a\x80\x02\n" +
	"\x16SyncActionMessageRange\x122\n" +
	"\x14lastMessageTimestamp\x18\x01 \x01(\x03R\x14lastMessageTimestamp\x12>\n" +
	"\x1alastSystemMessageTimestamp\x18\x02 \x01(\x03R\x1alastSystemMessageTimestamp\x12r\n" +
	"\bmessages\x18\x03 \x03(\v2V.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessageR\bmessagesB\f\n" +
	"\n" +
	"actionType\x1az\n" +
	"\x18MetadataSyncNotification\x12^\n" +
	"\aactions\x18\x02 \x03(\v2D.WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncActionR\aactionsB\x11\n" +
	"\x0fapplicationData\x1a\xa7 \n" +
	"\aContent\x12_\n" +
	"\rcommonSticker\x18\x01 \x01(\v27.WAArmadilloApplication.Armadillo.Content.CommonStickerH\x00R\rcommonSticker\x12h\n" +
	"\x10screenshotAction\x18\x03 \x01(\v2:.WAArmadilloApplication.Armadillo.Content.ScreenshotActionH\x00R\x10screenshotAction\x12`\n" +
	"\x16extendedContentMessage\x18\x04 \x01(\v2&.WAArmadilloXMA.ExtendedContentMessageH\x00R\x16extendedContentMessage\x12\\\n" +
	"\fravenMessage\x18\x05 \x01(\v26.WAArmadilloApplication.Armadillo.Content.RavenMessageH\x00R\fravenMessage\x12}\n" +
	"\x17ravenActionNotifMessage\x18\x06 \x01(\v2A.WAArmadilloApplication.Armadillo.Content.RavenActionNotifMessageH\x00R\x17ravenActionNotifMessage\x12\x92\x01\n" +
	"\x1eextendedMessageContentWithSear\x18\a \x01(\v2H.WAArmadilloApplication.Armadillo.Content.ExtendedContentMessageWithSearH\x00R\x1eextendedMessageContentWithSear\x12q\n" +
	"\x13imageGalleryMessage\x18\b \x01(\v2=.WAArmadilloApplication.Armadillo.Content.ImageGalleryMessageH\x00R\x13imageGalleryMessage\x12\x86\x01\n" +
	"\x1apaymentsTransactionMessage\x18\n" +
	" \x01(\v2D.WAArmadilloApplication.Armadillo.Content.PaymentsTransactionMessageH\x00R\x1apaymentsTransactionMessage\x12q\n" +
	"\x13bumpExistingMessage\x18\v \x01(\v2=.WAArmadilloApplication.Armadillo.Content.BumpExistingMessageH\x00R\x13bumpExistingMessage\x12h\n" +
	"\x10noteReplyMessage\x18\r \x01(\v2:.WAArmadilloApplication.Armadillo.Content.NoteReplyMessageH\x00R\x10noteReplyMessage\x12d\n" +
	"\x10ravenMessageMsgr\x18\x0e \x01(\v26.WAArmadilloApplication.Armadillo.Content.RavenMessageH\x00R\x10ravenMessageMsgr\x12\x86\x01\n" +
	"\x1anetworkVerificationMessage\x18\x0f \x01(\v2D.WAArmadilloApplication.Armadillo.Content.NetworkVerificationMessageH\x00R\x1anetworkVerificationMessage\x1a\xba\a\n" +
	"\x1aPaymentsTransactionMessage\x12$\n" +
	"\rtransactionID\x18\x01 \x01(\x04R\rtransactionID\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\tR\x06amount\x12\x1a\n" +
	"\bcurrency\x18\x03 \x01(\tR\bcurrency\x12x\n" +
	"\rpaymentStatus\x18\x04 \x01(\x0e2R.WAArmadilloApplication.Armadillo.Content.PaymentsTransactionMessage.PaymentStatusR\rpaymentStatus\x12^\n" +
	"\x16extendedContentMessage\x18\x05 \x01(\v2&.WAArmadilloXMA.ExtendedContentMessageR\x16extendedContentMessage\"\xe7\x04\n" +
	"\rPaymentStatus\x12\x13\n" +
	"\x0fPAYMENT_UNKNOWN\x10\x00\x12\x12\n" +
	"\x0eREQUEST_INITED\x10\x04\x12\x14\n" +
	"\x10REQUEST_DECLINED\x10\x05\x12\x1b\n" +
	"\x17REQUEST_TRANSFER_INITED\x10\x06\x12\x1e\n" +
	"\x1aREQUEST_TRANSFER_COMPLETED\x10\a\x12\x1b\n" +
	"\x17REQUEST_TRANSFER_FAILED\x10\b\x12\x14\n" +
	"\x10REQUEST_CANCELED\x10\t\x12\x13\n" +
	"\x0fREQUEST_EXPIRED\x10\n" +
	"\x12\x13\n" +
	"\x0fTRANSFER_INITED\x10\v\x12\x14\n" +
	"\x10TRANSFER_PENDING\x10\f\x12+\n" +
	"'TRANSFER_PENDING_RECIPIENT_VERIFICATION\x10\r\x12\x15\n" +
	"\x11TRANSFER_CANCELED\x10\x0e\x12\x16\n" +
	"\x12TRANSFER_COMPLETED\x10\x0f\x12;\n" +
	"7TRANSFER_NO_RECEIVER_CREDENTIAL_NO_RTS_PENDING_CANCELED\x10\x10\x128\n" +
	"4TRANSFER_NO_RECEIVER_CREDENTIAL_NO_RTS_PENDING_OTHER\x10\x11\x12\x15\n" +
	"\x11TRANSFER_REFUNDED\x10\x12\x12\x1b\n" +
	"\x17TRANSFER_PARTIAL_REFUND\x10\x13\x12\x19\n" +
	"\x15TRANSFER_CHARGED_BACK\x10\x14\x12\x14\n" +
	"\x10TRANSFER_EXPIRED\x10\x15\x12\x15\n" +
	"\x11TRANSFER_DECLINED\x10\x16\x12\x18\n" +
	"\x14TRANSFER_UNAVAILABLE\x10\x17\x1a8\n" +
	"\x1aNetworkVerificationMessage\x12\x1a\n" +
	"\bcodeText\x18\x01 \x01(\tR\bcodeText\x1a\xd4\x02\n" +
	"\x10NoteReplyMessage\x129\n" +
	"\vtextContent\x18\x04 \x01(\v2\x15.WACommon.MessageTextH\x00R\vtextContent\x12?\n" +
	"\x0estickerContent\x18\x05 \x01(\v2\x15.WACommon.SubProtocolH\x00R\x0estickerContent\x12;\n" +
	"\fvideoContent\x18\x06 \x01(\v2\x15.WACommon.SubProtocolH\x00R\fvideoContent\x12\x16\n" +
	"\x06noteID\x18\x01 \x01(\tR\x06noteID\x121\n" +
	"\bnoteText\x18\x02 \x01(\v2\x15.WACommon.MessageTextR\bnoteText\x12(\n" +
	"\x0fnoteTimestampMS\x18\x03 \x01(\x03R\x0fnoteTimestampMSB\x12\n" +
	"\x10noteReplyContent\x1a=\n" +
	"\x13BumpExistingMessage\x12&\n" +
	"\x03key\x18\x01 \x01(\v2\x14.WACommon.MessageKeyR\x03key\x1aD\n" +
	"\x13ImageGalleryMessage\x12-\n" +
	"\x06images\x18\x01 \x03(\v2\x15.WACommon.SubProtocolR\x06images\x1a\xc3\x01\n" +
	"\x10ScreenshotAction\x12q\n" +
	"\x0escreenshotType\x18\x01 \x01(\x0e2I.WAArmadilloApplication.Armadillo.Content.ScreenshotAction.ScreenshotTypeR\x0escreenshotType\"<\n" +
	"\x0eScreenshotType\x12\x14\n" +
	"\x10SCREENSHOT_IMAGE\x10\x01\x12\x14\n" +
	"\x10SCREEN_RECORDING\x10\x02\x1a\xf3\x01\n" +
	"\x1eExtendedContentMessageWithSear\x12\x16\n" +
	"\x06searID\x18\x01 \x01(\tR\x06searID\x12\x18\n" +
	"\apayload\x18\x02 \x01(\fR\apayload\x12\x1c\n" +
	"\tnativeURL\x18\x03 \x01(\tR\tnativeURL\x12K\n" +
	"\x15searAssociatedMessage\x18\x04 \x01(\v2\x15.WACommon.SubProtocolR\x15searAssociatedMessage\x124\n" +
	"\x15searSentWithMessageID\x18\x05 \x01(\tR\x15searSentWithMessageID\x1a\x96\x02\n" +
	"\x17RavenActionNotifMessage\x12&\n" +
	"\x03key\x18\x01 \x01(\v2\x14.WACommon.MessageKeyR\x03key\x12(\n" +
	"\x0factionTimestamp\x18\x02 \x01(\x03R\x0factionTimestamp\x12l\n" +
	"\n" +
	"actionType\x18\x03 \x01(\x0e2L.WAArmadilloApplication.Armadillo.Content.RavenActionNotifMessage.ActionTypeR\n" +
	"actionType\";\n" +
	"\n" +
	"ActionType\x12\n" +
	"\n" +
	"\x06PLAYED\x10\x00\x12\x0e\n" +
	"\n" +
	"SCREENSHOT\x10\x01\x12\x11\n" +
	"\rFORCE_DISABLE\x10\x02\x1a\xc8\x02\n" +
	"\fRavenMessage\x12;\n" +
	"\fimageMessage\x18\x02 \x01(\v2\x15.WACommon.SubProtocolH\x00R\fimageMessage\x12;\n" +
	"\fvideoMessage\x18\x03 \x01(\v2\x15.WACommon.SubProtocolH\x00R\fvideoMessage\x12j\n" +
	"\rephemeralType\x18\x01 \x01(\x0e2D.WAArmadilloApplication.Armadillo.Content.RavenMessage.EphemeralTypeR\rephemeralType\"B\n" +
	"\rEphemeralType\x12\r\n" +
	"\tVIEW_ONCE\x10\x00\x12\x10\n" +
	"\fALLOW_REPLAY\x10\x01\x12\x10\n" +
	"\fKEEP_IN_CHAT\x10\x02B\x0e\n" +
	"\fmediaContent\x1a\xb6\x01\n" +
	"\rCommonSticker\x12e\n" +
	"\vstickerType\x18\x01 \x01(\x0e2C.WAArmadilloApplication.Armadillo.Content.CommonSticker.StickerTypeR\vstickerType\">\n" +
	"\vStickerType\x12\x0e\n" +
	"\n" +
	"SMALL_LIKE\x10\x01\x12\x0f\n" +
	"\vMEDIUM_LIKE\x10\x02\x12\x0e\n" +
	"\n" +
	"LARGE_LIKE\x10\x03B\t\n" +
	"\acontentB2Z0go.mau.fi/whatsmeow/proto/waArmadilloApplication"

var (
	file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescOnce sync.Once
	file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescData []byte
)

func file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescGZIP() []byte {
	file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescOnce.Do(func() {
		file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waArmadilloApplication_WAArmadilloApplication_proto_rawDesc), len(file_waArmadilloApplication_WAArmadilloApplication_proto_rawDesc)))
	})
	return file_waArmadilloApplication_WAArmadilloApplication_proto_rawDescData
}

var file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_waArmadilloApplication_WAArmadilloApplication_proto_goTypes = []any{
	(Armadillo_Signal_EncryptedBackupsSecrets_Epoch_EpochStatus)(0), // 0: WAArmadilloApplication.Armadillo.Signal.EncryptedBackupsSecrets.Epoch.EpochStatus
	(Armadillo_Content_PaymentsTransactionMessage_PaymentStatus)(0), // 1: WAArmadilloApplication.Armadillo.Content.PaymentsTransactionMessage.PaymentStatus
	(Armadillo_Content_ScreenshotAction_ScreenshotType)(0),          // 2: WAArmadilloApplication.Armadillo.Content.ScreenshotAction.ScreenshotType
	(Armadillo_Content_RavenActionNotifMessage_ActionType)(0),       // 3: WAArmadilloApplication.Armadillo.Content.RavenActionNotifMessage.ActionType
	(Armadillo_Content_RavenMessage_EphemeralType)(0),               // 4: WAArmadilloApplication.Armadillo.Content.RavenMessage.EphemeralType
	(Armadillo_Content_CommonSticker_StickerType)(0),                // 5: WAArmadilloApplication.Armadillo.Content.CommonSticker.StickerType
	(*Armadillo)(nil),                                                                          // 6: WAArmadilloApplication.Armadillo
	(*Armadillo_Metadata)(nil),                                                                 // 7: WAArmadilloApplication.Armadillo.Metadata
	(*Armadillo_Payload)(nil),                                                                  // 8: WAArmadilloApplication.Armadillo.Payload
	(*Armadillo_SubProtocolPayload)(nil),                                                       // 9: WAArmadilloApplication.Armadillo.SubProtocolPayload
	(*Armadillo_Signal)(nil),                                                                   // 10: WAArmadilloApplication.Armadillo.Signal
	(*Armadillo_ApplicationData)(nil),                                                          // 11: WAArmadilloApplication.Armadillo.ApplicationData
	(*Armadillo_Content)(nil),                                                                  // 12: WAArmadilloApplication.Armadillo.Content
	(*Armadillo_Signal_EncryptedBackupsSecrets)(nil),                                           // 13: WAArmadilloApplication.Armadillo.Signal.EncryptedBackupsSecrets
	(*Armadillo_Signal_EncryptedBackupsSecrets_Epoch)(nil),                                     // 14: WAArmadilloApplication.Armadillo.Signal.EncryptedBackupsSecrets.Epoch
	(*Armadillo_ApplicationData_MessageHistoryDocumentMessage)(nil),                            // 15: WAArmadilloApplication.Armadillo.ApplicationData.MessageHistoryDocumentMessage
	(*Armadillo_ApplicationData_AIBotResponseMessage)(nil),                                     // 16: WAArmadilloApplication.Armadillo.ApplicationData.AIBotResponseMessage
	(*Armadillo_ApplicationData_MetadataSyncAction)(nil),                                       // 17: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction
	(*Armadillo_ApplicationData_MetadataSyncNotification)(nil),                                 // 18: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncNotification
	(*Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction)(nil),                     // 19: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncMessageAction
	(*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction)(nil),                        // 20: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction
	(*Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessage)(nil),                     // 21: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessage
	(*Armadillo_ApplicationData_MetadataSyncAction_SyncActionMessageRange)(nil),                // 22: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessageRange
	(*Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_ActionMessageDelete)(nil), // 23: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncMessageAction.ActionMessageDelete
	(*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatRead)(nil),         // 24: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatRead
	(*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatDelete)(nil),       // 25: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatDelete
	(*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ActionChatArchive)(nil),      // 26: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatArchive
	(*Armadillo_Content_PaymentsTransactionMessage)(nil),                                       // 27: WAArmadilloApplication.Armadillo.Content.PaymentsTransactionMessage
	(*Armadillo_Content_NetworkVerificationMessage)(nil),                                       // 28: WAArmadilloApplication.Armadillo.Content.NetworkVerificationMessage
	(*Armadillo_Content_NoteReplyMessage)(nil),                                                 // 29: WAArmadilloApplication.Armadillo.Content.NoteReplyMessage
	(*Armadillo_Content_BumpExistingMessage)(nil),                                              // 30: WAArmadilloApplication.Armadillo.Content.BumpExistingMessage
	(*Armadillo_Content_ImageGalleryMessage)(nil),                                              // 31: WAArmadilloApplication.Armadillo.Content.ImageGalleryMessage
	(*Armadillo_Content_ScreenshotAction)(nil),                                                 // 32: WAArmadilloApplication.Armadillo.Content.ScreenshotAction
	(*Armadillo_Content_ExtendedContentMessageWithSear)(nil),                                   // 33: WAArmadilloApplication.Armadillo.Content.ExtendedContentMessageWithSear
	(*Armadillo_Content_RavenActionNotifMessage)(nil),                                          // 34: WAArmadilloApplication.Armadillo.Content.RavenActionNotifMessage
	(*Armadillo_Content_RavenMessage)(nil),                                                     // 35: WAArmadilloApplication.Armadillo.Content.RavenMessage
	(*Armadillo_Content_CommonSticker)(nil),                                                    // 36: WAArmadilloApplication.Armadillo.Content.CommonSticker
	(waCommon.FutureProofBehavior)(0),                                                          // 37: WACommon.FutureProofBehavior
	(*waArmadilloXMA.ExtendedContentMessage)(nil),                                              // 38: WAArmadilloXMA.ExtendedContentMessage
	(*waCommon.SubProtocol)(nil),                                                               // 39: WACommon.SubProtocol
	(*waCommon.MessageKey)(nil),                                                                // 40: WACommon.MessageKey
	(*waCommon.MessageText)(nil),                                                               // 41: WACommon.MessageText
}
var file_waArmadilloApplication_WAArmadilloApplication_proto_depIdxs = []int32{
	8,  // 0: WAArmadilloApplication.Armadillo.payload:type_name -> WAArmadilloApplication.Armadillo.Payload
	7,  // 1: WAArmadilloApplication.Armadillo.metadata:type_name -> WAArmadilloApplication.Armadillo.Metadata
	12, // 2: WAArmadilloApplication.Armadillo.Payload.content:type_name -> WAArmadilloApplication.Armadillo.Content
	11, // 3: WAArmadilloApplication.Armadillo.Payload.applicationData:type_name -> WAArmadilloApplication.Armadillo.ApplicationData
	10, // 4: WAArmadilloApplication.Armadillo.Payload.signal:type_name -> WAArmadilloApplication.Armadillo.Signal
	9,  // 5: WAArmadilloApplication.Armadillo.Payload.subProtocol:type_name -> WAArmadilloApplication.Armadillo.SubProtocolPayload
	37, // 6: WAArmadilloApplication.Armadillo.SubProtocolPayload.futureProof:type_name -> WACommon.FutureProofBehavior
	13, // 7: WAArmadilloApplication.Armadillo.Signal.encryptedBackupsSecrets:type_name -> WAArmadilloApplication.Armadillo.Signal.EncryptedBackupsSecrets
	18, // 8: WAArmadilloApplication.Armadillo.ApplicationData.metadataSync:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncNotification
	16, // 9: WAArmadilloApplication.Armadillo.ApplicationData.aiBotResponse:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.AIBotResponseMessage
	15, // 10: WAArmadilloApplication.Armadillo.ApplicationData.messageHistoryDocumentMessage:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MessageHistoryDocumentMessage
	36, // 11: WAArmadilloApplication.Armadillo.Content.commonSticker:type_name -> WAArmadilloApplication.Armadillo.Content.CommonSticker
	32, // 12: WAArmadilloApplication.Armadillo.Content.screenshotAction:type_name -> WAArmadilloApplication.Armadillo.Content.ScreenshotAction
	38, // 13: WAArmadilloApplication.Armadillo.Content.extendedContentMessage:type_name -> WAArmadilloXMA.ExtendedContentMessage
	35, // 14: WAArmadilloApplication.Armadillo.Content.ravenMessage:type_name -> WAArmadilloApplication.Armadillo.Content.RavenMessage
	34, // 15: WAArmadilloApplication.Armadillo.Content.ravenActionNotifMessage:type_name -> WAArmadilloApplication.Armadillo.Content.RavenActionNotifMessage
	33, // 16: WAArmadilloApplication.Armadillo.Content.extendedMessageContentWithSear:type_name -> WAArmadilloApplication.Armadillo.Content.ExtendedContentMessageWithSear
	31, // 17: WAArmadilloApplication.Armadillo.Content.imageGalleryMessage:type_name -> WAArmadilloApplication.Armadillo.Content.ImageGalleryMessage
	27, // 18: WAArmadilloApplication.Armadillo.Content.paymentsTransactionMessage:type_name -> WAArmadilloApplication.Armadillo.Content.PaymentsTransactionMessage
	30, // 19: WAArmadilloApplication.Armadillo.Content.bumpExistingMessage:type_name -> WAArmadilloApplication.Armadillo.Content.BumpExistingMessage
	29, // 20: WAArmadilloApplication.Armadillo.Content.noteReplyMessage:type_name -> WAArmadilloApplication.Armadillo.Content.NoteReplyMessage
	35, // 21: WAArmadilloApplication.Armadillo.Content.ravenMessageMsgr:type_name -> WAArmadilloApplication.Armadillo.Content.RavenMessage
	28, // 22: WAArmadilloApplication.Armadillo.Content.networkVerificationMessage:type_name -> WAArmadilloApplication.Armadillo.Content.NetworkVerificationMessage
	14, // 23: WAArmadilloApplication.Armadillo.Signal.EncryptedBackupsSecrets.epoch:type_name -> WAArmadilloApplication.Armadillo.Signal.EncryptedBackupsSecrets.Epoch
	0,  // 24: WAArmadilloApplication.Armadillo.Signal.EncryptedBackupsSecrets.Epoch.status:type_name -> WAArmadilloApplication.Armadillo.Signal.EncryptedBackupsSecrets.Epoch.EpochStatus
	39, // 25: WAArmadilloApplication.Armadillo.ApplicationData.MessageHistoryDocumentMessage.document:type_name -> WACommon.SubProtocol
	20, // 26: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.chatAction:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction
	19, // 27: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.messageAction:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncMessageAction
	17, // 28: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncNotification.actions:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction
	23, // 29: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncMessageAction.messageDelete:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncMessageAction.ActionMessageDelete
	40, // 30: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncMessageAction.key:type_name -> WACommon.MessageKey
	26, // 31: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.chatArchive:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatArchive
	25, // 32: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.chatDelete:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatDelete
	24, // 33: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.chatRead:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatRead
	40, // 34: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessage.key:type_name -> WACommon.MessageKey
	21, // 35: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessageRange.messages:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessage
	22, // 36: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatRead.messageRange:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessageRange
	22, // 37: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatDelete.messageRange:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessageRange
	22, // 38: WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncChatAction.ActionChatArchive.messageRange:type_name -> WAArmadilloApplication.Armadillo.ApplicationData.MetadataSyncAction.SyncActionMessageRange
	1,  // 39: WAArmadilloApplication.Armadillo.Content.PaymentsTransactionMessage.paymentStatus:type_name -> WAArmadilloApplication.Armadillo.Content.PaymentsTransactionMessage.PaymentStatus
	38, // 40: WAArmadilloApplication.Armadillo.Content.PaymentsTransactionMessage.extendedContentMessage:type_name -> WAArmadilloXMA.ExtendedContentMessage
	41, // 41: WAArmadilloApplication.Armadillo.Content.NoteReplyMessage.textContent:type_name -> WACommon.MessageText
	39, // 42: WAArmadilloApplication.Armadillo.Content.NoteReplyMessage.stickerContent:type_name -> WACommon.SubProtocol
	39, // 43: WAArmadilloApplication.Armadillo.Content.NoteReplyMessage.videoContent:type_name -> WACommon.SubProtocol
	41, // 44: WAArmadilloApplication.Armadillo.Content.NoteReplyMessage.noteText:type_name -> WACommon.MessageText
	40, // 45: WAArmadilloApplication.Armadillo.Content.BumpExistingMessage.key:type_name -> WACommon.MessageKey
	39, // 46: WAArmadilloApplication.Armadillo.Content.ImageGalleryMessage.images:type_name -> WACommon.SubProtocol
	2,  // 47: WAArmadilloApplication.Armadillo.Content.ScreenshotAction.screenshotType:type_name -> WAArmadilloApplication.Armadillo.Content.ScreenshotAction.ScreenshotType
	39, // 48: WAArmadilloApplication.Armadillo.Content.ExtendedContentMessageWithSear.searAssociatedMessage:type_name -> WACommon.SubProtocol
	40, // 49: WAArmadilloApplication.Armadillo.Content.RavenActionNotifMessage.key:type_name -> WACommon.MessageKey
	3,  // 50: WAArmadilloApplication.Armadillo.Content.RavenActionNotifMessage.actionType:type_name -> WAArmadilloApplication.Armadillo.Content.RavenActionNotifMessage.ActionType
	39, // 51: WAArmadilloApplication.Armadillo.Content.RavenMessage.imageMessage:type_name -> WACommon.SubProtocol
	39, // 52: WAArmadilloApplication.Armadillo.Content.RavenMessage.videoMessage:type_name -> WACommon.SubProtocol
	4,  // 53: WAArmadilloApplication.Armadillo.Content.RavenMessage.ephemeralType:type_name -> WAArmadilloApplication.Armadillo.Content.RavenMessage.EphemeralType
	5,  // 54: WAArmadilloApplication.Armadillo.Content.CommonSticker.stickerType:type_name -> WAArmadilloApplication.Armadillo.Content.CommonSticker.StickerType
	55, // [55:55] is the sub-list for method output_type
	55, // [55:55] is the sub-list for method input_type
	55, // [55:55] is the sub-list for extension type_name
	55, // [55:55] is the sub-list for extension extendee
	0,  // [0:55] is the sub-list for field type_name
}

func init() { file_waArmadilloApplication_WAArmadilloApplication_proto_init() }
func file_waArmadilloApplication_WAArmadilloApplication_proto_init() {
	if File_waArmadilloApplication_WAArmadilloApplication_proto != nil {
		return
	}
	file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[2].OneofWrappers = []any{
		(*Armadillo_Payload_Content)(nil),
		(*Armadillo_Payload_ApplicationData)(nil),
		(*Armadillo_Payload_Signal)(nil),
		(*Armadillo_Payload_SubProtocol)(nil),
	}
	file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[4].OneofWrappers = []any{
		(*Armadillo_Signal_EncryptedBackupsSecrets_)(nil),
	}
	file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[5].OneofWrappers = []any{
		(*Armadillo_ApplicationData_MetadataSync)(nil),
		(*Armadillo_ApplicationData_AiBotResponse)(nil),
		(*Armadillo_ApplicationData_MessageHistoryDocumentMessage_)(nil),
	}
	file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[6].OneofWrappers = []any{
		(*Armadillo_Content_CommonSticker_)(nil),
		(*Armadillo_Content_ScreenshotAction_)(nil),
		(*Armadillo_Content_ExtendedContentMessage)(nil),
		(*Armadillo_Content_RavenMessage_)(nil),
		(*Armadillo_Content_RavenActionNotifMessage_)(nil),
		(*Armadillo_Content_ExtendedMessageContentWithSear)(nil),
		(*Armadillo_Content_ImageGalleryMessage_)(nil),
		(*Armadillo_Content_PaymentsTransactionMessage_)(nil),
		(*Armadillo_Content_BumpExistingMessage_)(nil),
		(*Armadillo_Content_NoteReplyMessage_)(nil),
		(*Armadillo_Content_RavenMessageMsgr)(nil),
		(*Armadillo_Content_NetworkVerificationMessage_)(nil),
	}
	file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[11].OneofWrappers = []any{
		(*Armadillo_ApplicationData_MetadataSyncAction_ChatAction)(nil),
		(*Armadillo_ApplicationData_MetadataSyncAction_MessageAction)(nil),
	}
	file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[13].OneofWrappers = []any{
		(*Armadillo_ApplicationData_MetadataSyncAction_SyncMessageAction_MessageDelete)(nil),
	}
	file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[14].OneofWrappers = []any{
		(*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatArchive)(nil),
		(*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatDelete)(nil),
		(*Armadillo_ApplicationData_MetadataSyncAction_SyncChatAction_ChatRead)(nil),
	}
	file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[23].OneofWrappers = []any{
		(*Armadillo_Content_NoteReplyMessage_TextContent)(nil),
		(*Armadillo_Content_NoteReplyMessage_StickerContent)(nil),
		(*Armadillo_Content_NoteReplyMessage_VideoContent)(nil),
	}
	file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes[29].OneofWrappers = []any{
		(*Armadillo_Content_RavenMessage_ImageMessage)(nil),
		(*Armadillo_Content_RavenMessage_VideoMessage)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waArmadilloApplication_WAArmadilloApplication_proto_rawDesc), len(file_waArmadilloApplication_WAArmadilloApplication_proto_rawDesc)),
			NumEnums:      6,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waArmadilloApplication_WAArmadilloApplication_proto_goTypes,
		DependencyIndexes: file_waArmadilloApplication_WAArmadilloApplication_proto_depIdxs,
		EnumInfos:         file_waArmadilloApplication_WAArmadilloApplication_proto_enumTypes,
		MessageInfos:      file_waArmadilloApplication_WAArmadilloApplication_proto_msgTypes,
	}.Build()
	File_waArmadilloApplication_WAArmadilloApplication_proto = out.File
	file_waArmadilloApplication_WAArmadilloApplication_proto_goTypes = nil
	file_waArmadilloApplication_WAArmadilloApplication_proto_depIdxs = nil
}
