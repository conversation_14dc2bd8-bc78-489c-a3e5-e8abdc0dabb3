use std::io::Result;

fn main() -> Result<()> {
    // Protocol buffer files to compile
    let proto_files = [
        "proto/wa_e2e.proto",
        "proto/wa_web.proto",
        "proto/wa_wa6.proto",
        "proto/wa_common.proto",
        "proto/wa_adv.proto",
    ];

    // Check if proto files exist, if not create placeholder structure
    std::fs::create_dir_all("proto").unwrap_or_default();

    // Only compile if proto files exist
    let existing_protos: Vec<&str> = proto_files
        .iter()
        .filter(|&&file| std::path::Path::new(file).exists())
        .copied()
        .collect();

    if !existing_protos.is_empty() {
        prost_build::Config::new()
            .bytes(["."])
            .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
            .compile_protos(&existing_protos, &["proto/"])?;
    } else {
        // Create a placeholder proto file for initial compilation
        std::fs::write(
            "proto/placeholder.proto",
            r#"
syntax = "proto3";

package placeholder;

message Empty {}
"#,
        )?;

        prost_build::Config::new().compile_protos(&["proto/placeholder.proto"], &["proto/"])?;
    }

    Ok(())
}
