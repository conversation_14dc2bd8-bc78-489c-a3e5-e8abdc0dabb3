//! In-memory storage implementation for testing

use crate::error::StoreError;
use crate::traits::{
    DeviceStore, IdentityStore, PreKey, PreKeyStore, SenderKeyStore, SessionStore,
};
use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use whatsmeow_types::Device;

/// In-memory storage implementation
#[derive(Debu<PERSON>, Default)]
pub struct MemoryStore {
    device: Arc<RwLock<Option<Device>>>,
    sessions: Arc<RwLock<HashMap<String, Vec<u8>>>>,
    prekeys: Arc<RwLock<HashMap<u32, PreKey>>>,
    sender_keys: Arc<RwLock<HashMap<String, Vec<u8>>>>,
    identity_keys: Arc<RwLock<HashMap<String, Vec<u8>>>>,
}

impl MemoryStore {
    pub fn new() -> Self {
        Self::default()
    }
}

#[async_trait]
impl DeviceStore for MemoryStore {
    async fn get_device(&self) -> Result<Option<Device>, StoreError> {
        let device = self.device.read().await;
        Ok(device.clone())
    }

    async fn put_device(&self, device: &Device) -> Result<(), StoreError> {
        let mut store_device = self.device.write().await;
        *store_device = Some(device.clone());
        Ok(())
    }

    async fn delete_device(&self) -> Result<(), StoreError> {
        let mut device = self.device.write().await;
        *device = None;
        Ok(())
    }
}

#[async_trait]
impl SessionStore for MemoryStore {
    async fn get_session(&self, address: &str) -> Result<Option<Vec<u8>>, StoreError> {
        let sessions = self.sessions.read().await;
        Ok(sessions.get(address).cloned())
    }

    async fn put_session(&self, address: &str, session: &[u8]) -> Result<(), StoreError> {
        let mut sessions = self.sessions.write().await;
        sessions.insert(address.to_string(), session.to_vec());
        Ok(())
    }

    async fn delete_session(&self, address: &str) -> Result<(), StoreError> {
        let mut sessions = self.sessions.write().await;
        sessions.remove(address);
        Ok(())
    }

    async fn delete_all_sessions(&self, phone: &str) -> Result<(), StoreError> {
        let mut sessions = self.sessions.write().await;
        sessions.retain(|key, _| !key.starts_with(phone));
        Ok(())
    }
}

#[async_trait]
impl PreKeyStore for MemoryStore {
    async fn get_or_gen_prekeys(&self, count: u32) -> Result<Vec<PreKey>, StoreError> {
        let mut prekeys = self.prekeys.write().await;
        let mut result = Vec::new();

        // Generate new prekeys if needed
        let start_id = prekeys.len() as u32;
        for i in 0..count {
            let id = start_id + i;
            let prekey = PreKey::new(id, vec![0; 32]); // Placeholder key data
            prekeys.insert(id, prekey.clone());
            result.push(prekey);
        }

        Ok(result)
    }

    async fn get_prekey(&self, id: u32) -> Result<Option<PreKey>, StoreError> {
        let prekeys = self.prekeys.read().await;
        Ok(prekeys.get(&id).cloned())
    }

    async fn remove_prekey(&self, id: u32) -> Result<(), StoreError> {
        let mut prekeys = self.prekeys.write().await;
        prekeys.remove(&id);
        Ok(())
    }

    async fn mark_prekeys_as_uploaded(&self, up_to_id: u32) -> Result<(), StoreError> {
        let mut prekeys = self.prekeys.write().await;
        for (id, prekey) in prekeys.iter_mut() {
            if *id <= up_to_id {
                prekey.uploaded = true;
            }
        }
        Ok(())
    }
}

#[async_trait]
impl SenderKeyStore for MemoryStore {
    async fn get_sender_key(
        &self,
        group_id: &str,
        sender_id: &str,
    ) -> Result<Option<Vec<u8>>, StoreError> {
        let key = format!("{}:{}", group_id, sender_id);
        let sender_keys = self.sender_keys.read().await;
        Ok(sender_keys.get(&key).cloned())
    }

    async fn put_sender_key(
        &self,
        group_id: &str,
        sender_id: &str,
        key: &[u8],
    ) -> Result<(), StoreError> {
        let key_id = format!("{}:{}", group_id, sender_id);
        let mut sender_keys = self.sender_keys.write().await;
        sender_keys.insert(key_id, key.to_vec());
        Ok(())
    }
}

#[async_trait]
impl IdentityStore for MemoryStore {
    async fn get_identity_key(&self, address: &str) -> Result<Option<Vec<u8>>, StoreError> {
        let identity_keys = self.identity_keys.read().await;
        Ok(identity_keys.get(address).cloned())
    }

    async fn put_identity_key(&self, address: &str, key: &[u8]) -> Result<(), StoreError> {
        let mut identity_keys = self.identity_keys.write().await;
        identity_keys.insert(address.to_string(), key.to_vec());
        Ok(())
    }

    async fn is_trusted_identity(&self, address: &str, key: &[u8]) -> Result<bool, StoreError> {
        let identity_keys = self.identity_keys.read().await;
        match identity_keys.get(address) {
            Some(stored_key) => Ok(stored_key == key),
            None => Ok(true), // Trust on first use
        }
    }
}
