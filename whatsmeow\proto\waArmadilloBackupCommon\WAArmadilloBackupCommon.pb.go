// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waArmadilloBackupCommon/WAArmadilloBackupCommon.proto

package waArmadilloBackupCommon

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Subprotocol struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Payload       []byte                 `protobuf:"bytes,1,opt,name=payload" json:"payload,omitempty"`
	Version       *int32                 `protobuf:"varint,2,opt,name=version" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Subprotocol) Reset() {
	*x = Subprotocol{}
	mi := &file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Subprotocol) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Subprotocol) ProtoMessage() {}

func (x *Subprotocol) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Subprotocol.ProtoReflect.Descriptor instead.
func (*Subprotocol) Descriptor() ([]byte, []int) {
	return file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDescGZIP(), []int{0}
}

func (x *Subprotocol) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *Subprotocol) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

var File_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto protoreflect.FileDescriptor

const file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDesc = "" +
	"\n" +
	"5waArmadilloBackupCommon/WAArmadilloBackupCommon.proto\x12\x17WAArmadilloBackupCommon\"A\n" +
	"\vSubprotocol\x12\x18\n" +
	"\apayload\x18\x01 \x01(\fR\apayload\x12\x18\n" +
	"\aversion\x18\x02 \x01(\x05R\aversionB3Z1go.mau.fi/whatsmeow/proto/waArmadilloBackupCommon"

var (
	file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDescOnce sync.Once
	file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDescData []byte
)

func file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDescGZIP() []byte {
	file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDescOnce.Do(func() {
		file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDesc), len(file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDesc)))
	})
	return file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDescData
}

var file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_goTypes = []any{
	(*Subprotocol)(nil), // 0: WAArmadilloBackupCommon.Subprotocol
}
var file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_init() }
func file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_init() {
	if File_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDesc), len(file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_goTypes,
		DependencyIndexes: file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_depIdxs,
		MessageInfos:      file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_msgTypes,
	}.Build()
	File_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto = out.File
	file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_goTypes = nil
	file_waArmadilloBackupCommon_WAArmadilloBackupCommon_proto_depIdxs = nil
}
