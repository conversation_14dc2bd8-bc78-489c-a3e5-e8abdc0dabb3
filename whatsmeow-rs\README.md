# whatsmeow-rs

A Rust implementation of the WhatsApp Web multidevice API, translated from the Go [whatsmeow](https://github.com/tulir/whatsmeow) library.

## Overview

whatsmeow-rs provides a comprehensive client for interacting with WhatsApp's Web API, supporting:

- Message sending and receiving (text, media, documents)
- Group management (create, modify, participants)
- Media upload and download with encryption
- App state synchronization
- Presence and receipt handling
- End-to-end encryption using Signal protocol
- Transport encryption using Noise protocol

## Architecture

The library is organized as a Cargo workspace with multiple crates:

- `whatsmeow-rs` - Main client library and public API
- `whatsmeow-proto` - Protocol buffer definitions and generated code
- `whatsmeow-crypto` - Cryptographic operations (Signal, Noise)
- `whatsmeow-binary` - Binary node parsing and serialization
- `whatsmeow-store` - Storage interfaces and implementations
- `whatsmeow-types` - Common types and utilities

## Usage

```rust
use whatsmeow_rs::{Client, ClientConfig};
use whatsmeow_store::MemoryStore;
use std::sync::Arc;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let store = Arc::new(MemoryStore::new());
    let client = Client::new(store);
    
    // Connect to WhatsApp
    client.connect().await?;
    
    // Add event handler
    client.add_event_handler(Box::new(|event| {
        println!("Received event: {:?}", event);
        true // Continue processing events
    })).await;
    
    Ok(())
}
```

## Development Status

This is a work-in-progress translation from the Go whatsmeow library. The project structure and basic types are implemented, but core functionality is still being developed.

## License

MIT License - see LICENSE file for details.