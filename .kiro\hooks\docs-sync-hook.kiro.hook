{"enabled": true, "name": "Documentation Sync", "description": "Listens to source file changes in Go and Rust projects and triggers documentation updates in README files", "version": "1", "when": {"type": "fileEdited", "patterns": ["whatsmeow/*.go", "whatsmeow-rs/src/**/*.rs", "whatsmeow-rs/Cargo.toml", "whatsmeow/go.mod"]}, "then": {"type": "askAgent", "prompt": "Source files have been modified in the repository. Please review the changes and update the relevant documentation. Specifically:\n\n1. Check if the README.md files in whatsmeow/ and whatsmeow-rs/ need updates to reflect any API changes, new features, or modified functionality\n2. Look for any new public functions, structs, or modules that should be documented\n3. Update examples if the API has changed\n4. Ensure that any breaking changes are properly documented\n5. Update version information if applicable\n\nFocus on keeping the documentation accurate and helpful for users of these libraries."}}