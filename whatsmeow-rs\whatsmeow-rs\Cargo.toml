[package]
name = "whatsmeow-rs"
version = "0.1.0"
edition = "2021"
description = "Rust implementation of the WhatsApp Web multidevice API"
license = "MIT"
repository = "https://github.com/your-org/whatsmeow-rs"
keywords = ["whatsapp", "messaging", "api", "client"]
categories = ["api-bindings", "network-programming"]

[dependencies]
# Workspace dependencies
whatsmeow-proto = { path = "../whatsmeow-proto" }
whatsmeow-crypto = { path = "../whatsmeow-crypto" }
whatsmeow-binary = { path = "../whatsmeow-binary" }
whatsmeow-store = { path = "../whatsmeow-store" }
whatsmeow-types = { path = "../whatsmeow-types" }

# External dependencies
tokio = { workspace = true }
tokio-tungstenite = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }
uuid = { workspace = true }
bytes = { workspace = true }
tracing = { workspace = true }
async-trait = { workspace = true }
chrono = { workspace = true }

[dev-dependencies]
tokio-test = { workspace = true }
mockall = { workspace = true }
proptest = { workspace = true }
criterion = { workspace = true }

[[bench]]
name = "client_benchmarks"
harness = false

[[example]]
name = "basic_client"
path = "../examples/basic_client.rs"