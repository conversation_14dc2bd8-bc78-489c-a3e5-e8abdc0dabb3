[workspace]
members = [
    "whatsmeow-rs",
    "whatsmeow-proto",
    "whatsmeow-crypto",
    "whatsmeow-binary",
    "whatsmeow-store",
    "whatsmeow-types",
]

resolver = "2"

[workspace.dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = "0.27.0"

# Serialization
prost = "0.14.1"
prost-types = "0.14.1"

# Cryptography
ring = "0.17"
ed25519-dalek = "2.0"
x25519-dalek = "2.0"
aes-gcm = "0.10"
hkdf = "0.12"

# Error handling
thiserror = "2.0.12"
anyhow = "1.0"

# Utilities
uuid = { version = "1.0", features = ["v4"] }
base64 = "0.22.1"
hex = "0.4"
bytes = "1.0"
tracing = "0.1"

# Async traits
async-trait = "0.1"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }

# Testing dependencies
tokio-test = "0.4"
mockall = "0.13.1"
proptest = "1.7.0"
criterion = "0.6.0"

# Build dependencies
prost-build = "0.14.1"
