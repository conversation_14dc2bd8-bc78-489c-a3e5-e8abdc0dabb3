// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waArmadilloTransportEvent/WAArmadilloTransportEvent.proto

package waArmadilloTransportEvent

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TransportEvent_Event_IcdcAlert_Type int32

const (
	TransportEvent_Event_IcdcAlert_NONE     TransportEvent_Event_IcdcAlert_Type = 0
	TransportEvent_Event_IcdcAlert_DETECTED TransportEvent_Event_IcdcAlert_Type = 1
	TransportEvent_Event_IcdcAlert_CLEARED  TransportEvent_Event_IcdcAlert_Type = 2
)

// Enum value maps for TransportEvent_Event_IcdcAlert_Type.
var (
	TransportEvent_Event_IcdcAlert_Type_name = map[int32]string{
		0: "NONE",
		1: "DETECTED",
		2: "CLEARED",
	}
	TransportEvent_Event_IcdcAlert_Type_value = map[string]int32{
		"NONE":     0,
		"DETECTED": 1,
		"CLEARED":  2,
	}
)

func (x TransportEvent_Event_IcdcAlert_Type) Enum() *TransportEvent_Event_IcdcAlert_Type {
	p := new(TransportEvent_Event_IcdcAlert_Type)
	*p = x
	return p
}

func (x TransportEvent_Event_IcdcAlert_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransportEvent_Event_IcdcAlert_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_enumTypes[0].Descriptor()
}

func (TransportEvent_Event_IcdcAlert_Type) Type() protoreflect.EnumType {
	return &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_enumTypes[0]
}

func (x TransportEvent_Event_IcdcAlert_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *TransportEvent_Event_IcdcAlert_Type) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = TransportEvent_Event_IcdcAlert_Type(num)
	return nil
}

// Deprecated: Use TransportEvent_Event_IcdcAlert_Type.Descriptor instead.
func (TransportEvent_Event_IcdcAlert_Type) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescGZIP(), []int{0, 0, 0, 0}
}

type TransportEvent_Event_DeviceChange_Type int32

const (
	TransportEvent_Event_DeviceChange_NONE     TransportEvent_Event_DeviceChange_Type = 0
	TransportEvent_Event_DeviceChange_ADDED    TransportEvent_Event_DeviceChange_Type = 1
	TransportEvent_Event_DeviceChange_REMOVED  TransportEvent_Event_DeviceChange_Type = 2
	TransportEvent_Event_DeviceChange_REPLACED TransportEvent_Event_DeviceChange_Type = 3
)

// Enum value maps for TransportEvent_Event_DeviceChange_Type.
var (
	TransportEvent_Event_DeviceChange_Type_name = map[int32]string{
		0: "NONE",
		1: "ADDED",
		2: "REMOVED",
		3: "REPLACED",
	}
	TransportEvent_Event_DeviceChange_Type_value = map[string]int32{
		"NONE":     0,
		"ADDED":    1,
		"REMOVED":  2,
		"REPLACED": 3,
	}
)

func (x TransportEvent_Event_DeviceChange_Type) Enum() *TransportEvent_Event_DeviceChange_Type {
	p := new(TransportEvent_Event_DeviceChange_Type)
	*p = x
	return p
}

func (x TransportEvent_Event_DeviceChange_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransportEvent_Event_DeviceChange_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_enumTypes[1].Descriptor()
}

func (TransportEvent_Event_DeviceChange_Type) Type() protoreflect.EnumType {
	return &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_enumTypes[1]
}

func (x TransportEvent_Event_DeviceChange_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *TransportEvent_Event_DeviceChange_Type) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = TransportEvent_Event_DeviceChange_Type(num)
	return nil
}

// Deprecated: Use TransportEvent_Event_DeviceChange_Type.Descriptor instead.
func (TransportEvent_Event_DeviceChange_Type) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescGZIP(), []int{0, 0, 1, 0}
}

type TransportEvent_Placeholder_Type int32

const (
	TransportEvent_Placeholder_DECRYPTION_FAILURE  TransportEvent_Placeholder_Type = 1
	TransportEvent_Placeholder_UNAVAILABLE_MESSAGE TransportEvent_Placeholder_Type = 2
)

// Enum value maps for TransportEvent_Placeholder_Type.
var (
	TransportEvent_Placeholder_Type_name = map[int32]string{
		1: "DECRYPTION_FAILURE",
		2: "UNAVAILABLE_MESSAGE",
	}
	TransportEvent_Placeholder_Type_value = map[string]int32{
		"DECRYPTION_FAILURE":  1,
		"UNAVAILABLE_MESSAGE": 2,
	}
)

func (x TransportEvent_Placeholder_Type) Enum() *TransportEvent_Placeholder_Type {
	p := new(TransportEvent_Placeholder_Type)
	*p = x
	return p
}

func (x TransportEvent_Placeholder_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TransportEvent_Placeholder_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_enumTypes[2].Descriptor()
}

func (TransportEvent_Placeholder_Type) Type() protoreflect.EnumType {
	return &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_enumTypes[2]
}

func (x TransportEvent_Placeholder_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *TransportEvent_Placeholder_Type) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = TransportEvent_Placeholder_Type(num)
	return nil
}

// Deprecated: Use TransportEvent_Placeholder_Type.Descriptor instead.
func (TransportEvent_Placeholder_Type) EnumDescriptor() ([]byte, []int) {
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescGZIP(), []int{0, 1, 0}
}

type TransportEvent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Content:
	//
	//	*TransportEvent_Placeholder_
	//	*TransportEvent_Event_
	Content       isTransportEvent_Content `protobuf_oneof:"content"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransportEvent) Reset() {
	*x = TransportEvent{}
	mi := &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransportEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportEvent) ProtoMessage() {}

func (x *TransportEvent) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportEvent.ProtoReflect.Descriptor instead.
func (*TransportEvent) Descriptor() ([]byte, []int) {
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescGZIP(), []int{0}
}

func (x *TransportEvent) GetContent() isTransportEvent_Content {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *TransportEvent) GetPlaceholder() *TransportEvent_Placeholder {
	if x != nil {
		if x, ok := x.Content.(*TransportEvent_Placeholder_); ok {
			return x.Placeholder
		}
	}
	return nil
}

func (x *TransportEvent) GetEvent() *TransportEvent_Event {
	if x != nil {
		if x, ok := x.Content.(*TransportEvent_Event_); ok {
			return x.Event
		}
	}
	return nil
}

type isTransportEvent_Content interface {
	isTransportEvent_Content()
}

type TransportEvent_Placeholder_ struct {
	Placeholder *TransportEvent_Placeholder `protobuf:"bytes,1,opt,name=placeholder,oneof"`
}

type TransportEvent_Event_ struct {
	Event *TransportEvent_Event `protobuf:"bytes,2,opt,name=event,oneof"`
}

func (*TransportEvent_Placeholder_) isTransportEvent_Content() {}

func (*TransportEvent_Event_) isTransportEvent_Content() {}

type TransportEvent_Event struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Event:
	//
	//	*TransportEvent_Event_DeviceChange_
	//	*TransportEvent_Event_IcdcAlert_
	Event         isTransportEvent_Event_Event `protobuf_oneof:"event"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransportEvent_Event) Reset() {
	*x = TransportEvent_Event{}
	mi := &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransportEvent_Event) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportEvent_Event) ProtoMessage() {}

func (x *TransportEvent_Event) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportEvent_Event.ProtoReflect.Descriptor instead.
func (*TransportEvent_Event) Descriptor() ([]byte, []int) {
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescGZIP(), []int{0, 0}
}

func (x *TransportEvent_Event) GetEvent() isTransportEvent_Event_Event {
	if x != nil {
		return x.Event
	}
	return nil
}

func (x *TransportEvent_Event) GetDeviceChange() *TransportEvent_Event_DeviceChange {
	if x != nil {
		if x, ok := x.Event.(*TransportEvent_Event_DeviceChange_); ok {
			return x.DeviceChange
		}
	}
	return nil
}

func (x *TransportEvent_Event) GetIcdcAlert() *TransportEvent_Event_IcdcAlert {
	if x != nil {
		if x, ok := x.Event.(*TransportEvent_Event_IcdcAlert_); ok {
			return x.IcdcAlert
		}
	}
	return nil
}

type isTransportEvent_Event_Event interface {
	isTransportEvent_Event_Event()
}

type TransportEvent_Event_DeviceChange_ struct {
	DeviceChange *TransportEvent_Event_DeviceChange `protobuf:"bytes,1,opt,name=deviceChange,oneof"`
}

type TransportEvent_Event_IcdcAlert_ struct {
	IcdcAlert *TransportEvent_Event_IcdcAlert `protobuf:"bytes,2,opt,name=icdcAlert,oneof"`
}

func (*TransportEvent_Event_DeviceChange_) isTransportEvent_Event_Event() {}

func (*TransportEvent_Event_IcdcAlert_) isTransportEvent_Event_Event() {}

type TransportEvent_Placeholder struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Type          *TransportEvent_Placeholder_Type `protobuf:"varint,1,opt,name=type,enum=WAArmadilloTransportEvent.TransportEvent_Placeholder_Type" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransportEvent_Placeholder) Reset() {
	*x = TransportEvent_Placeholder{}
	mi := &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransportEvent_Placeholder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportEvent_Placeholder) ProtoMessage() {}

func (x *TransportEvent_Placeholder) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportEvent_Placeholder.ProtoReflect.Descriptor instead.
func (*TransportEvent_Placeholder) Descriptor() ([]byte, []int) {
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescGZIP(), []int{0, 1}
}

func (x *TransportEvent_Placeholder) GetType() TransportEvent_Placeholder_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return TransportEvent_Placeholder_DECRYPTION_FAILURE
}

type TransportEvent_Event_IcdcAlert struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	Type          *TransportEvent_Event_IcdcAlert_Type `protobuf:"varint,1,opt,name=type,enum=WAArmadilloTransportEvent.TransportEvent_Event_IcdcAlert_Type" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransportEvent_Event_IcdcAlert) Reset() {
	*x = TransportEvent_Event_IcdcAlert{}
	mi := &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransportEvent_Event_IcdcAlert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportEvent_Event_IcdcAlert) ProtoMessage() {}

func (x *TransportEvent_Event_IcdcAlert) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportEvent_Event_IcdcAlert.ProtoReflect.Descriptor instead.
func (*TransportEvent_Event_IcdcAlert) Descriptor() ([]byte, []int) {
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *TransportEvent_Event_IcdcAlert) GetType() TransportEvent_Event_IcdcAlert_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return TransportEvent_Event_IcdcAlert_NONE
}

type TransportEvent_Event_DeviceChange struct {
	state          protoimpl.MessageState                  `protogen:"open.v1"`
	Type           *TransportEvent_Event_DeviceChange_Type `protobuf:"varint,1,opt,name=type,enum=WAArmadilloTransportEvent.TransportEvent_Event_DeviceChange_Type" json:"type,omitempty"`
	DeviceName     *string                                 `protobuf:"bytes,2,opt,name=deviceName" json:"deviceName,omitempty"`
	DevicePlatform *string                                 `protobuf:"bytes,3,opt,name=devicePlatform" json:"devicePlatform,omitempty"`
	DeviceModel    *string                                 `protobuf:"bytes,4,opt,name=deviceModel" json:"deviceModel,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *TransportEvent_Event_DeviceChange) Reset() {
	*x = TransportEvent_Event_DeviceChange{}
	mi := &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransportEvent_Event_DeviceChange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportEvent_Event_DeviceChange) ProtoMessage() {}

func (x *TransportEvent_Event_DeviceChange) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportEvent_Event_DeviceChange.ProtoReflect.Descriptor instead.
func (*TransportEvent_Event_DeviceChange) Descriptor() ([]byte, []int) {
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescGZIP(), []int{0, 0, 1}
}

func (x *TransportEvent_Event_DeviceChange) GetType() TransportEvent_Event_DeviceChange_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return TransportEvent_Event_DeviceChange_NONE
}

func (x *TransportEvent_Event_DeviceChange) GetDeviceName() string {
	if x != nil && x.DeviceName != nil {
		return *x.DeviceName
	}
	return ""
}

func (x *TransportEvent_Event_DeviceChange) GetDevicePlatform() string {
	if x != nil && x.DevicePlatform != nil {
		return *x.DevicePlatform
	}
	return ""
}

func (x *TransportEvent_Event_DeviceChange) GetDeviceModel() string {
	if x != nil && x.DeviceModel != nil {
		return *x.DeviceModel
	}
	return ""
}

var File_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto protoreflect.FileDescriptor

const file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDesc = "" +
	"\n" +
	"9waArmadilloTransportEvent/WAArmadilloTransportEvent.proto\x12\x19WAArmadilloTransportEvent\"\xc3\a\n" +
	"\x0eTransportEvent\x12Y\n" +
	"\vplaceholder\x18\x01 \x01(\v25.WAArmadilloTransportEvent.TransportEvent.PlaceholderH\x00R\vplaceholder\x12G\n" +
	"\x05event\x18\x02 \x01(\v2/.WAArmadilloTransportEvent.TransportEvent.EventH\x00R\x05event\x1a\xe8\x04\n" +
	"\x05Event\x12b\n" +
	"\fdeviceChange\x18\x01 \x01(\v2<.WAArmadilloTransportEvent.TransportEvent.Event.DeviceChangeH\x00R\fdeviceChange\x12Y\n" +
	"\ticdcAlert\x18\x02 \x01(\v29.WAArmadilloTransportEvent.TransportEvent.Event.IcdcAlertH\x00R\ticdcAlert\x1a\x8c\x01\n" +
	"\tIcdcAlert\x12R\n" +
	"\x04type\x18\x01 \x01(\x0e2>.WAArmadilloTransportEvent.TransportEvent.Event.IcdcAlert.TypeR\x04type\"+\n" +
	"\x04Type\x12\b\n" +
	"\x04NONE\x10\x00\x12\f\n" +
	"\bDETECTED\x10\x01\x12\v\n" +
	"\aCLEARED\x10\x02\x1a\x87\x02\n" +
	"\fDeviceChange\x12U\n" +
	"\x04type\x18\x01 \x01(\x0e2A.WAArmadilloTransportEvent.TransportEvent.Event.DeviceChange.TypeR\x04type\x12\x1e\n" +
	"\n" +
	"deviceName\x18\x02 \x01(\tR\n" +
	"deviceName\x12&\n" +
	"\x0edevicePlatform\x18\x03 \x01(\tR\x0edevicePlatform\x12 \n" +
	"\vdeviceModel\x18\x04 \x01(\tR\vdeviceModel\"6\n" +
	"\x04Type\x12\b\n" +
	"\x04NONE\x10\x00\x12\t\n" +
	"\x05ADDED\x10\x01\x12\v\n" +
	"\aREMOVED\x10\x02\x12\f\n" +
	"\bREPLACED\x10\x03B\a\n" +
	"\x05event\x1a\x96\x01\n" +
	"\vPlaceholder\x12N\n" +
	"\x04type\x18\x01 \x01(\x0e2:.WAArmadilloTransportEvent.TransportEvent.Placeholder.TypeR\x04type\"7\n" +
	"\x04Type\x12\x16\n" +
	"\x12DECRYPTION_FAILURE\x10\x01\x12\x17\n" +
	"\x13UNAVAILABLE_MESSAGE\x10\x02B\t\n" +
	"\acontentB5Z3go.mau.fi/whatsmeow/proto/waArmadilloTransportEvent"

var (
	file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescOnce sync.Once
	file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescData []byte
)

func file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescGZIP() []byte {
	file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescOnce.Do(func() {
		file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDesc), len(file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDesc)))
	})
	return file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDescData
}

var file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_goTypes = []any{
	(TransportEvent_Event_IcdcAlert_Type)(0),    // 0: WAArmadilloTransportEvent.TransportEvent.Event.IcdcAlert.Type
	(TransportEvent_Event_DeviceChange_Type)(0), // 1: WAArmadilloTransportEvent.TransportEvent.Event.DeviceChange.Type
	(TransportEvent_Placeholder_Type)(0),        // 2: WAArmadilloTransportEvent.TransportEvent.Placeholder.Type
	(*TransportEvent)(nil),                      // 3: WAArmadilloTransportEvent.TransportEvent
	(*TransportEvent_Event)(nil),                // 4: WAArmadilloTransportEvent.TransportEvent.Event
	(*TransportEvent_Placeholder)(nil),          // 5: WAArmadilloTransportEvent.TransportEvent.Placeholder
	(*TransportEvent_Event_IcdcAlert)(nil),      // 6: WAArmadilloTransportEvent.TransportEvent.Event.IcdcAlert
	(*TransportEvent_Event_DeviceChange)(nil),   // 7: WAArmadilloTransportEvent.TransportEvent.Event.DeviceChange
}
var file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_depIdxs = []int32{
	5, // 0: WAArmadilloTransportEvent.TransportEvent.placeholder:type_name -> WAArmadilloTransportEvent.TransportEvent.Placeholder
	4, // 1: WAArmadilloTransportEvent.TransportEvent.event:type_name -> WAArmadilloTransportEvent.TransportEvent.Event
	7, // 2: WAArmadilloTransportEvent.TransportEvent.Event.deviceChange:type_name -> WAArmadilloTransportEvent.TransportEvent.Event.DeviceChange
	6, // 3: WAArmadilloTransportEvent.TransportEvent.Event.icdcAlert:type_name -> WAArmadilloTransportEvent.TransportEvent.Event.IcdcAlert
	2, // 4: WAArmadilloTransportEvent.TransportEvent.Placeholder.type:type_name -> WAArmadilloTransportEvent.TransportEvent.Placeholder.Type
	0, // 5: WAArmadilloTransportEvent.TransportEvent.Event.IcdcAlert.type:type_name -> WAArmadilloTransportEvent.TransportEvent.Event.IcdcAlert.Type
	1, // 6: WAArmadilloTransportEvent.TransportEvent.Event.DeviceChange.type:type_name -> WAArmadilloTransportEvent.TransportEvent.Event.DeviceChange.Type
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_init() }
func file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_init() {
	if File_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto != nil {
		return
	}
	file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[0].OneofWrappers = []any{
		(*TransportEvent_Placeholder_)(nil),
		(*TransportEvent_Event_)(nil),
	}
	file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes[1].OneofWrappers = []any{
		(*TransportEvent_Event_DeviceChange_)(nil),
		(*TransportEvent_Event_IcdcAlert_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDesc), len(file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_goTypes,
		DependencyIndexes: file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_depIdxs,
		EnumInfos:         file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_enumTypes,
		MessageInfos:      file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_msgTypes,
	}.Build()
	File_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto = out.File
	file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_goTypes = nil
	file_waArmadilloTransportEvent_WAArmadilloTransportEvent_proto_depIdxs = nil
}
