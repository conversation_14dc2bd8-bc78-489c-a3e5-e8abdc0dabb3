//! Conversion traits for protocol buffer types

/// Trait for converting from protocol buffer types
pub trait FromProto<T> {
    type Error;

    /// Convert from a protocol buffer type
    fn from_proto(proto: T) -> Result<Self, Self::Error>
    where
        Self: Sized;
}

/// Trait for converting to protocol buffer types
pub trait ToProto<T> {
    /// Convert to a protocol buffer type
    fn to_proto(&self) -> T;
}

/// Trait for bidirectional conversion
pub trait ProtoConvert<T>: FromProto<T> + ToProto<T> {}
