syntax = "proto2";
package WAProtobufsDeviceCapabilities;
option go_package = "go.mau.fi/whatsmeow/proto/waDeviceCapabilities";

message DeviceCapabilities {
	enum ChatLockSupportLevel {
		NONE = 0;
		MINIMAL = 1;
		FULL = 2;
	}

	message LIDMigration {
		optional uint64 chatDbMigrationTimestamp = 1;
	}

	optional ChatLockSupportLevel chatLockSupportLevel = 1;
	optional LIDMigration lidMigration = 2;
}
