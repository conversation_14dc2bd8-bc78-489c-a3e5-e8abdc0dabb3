//! Event system for WhatsApp client

use std::time::SystemTime;
use whatsmeow_types::{Jid, MessageId};

/// Events emitted by the WhatsApp client
#[derive(Debug, Clone)]
pub enum Event {
    /// Client connected successfully
    Connected,
    /// Client disconnected
    Disconnected { reason: DisconnectReason },
    /// QR code for authentication
    QrCode { codes: Vec<String> },
    /// Pairing successful
    PairSuccess {
        id: Jid,
        business_name: Option<String>,
    },
    /// Message received
    Message(MessageEvent),
    /// Receipt received
    Receipt(ReceiptEvent),
    /// Presence update
    Presence(PresenceEvent),
    /// Group information update
    GroupInfo(GroupEvent),
}

/// Reasons for disconnection
#[derive(Debug, Clone)]
pub enum DisconnectReason {
    /// User requested disconnection
    UserRequested,
    /// Network error
    NetworkError,
    /// Authentication failed
    AuthenticationFailed,
    /// Server closed connection
    ServerClosed,
    /// Unknown reason
    Unknown,
}

/// Message event data
#[derive(Debug, <PERSON>lone)]
pub struct MessageEvent {
    pub info: MessageInfo,
    pub content: MessageContent,
}

/// Message information
#[derive(Debug, <PERSON>lone)]
pub struct MessageInfo {
    pub id: MessageId,
    pub timestamp: SystemTime,
    pub chat: Jid,
    pub sender: Option<Jid>,
    pub from_me: bool,
    pub broadcast_list_owner: Option<Jid>,
}

/// Message content types
#[derive(Debug, Clone)]
pub enum MessageContent {
    Text {
        text: String,
        mentions: Vec<Mention>,
    },
    Image {
        caption: Option<String>,
        // TODO: Add media info
    },
    Document {
        filename: String,
        // TODO: Add media info
    },
    // TODO: Add other message types
}

/// Mention in a message
#[derive(Debug, Clone)]
pub struct Mention {
    pub jid: Jid,
    pub start: usize,
    pub length: usize,
}

/// Receipt event data
#[derive(Debug, Clone)]
pub struct ReceiptEvent {
    pub message_ids: Vec<MessageId>,
    pub timestamp: SystemTime,
    pub chat: Jid,
    pub sender: Jid,
    pub receipt_type: ReceiptType,
}

/// Types of receipts
#[derive(Debug, Clone)]
pub enum ReceiptType {
    Delivery,
    Read,
    Played,
}

/// Presence event data
#[derive(Debug, Clone)]
pub struct PresenceEvent {
    pub from: Jid,
    pub presence: PresenceType,
    pub last_seen: Option<SystemTime>,
}

/// Types of presence
#[derive(Debug, Clone)]
pub enum PresenceType {
    Available,
    Unavailable,
    Composing,
    Paused,
}

/// Group event data
#[derive(Debug, Clone)]
pub struct GroupEvent {
    pub jid: Jid,
    pub event_type: GroupEventType,
}

/// Types of group events
#[derive(Debug, Clone)]
pub enum GroupEventType {
    Create,
    ParticipantAdd { participants: Vec<Jid> },
    ParticipantRemove { participants: Vec<Jid> },
    SubjectChange { subject: String },
    DescriptionChange { description: String },
    // TODO: Add other group event types
}

/// Trait for handling events
pub trait EventHandler: Send + Sync {
    /// Handle an event, return true to continue processing, false to stop
    fn handle(&self, event: Event) -> bool;
}

/// Implementation of EventHandler for closures
impl<F> EventHandler for F
where
    F: Fn(Event) -> bool + Send + Sync,
{
    fn handle(&self, event: Event) -> bool {
        self(event)
    }
}
