# Design Document

## Overview

The whatsmeow-rs library will be a faithful Rust translation of the Go whatsmeow library, providing a comprehensive WhatsApp Web API client. The design leverages Rust's ownership system, type safety, and async/await model to create a memory-safe, performant, and ergonomic API while maintaining functional compatibility with the original Go implementation.

The library will be structured as a multi-crate workspace to mirror the Go package organization, with clear separation of concerns between protocol handling, cryptography, storage, and client logic.

## Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    whatsmeow-rs                             │
├─────────────────────────────────────────────────────────────┤
│  Client API Layer                                           │
│  ├─ Client struct (main interface)                          │
│  ├─ Event system (typed events)                             │
│  └─ Configuration and builders                              │
├─────────────────────────────────────────────────────────────┤
│  Protocol Layer                                             │
│  ├─ Message handling (send/receive)                         │
│  ├─ Group management                                        │
│  ├─ Media upload/download                                   │
│  └─ App state synchronization                               │
├─────────────────────────────────────────────────────────────┤
│  Transport Layer                                            │
│  ├─ WebSocket connection management                         │
│  ├─ Noise protocol implementation                           │
│  ├─ Binary node parsing                                     │
│  └─ Protocol buffer handling                                │
├─────────────────────────────────────────────────────────────┤
│  Cryptography Layer                                         │
│  ├─ Signal protocol (E2E encryption)                        │
│  ├─ Key management                                          │
│  └─ Noise handshake                                         │
├─────────────────────────────────────────────────────────────┤
│  Storage Layer                                              │
│  ├─ Device store interface                                  │
│  ├─ Session management                                      │
│  └─ Key storage                                             │
└─────────────────────────────────────────────────────────────┘
```

### Crate Structure

The project will be organized as a Cargo workspace with the following crates:

- `whatsmeow-rs` - Main client library and public API
- `whatsmeow-proto` - Protocol buffer definitions and generated code
- `whatsmeow-crypto` - Cryptographic operations (Signal, Noise)
- `whatsmeow-binary` - Binary node parsing and serialization
- `whatsmeow-store` - Storage interfaces and implementations
- `whatsmeow-types` - Common types and utilities

## Components and Interfaces

### Core Client Component

```rust
pub struct Client {
    store: Arc<dyn DeviceStore>,
    logger: Arc<dyn Logger>,
    socket: Arc<RwLock<Option<NoiseSocket>>>,
    event_handlers: Arc<RwLock<Vec<EventHandler>>>,
    config: ClientConfig,
    // ... other fields
}

pub struct ClientConfig {
    pub enable_auto_reconnect: bool,
    pub auto_reconnect_errors: u32,
    pub synchronous_ack: bool,
    pub enable_decrypted_event_buffer: bool,
    // ... other configuration options
}

impl Client {
    pub fn new(store: Arc<dyn DeviceStore>, logger: Arc<dyn Logger>) -> Self;
    pub async fn connect(&self) -> Result<(), WhatsAppError>;
    pub async fn disconnect(&self) -> Result<(), WhatsAppError>;
    pub fn add_event_handler<F>(&self, handler: F) -> HandlerId
    where F: Fn(Event) -> bool + Send + Sync + 'static;
    pub fn remove_event_handler(&self, id: HandlerId);
    // ... other methods
}
```

### Event System

```rust
#[derive(Debug, Clone)]
pub enum Event {
    Connected,
    Disconnected { reason: DisconnectReason },
    QrCode { codes: Vec<String> },
    PairSuccess { id: Jid, business_name: Option<String> },
    Message(MessageEvent),
    Receipt(ReceiptEvent),
    Presence(PresenceEvent),
    GroupInfo(GroupEvent),
    // ... other event types
}

pub trait EventHandler: Send + Sync {
    fn handle(&self, event: Event) -> bool;
}

pub type EventHandlerFn = Box<dyn Fn(Event) -> bool + Send + Sync>;
```

### Message Handling

```rust
pub struct MessageSender {
    client: Weak<Client>,
}

impl MessageSender {
    pub async fn send_text(&self, to: Jid, text: &str) -> Result<MessageId, WhatsAppError>;
    pub async fn send_image(&self, to: Jid, image: ImageMessage) -> Result<MessageId, WhatsAppError>;
    pub async fn send_document(&self, to: Jid, doc: DocumentMessage) -> Result<MessageId, WhatsAppError>;
    // ... other send methods
}

#[derive(Debug, Clone)]
pub struct MessageEvent {
    pub info: MessageInfo,
    pub message: MessageContent,
}

#[derive(Debug, Clone)]
pub enum MessageContent {
    Text { text: String, mentions: Vec<Mention> },
    Image { caption: Option<String>, image: MediaInfo },
    Document { filename: String, document: MediaInfo },
    // ... other message types
}
```

### Transport Layer

```rust
pub struct NoiseSocket {
    frame_socket: FrameSocket,
    write_key: Box<dyn Aead>,
    read_key: Box<dyn Aead>,
    write_counter: AtomicU32,
    read_counter: AtomicU32,
    frame_handler: Arc<dyn FrameHandler>,
}

impl NoiseSocket {
    pub async fn new(
        websocket: WebSocketStream<MaybeTlsStream<TcpStream>>,
        write_key: Box<dyn Aead>,
        read_key: Box<dyn Aead>,
        frame_handler: Arc<dyn FrameHandler>,
    ) -> Result<Self, NoiseError>;

    pub async fn send_frame(&self, data: &[u8]) -> Result<(), NoiseError>;
    pub async fn close(&self) -> Result<(), NoiseError>;
}

pub trait FrameHandler: Send + Sync {
    fn handle_frame(&self, frame: &[u8]);
}
```

### Storage Interface

```rust
#[async_trait]
pub trait DeviceStore: Send + Sync {
    async fn get_device(&self) -> Result<Option<Device>, StoreError>;
    async fn put_device(&self, device: &Device) -> Result<(), StoreError>;
    async fn delete_device(&self) -> Result<(), StoreError>;
}

#[async_trait]
pub trait SessionStore: Send + Sync {
    async fn get_session(&self, address: &str) -> Result<Option<Vec<u8>>, StoreError>;
    async fn put_session(&self, address: &str, session: &[u8]) -> Result<(), StoreError>;
    async fn delete_session(&self, address: &str) -> Result<(), StoreError>;
    async fn delete_all_sessions(&self, phone: &str) -> Result<(), StoreError>;
}

#[async_trait]
pub trait PreKeyStore: Send + Sync {
    async fn get_or_gen_prekeys(&self, count: u32) -> Result<Vec<PreKey>, StoreError>;
    async fn get_prekey(&self, id: u32) -> Result<Option<PreKey>, StoreError>;
    async fn remove_prekey(&self, id: u32) -> Result<(), StoreError>;
    async fn mark_prekeys_as_uploaded(&self, up_to_id: u32) -> Result<(), StoreError>;
}
```

### Cryptography Components

```rust
pub struct SignalProtocol {
    identity_store: Arc<dyn IdentityStore>,
    session_store: Arc<dyn SessionStore>,
    prekey_store: Arc<dyn PreKeyStore>,
    sender_key_store: Arc<dyn SenderKeyStore>,
}

impl SignalProtocol {
    pub fn new(
        identity_store: Arc<dyn IdentityStore>,
        session_store: Arc<dyn SessionStore>,
        prekey_store: Arc<dyn PreKeyStore>,
        sender_key_store: Arc<dyn SenderKeyStore>,
    ) -> Self;

    pub async fn encrypt_message(&self, recipient: &Jid, plaintext: &[u8]) -> Result<Vec<u8>, CryptoError>;
    pub async fn decrypt_message(&self, sender: &Jid, ciphertext: &[u8]) -> Result<Vec<u8>, CryptoError>;
}

pub struct NoiseHandshake {
    ephemeral_key: EphemeralKey,
    static_key: StaticKey,
}

impl NoiseHandshake {
    pub fn new(static_key: StaticKey) -> Self;
    pub fn create_client_hello(&mut self) -> Result<Vec<u8>, NoiseError>;
    pub fn process_server_hello(&mut self, server_hello: &[u8]) -> Result<NoiseKeys, NoiseError>;
}
```

## Data Models

### Core Types

```rust
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct Jid {
    pub user: String,
    pub server: String,
    pub device: u8,
}

impl Jid {
    pub fn new(user: String, server: String, device: u8) -> Self;
    pub fn from_string(s: &str) -> Result<Self, JidError>;
    pub fn to_string(&self) -> String;
    pub fn is_group(&self) -> bool;
    pub fn is_broadcast(&self) -> bool;
}

#[derive(Debug, Clone)]
pub struct MessageInfo {
    pub id: MessageId,
    pub timestamp: SystemTime,
    pub chat: Jid,
    pub sender: Option<Jid>,
    pub from_me: bool,
    pub broadcast_list_owner: Option<Jid>,
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct MessageId(pub String);

#[derive(Debug, Clone)]
pub struct Device {
    pub jid: Jid,
    pub registration_id: u32,
    pub noise_key: NoiseKey,
    pub identity_key: IdentityKey,
    pub signed_prekey: SignedPreKey,
    pub platform: String,
    pub business_name: Option<String>,
}
```

### Protocol Buffer Integration

```rust
// Generated from .proto files
pub mod proto {
    pub mod wa_e2e {
        include!(concat!(env!("OUT_DIR"), "/wa_e2e.rs"));
    }
    pub mod wa_web {
        include!(concat!(env!("OUT_DIR"), "/wa_web.rs"));
    }
    // ... other proto modules
}

// Conversion traits for proto types
pub trait FromProto<T> {
    type Error;
    fn from_proto(proto: T) -> Result<Self, Self::Error>
    where
        Self: Sized;
}

pub trait ToProto<T> {
    fn to_proto(&self) -> T;
}
```

## Error Handling

### Error Types

```rust
#[derive(Debug, thiserror::Error)]
pub enum WhatsAppError {
    #[error("Connection error: {0}")]
    Connection(#[from] ConnectionError),

    #[error("Protocol error: {0}")]
    Protocol(#[from] ProtocolError),

    #[error("Cryptography error: {0}")]
    Crypto(#[from] CryptoError),

    #[error("Storage error: {0}")]
    Store(#[from] StoreError),

    #[error("Serialization error: {0}")]
    Serialization(#[from] SerializationError),

    #[error("Authentication failed: {reason}")]
    AuthenticationFailed { reason: String },

    #[error("Not connected")]
    NotConnected,

    #[error("Invalid JID: {0}")]
    InvalidJid(String),
}

#[derive(Debug, thiserror::Error)]
pub enum ConnectionError {
    #[error("WebSocket error: {0}")]
    WebSocket(#[from] tokio_tungstenite::tungstenite::Error),

    #[error("Noise handshake failed: {0}")]
    NoiseHandshake(#[from] NoiseError),

    #[error("Connection timeout")]
    Timeout,

    #[error("Unexpected disconnection")]
    UnexpectedDisconnection,
}

// Result type alias for convenience
pub type Result<T> = std::result::Result<T, WhatsAppError>;
```

## Testing Strategy

### Unit Testing Approach

1. **Isolated Component Testing**: Each module will have comprehensive unit tests that mock dependencies
2. **Property-Based Testing**: Use `proptest` for testing protocol parsing and cryptographic operations
3. **Mock Implementations**: Create mock implementations of all traits for testing
4. **Async Testing**: Use `tokio-test` for testing async components

### Test Structure

```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;
    use mockall::predicate::*;

    #[tokio::test]
    async fn test_client_connection() {
        let mock_store = MockDeviceStore::new();
        let mock_logger = MockLogger::new();

        // Setup expectations
        mock_store.expect_get_device()
            .returning(|| Ok(Some(test_device())));

        let client = Client::new(Arc::new(mock_store), Arc::new(mock_logger));

        // Test connection logic
        let result = client.connect().await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_jid_parsing() {
        let jid = Jid::from_string("<EMAIL>").unwrap();
        assert_eq!(jid.user, "1234567890");
        assert_eq!(jid.server, "s.whatsapp.net");
        assert_eq!(jid.device, 0);
    }
}
```

### Integration Testing

```rust
// tests/integration_test.rs
use whatsmeow_rs::*;
use std::sync::Arc;

#[tokio::test]
async fn test_full_message_flow() {
    let store = Arc::new(MemoryStore::new());
    let logger = Arc::new(TestLogger::new());
    let client = Client::new(store, logger);

    // Test complete message sending and receiving flow
    // This would use a test WhatsApp server or mock server
}
```

### Mock Server for Testing

```rust
// A mock WhatsApp server for integration testing
pub struct MockWhatsAppServer {
    port: u16,
    server: Option<tokio::task::JoinHandle<()>>,
}

impl MockWhatsAppServer {
    pub async fn start() -> Self {
        // Start a mock WebSocket server that simulates WhatsApp protocol
    }

    pub async fn stop(&mut self) {
        // Stop the mock server
    }

    pub fn expect_message(&self, message: &str) {
        // Set up expectations for testing
    }
}
```

## Dependencies and External Libraries

### Core Dependencies

```toml
[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = "0.20"

# Serialization
prost = "0.12"
prost-types = "0.12"

# Cryptography
ring = "0.17"
ed25519-dalek = "2.0"
x25519-dalek = "2.0"
aes-gcm = "0.10"
hkdf = "0.12"

# Error handling
thiserror = "1.0"
anyhow = "1.0"

# Utilities
uuid = { version = "1.0", features = ["v4"] }
base64 = "0.21"
hex = "0.4"
bytes = "1.0"
tracing = "0.1"

# Async traits
async-trait = "0.1"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

[dev-dependencies]
# Testing
tokio-test = "0.4"
mockall = "0.11"
proptest = "1.0"
criterion = "0.5"

[build-dependencies]
# Protocol buffer compilation
prost-build = "0.12"
```

### Build Script for Protocol Buffers

```rust
// build.rs
use std::io::Result;

fn main() -> Result<()> {
    let proto_files = [
        "proto/wa_e2e.proto",
        "proto/wa_web.proto",
        "proto/wa_wa6.proto",
        // ... other proto files
    ];

    prost_build::Config::new()
        .bytes(["."])
        .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
        .compile_protos(&proto_files, &["proto/"])?;

    Ok(())
}
```

## Performance Considerations

### Memory Management

1. **Zero-Copy Parsing**: Use `bytes::Bytes` for protocol buffer parsing to avoid unnecessary copying
2. **Connection Pooling**: Reuse WebSocket connections where possible
3. **Streaming**: Implement streaming for large media uploads/downloads
4. **Buffer Management**: Use ring buffers for frame processing

### Concurrency Model

1. **Actor Pattern**: Use message passing between components to avoid shared mutable state
2. **Async/Await**: Leverage Rust's async ecosystem for non-blocking I/O
3. **Channel-Based Communication**: Use `tokio::sync::mpsc` for inter-component communication
4. **Lock-Free Data Structures**: Use atomic operations where possible

### Benchmarking

```rust
// benches/message_parsing.rs
use criterion::{black_box, criterion_group, criterion_main, Criterion};
use whatsmeow_rs::binary::Node;

fn benchmark_node_parsing(c: &mut Criterion) {
    let test_data = include_bytes!("../test_data/sample_node.bin");

    c.bench_function("parse_binary_node", |b| {
        b.iter(|| {
            let node = Node::parse(black_box(test_data)).unwrap();
            black_box(node);
        })
    });
}

criterion_group!(benches, benchmark_node_parsing);
criterion_main!(benches);
```

This design provides a comprehensive foundation for translating the whatsmeow Go library to Rust while maintaining API compatibility and leveraging Rust's strengths in safety and performance.
