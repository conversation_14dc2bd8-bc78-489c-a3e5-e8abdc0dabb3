syntax = "proto2";
package WAProtobufsUserPassword;
option go_package = "go.mau.fi/whatsmeow/proto/waUserPassword";

message UserPassword {
	enum Transformer {
		NONE = 0;
		PBKDF2_HMAC_SHA512 = 1;
		PBKDF2_HMAC_SHA384 = 2;
	}

	enum Encoding {
		UTF8 = 0;
		UTF8_BROKEN = 1;
	}

	message TransformerArg {
		message Value {
			oneof value {
				bytes asBlob = 1;
				uint32 asUnsignedInteger = 2;
			}
		}

		optional string key = 1;
		optional Value value = 2;
	}

	optional Encoding encoding = 1;
	optional Transformer transformer = 2;
	repeated TransformerArg transformerArg = 3;
	optional bytes transformedData = 4;
}
