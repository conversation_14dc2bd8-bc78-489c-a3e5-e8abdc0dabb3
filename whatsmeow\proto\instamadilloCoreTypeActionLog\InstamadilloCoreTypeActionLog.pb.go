// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: instamadilloCoreTypeActionLog/InstamadilloCoreTypeActionLog.proto

package instamadilloCoreTypeActionLog

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActionLog struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to ActionLogSubtype:
	//
	//	*ActionLog_ActionLogReaction
	ActionLogSubtype isActionLog_ActionLogSubtype `protobuf_oneof:"actionLogSubtype"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ActionLog) Reset() {
	*x = ActionLog{}
	mi := &file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActionLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionLog) ProtoMessage() {}

func (x *ActionLog) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionLog.ProtoReflect.Descriptor instead.
func (*ActionLog) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDescGZIP(), []int{0}
}

func (x *ActionLog) GetActionLogSubtype() isActionLog_ActionLogSubtype {
	if x != nil {
		return x.ActionLogSubtype
	}
	return nil
}

func (x *ActionLog) GetActionLogReaction() *ActionLogReaction {
	if x != nil {
		if x, ok := x.ActionLogSubtype.(*ActionLog_ActionLogReaction); ok {
			return x.ActionLogReaction
		}
	}
	return nil
}

type isActionLog_ActionLogSubtype interface {
	isActionLog_ActionLogSubtype()
}

type ActionLog_ActionLogReaction struct {
	ActionLogReaction *ActionLogReaction `protobuf:"bytes,1,opt,name=actionLogReaction,oneof"`
}

func (*ActionLog_ActionLogReaction) isActionLog_ActionLogSubtype() {}

type ActionLogReaction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EmojiUnicode  *string                `protobuf:"bytes,1,opt,name=emojiUnicode" json:"emojiUnicode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActionLogReaction) Reset() {
	*x = ActionLogReaction{}
	mi := &file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActionLogReaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionLogReaction) ProtoMessage() {}

func (x *ActionLogReaction) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionLogReaction.ProtoReflect.Descriptor instead.
func (*ActionLogReaction) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDescGZIP(), []int{1}
}

func (x *ActionLogReaction) GetEmojiUnicode() string {
	if x != nil && x.EmojiUnicode != nil {
		return *x.EmojiUnicode
	}
	return ""
}

var File_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto protoreflect.FileDescriptor

const file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDesc = "" +
	"\n" +
	"AinstamadilloCoreTypeActionLog/InstamadilloCoreTypeActionLog.proto\x12\x1dInstamadilloCoreTypeActionLog\"\x81\x01\n" +
	"\tActionLog\x12`\n" +
	"\x11actionLogReaction\x18\x01 \x01(\v20.InstamadilloCoreTypeActionLog.ActionLogReactionH\x00R\x11actionLogReactionB\x12\n" +
	"\x10actionLogSubtype\"7\n" +
	"\x11ActionLogReaction\x12\"\n" +
	"\femojiUnicode\x18\x01 \x01(\tR\femojiUnicodeB9Z7go.mau.fi/whatsmeow/proto/instamadilloCoreTypeActionLog"

var (
	file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDescOnce sync.Once
	file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDescData []byte
)

func file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDescGZIP() []byte {
	file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDescOnce.Do(func() {
		file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDesc), len(file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDesc)))
	})
	return file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDescData
}

var file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_goTypes = []any{
	(*ActionLog)(nil),         // 0: InstamadilloCoreTypeActionLog.ActionLog
	(*ActionLogReaction)(nil), // 1: InstamadilloCoreTypeActionLog.ActionLogReaction
}
var file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_depIdxs = []int32{
	1, // 0: InstamadilloCoreTypeActionLog.ActionLog.actionLogReaction:type_name -> InstamadilloCoreTypeActionLog.ActionLogReaction
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_init() }
func file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_init() {
	if File_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto != nil {
		return
	}
	file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_msgTypes[0].OneofWrappers = []any{
		(*ActionLog_ActionLogReaction)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDesc), len(file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_goTypes,
		DependencyIndexes: file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_depIdxs,
		MessageInfos:      file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_msgTypes,
	}.Build()
	File_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto = out.File
	file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_goTypes = nil
	file_instamadilloCoreTypeActionLog_InstamadilloCoreTypeActionLog_proto_depIdxs = nil
}
