//! Protocol buffer error types

use thiserror::Error;

/// Errors that can occur during protocol buffer operations
#[derive(<PERSON>bu<PERSON>, Error)]
pub enum ProtoError {
    #[error("Serialization failed: {0}")]
    SerializationFailed(String),

    #[error("Deserialization failed: {0}")]
    DeserializationFailed(String),

    #[error("Invalid field: {0}")]
    InvalidField(String),

    #[error("Missing required field: {0}")]
    MissingRequiredField(String),

    #[error("Type conversion failed: {0}")]
    TypeConversionFailed(String),
}
