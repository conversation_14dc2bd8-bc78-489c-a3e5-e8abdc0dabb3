//! Message handling functionality

use crate::error::Result;
use whatsmeow_types::{Jid, MessageId};

/// Message sender for various message types
pub struct MessageSender {
    // TODO: Add client reference
}

impl MessageSender {
    /// Send a text message
    pub async fn send_text(&self, to: Jid, text: &str) -> Result<MessageId> {
        // TODO: Implement text message sending
        todo!("send_text not implemented")
    }

    /// Send an image message
    pub async fn send_image(&self, to: Jid, image: ImageMessage) -> Result<MessageId> {
        // TODO: Implement image message sending
        todo!("send_image not implemented")
    }
}

/// Image message data
pub struct ImageMessage {
    pub data: Vec<u8>,
    pub caption: Option<String>,
    pub mime_type: String,
}
