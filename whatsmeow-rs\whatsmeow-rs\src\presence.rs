//! Presence handling functionality

use crate::error::Result;
use whatsmeow_types::Jid;

/// Presence manager for handling online/offline status
pub struct PresenceManager {
    // TODO: Add client reference
}

impl PresenceManager {
    /// Send typing notification
    pub async fn send_typing(&self, to: Jid) -> Result<()> {
        // TODO: Implement typing notification
        todo!("send_typing not implemented")
    }

    /// Send presence update
    pub async fn send_presence(&self, presence: PresenceType) -> Result<()> {
        // TODO: Implement presence update
        todo!("send_presence not implemented")
    }
}

/// Types of presence
#[derive(Debug, Clone)]
pub enum PresenceType {
    Available,
    Unavailable,
}
