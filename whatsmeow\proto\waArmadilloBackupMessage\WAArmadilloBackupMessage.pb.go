// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waArmadilloBackupMessage/WAArmadilloBackupMessage.proto

package waArmadilloBackupMessage

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	waArmadilloBackupCommon "go.mau.fi/whatsmeow/proto/waArmadilloBackupCommon"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackupMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Payload:
	//
	//	*BackupMessage_EncryptedTransportMessage
	//	*BackupMessage_EncryptedTransportEvent
	//	*BackupMessage_EncryptedTransportLocallyTransformedMessage
	//	*BackupMessage_MiTransportAdminMessage
	Payload       isBackupMessage_Payload `protobuf_oneof:"payload"`
	Metadata      *BackupMessage_Metadata `protobuf:"bytes,1,opt,name=metadata" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackupMessage) Reset() {
	*x = BackupMessage{}
	mi := &file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackupMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackupMessage) ProtoMessage() {}

func (x *BackupMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackupMessage.ProtoReflect.Descriptor instead.
func (*BackupMessage) Descriptor() ([]byte, []int) {
	return file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDescGZIP(), []int{0}
}

func (x *BackupMessage) GetPayload() isBackupMessage_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *BackupMessage) GetEncryptedTransportMessage() []byte {
	if x != nil {
		if x, ok := x.Payload.(*BackupMessage_EncryptedTransportMessage); ok {
			return x.EncryptedTransportMessage
		}
	}
	return nil
}

func (x *BackupMessage) GetEncryptedTransportEvent() *waArmadilloBackupCommon.Subprotocol {
	if x != nil {
		if x, ok := x.Payload.(*BackupMessage_EncryptedTransportEvent); ok {
			return x.EncryptedTransportEvent
		}
	}
	return nil
}

func (x *BackupMessage) GetEncryptedTransportLocallyTransformedMessage() *waArmadilloBackupCommon.Subprotocol {
	if x != nil {
		if x, ok := x.Payload.(*BackupMessage_EncryptedTransportLocallyTransformedMessage); ok {
			return x.EncryptedTransportLocallyTransformedMessage
		}
	}
	return nil
}

func (x *BackupMessage) GetMiTransportAdminMessage() *waArmadilloBackupCommon.Subprotocol {
	if x != nil {
		if x, ok := x.Payload.(*BackupMessage_MiTransportAdminMessage); ok {
			return x.MiTransportAdminMessage
		}
	}
	return nil
}

func (x *BackupMessage) GetMetadata() *BackupMessage_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type isBackupMessage_Payload interface {
	isBackupMessage_Payload()
}

type BackupMessage_EncryptedTransportMessage struct {
	EncryptedTransportMessage []byte `protobuf:"bytes,2,opt,name=encryptedTransportMessage,oneof"`
}

type BackupMessage_EncryptedTransportEvent struct {
	EncryptedTransportEvent *waArmadilloBackupCommon.Subprotocol `protobuf:"bytes,5,opt,name=encryptedTransportEvent,oneof"`
}

type BackupMessage_EncryptedTransportLocallyTransformedMessage struct {
	EncryptedTransportLocallyTransformedMessage *waArmadilloBackupCommon.Subprotocol `protobuf:"bytes,6,opt,name=encryptedTransportLocallyTransformedMessage,oneof"`
}

type BackupMessage_MiTransportAdminMessage struct {
	MiTransportAdminMessage *waArmadilloBackupCommon.Subprotocol `protobuf:"bytes,7,opt,name=miTransportAdminMessage,oneof"`
}

func (*BackupMessage_EncryptedTransportMessage) isBackupMessage_Payload() {}

func (*BackupMessage_EncryptedTransportEvent) isBackupMessage_Payload() {}

func (*BackupMessage_EncryptedTransportLocallyTransformedMessage) isBackupMessage_Payload() {}

func (*BackupMessage_MiTransportAdminMessage) isBackupMessage_Payload() {}

type BackupMessage_Metadata struct {
	state               protoimpl.MessageState                   `protogen:"open.v1"`
	SenderID            *string                                  `protobuf:"bytes,1,opt,name=senderID" json:"senderID,omitempty"`
	MessageID           *string                                  `protobuf:"bytes,2,opt,name=messageID" json:"messageID,omitempty"`
	TimestampMS         *int64                                   `protobuf:"varint,3,opt,name=timestampMS" json:"timestampMS,omitempty"`
	FrankingMetadata    *BackupMessage_Metadata_FrankingMetadata `protobuf:"bytes,4,opt,name=frankingMetadata" json:"frankingMetadata,omitempty"`
	PayloadVersion      *int32                                   `protobuf:"varint,5,opt,name=payloadVersion" json:"payloadVersion,omitempty"`
	FutureProofBehavior *int32                                   `protobuf:"varint,6,opt,name=futureProofBehavior" json:"futureProofBehavior,omitempty"`
	ThreadTypeTag       *int32                                   `protobuf:"varint,7,opt,name=threadTypeTag" json:"threadTypeTag,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *BackupMessage_Metadata) Reset() {
	*x = BackupMessage_Metadata{}
	mi := &file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackupMessage_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackupMessage_Metadata) ProtoMessage() {}

func (x *BackupMessage_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackupMessage_Metadata.ProtoReflect.Descriptor instead.
func (*BackupMessage_Metadata) Descriptor() ([]byte, []int) {
	return file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDescGZIP(), []int{0, 0}
}

func (x *BackupMessage_Metadata) GetSenderID() string {
	if x != nil && x.SenderID != nil {
		return *x.SenderID
	}
	return ""
}

func (x *BackupMessage_Metadata) GetMessageID() string {
	if x != nil && x.MessageID != nil {
		return *x.MessageID
	}
	return ""
}

func (x *BackupMessage_Metadata) GetTimestampMS() int64 {
	if x != nil && x.TimestampMS != nil {
		return *x.TimestampMS
	}
	return 0
}

func (x *BackupMessage_Metadata) GetFrankingMetadata() *BackupMessage_Metadata_FrankingMetadata {
	if x != nil {
		return x.FrankingMetadata
	}
	return nil
}

func (x *BackupMessage_Metadata) GetPayloadVersion() int32 {
	if x != nil && x.PayloadVersion != nil {
		return *x.PayloadVersion
	}
	return 0
}

func (x *BackupMessage_Metadata) GetFutureProofBehavior() int32 {
	if x != nil && x.FutureProofBehavior != nil {
		return *x.FutureProofBehavior
	}
	return 0
}

func (x *BackupMessage_Metadata) GetThreadTypeTag() int32 {
	if x != nil && x.ThreadTypeTag != nil {
		return *x.ThreadTypeTag
	}
	return 0
}

type BackupMessage_Metadata_FrankingMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FrankingTag   []byte                 `protobuf:"bytes,3,opt,name=frankingTag" json:"frankingTag,omitempty"`
	ReportingTag  []byte                 `protobuf:"bytes,4,opt,name=reportingTag" json:"reportingTag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackupMessage_Metadata_FrankingMetadata) Reset() {
	*x = BackupMessage_Metadata_FrankingMetadata{}
	mi := &file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackupMessage_Metadata_FrankingMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackupMessage_Metadata_FrankingMetadata) ProtoMessage() {}

func (x *BackupMessage_Metadata_FrankingMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackupMessage_Metadata_FrankingMetadata.ProtoReflect.Descriptor instead.
func (*BackupMessage_Metadata_FrankingMetadata) Descriptor() ([]byte, []int) {
	return file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *BackupMessage_Metadata_FrankingMetadata) GetFrankingTag() []byte {
	if x != nil {
		return x.FrankingTag
	}
	return nil
}

func (x *BackupMessage_Metadata_FrankingMetadata) GetReportingTag() []byte {
	if x != nil {
		return x.ReportingTag
	}
	return nil
}

var File_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto protoreflect.FileDescriptor

const file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDesc = "" +
	"\n" +
	"7waArmadilloBackupMessage/WAArmadilloBackupMessage.proto\x12\x18WAArmadilloBackupMessage\x1a5waArmadilloBackupCommon/WAArmadilloBackupCommon.proto\"\xa9\a\n" +
	"\rBackupMessage\x12>\n" +
	"\x19encryptedTransportMessage\x18\x02 \x01(\fH\x00R\x19encryptedTransportMessage\x12`\n" +
	"\x17encryptedTransportEvent\x18\x05 \x01(\v2$.WAArmadilloBackupCommon.SubprotocolH\x00R\x17encryptedTransportEvent\x12\x88\x01\n" +
	"+encryptedTransportLocallyTransformedMessage\x18\x06 \x01(\v2$.WAArmadilloBackupCommon.SubprotocolH\x00R+encryptedTransportLocallyTransformedMessage\x12`\n" +
	"\x17miTransportAdminMessage\x18\a \x01(\v2$.WAArmadilloBackupCommon.SubprotocolH\x00R\x17miTransportAdminMessage\x12L\n" +
	"\bmetadata\x18\x01 \x01(\v20.WAArmadilloBackupMessage.BackupMessage.MetadataR\bmetadata\x1a\xaf\x03\n" +
	"\bMetadata\x12\x1a\n" +
	"\bsenderID\x18\x01 \x01(\tR\bsenderID\x12\x1c\n" +
	"\tmessageID\x18\x02 \x01(\tR\tmessageID\x12 \n" +
	"\vtimestampMS\x18\x03 \x01(\x03R\vtimestampMS\x12m\n" +
	"\x10frankingMetadata\x18\x04 \x01(\v2A.WAArmadilloBackupMessage.BackupMessage.Metadata.FrankingMetadataR\x10frankingMetadata\x12&\n" +
	"\x0epayloadVersion\x18\x05 \x01(\x05R\x0epayloadVersion\x120\n" +
	"\x13futureProofBehavior\x18\x06 \x01(\x05R\x13futureProofBehavior\x12$\n" +
	"\rthreadTypeTag\x18\a \x01(\x05R\rthreadTypeTag\x1aX\n" +
	"\x10FrankingMetadata\x12 \n" +
	"\vfrankingTag\x18\x03 \x01(\fR\vfrankingTag\x12\"\n" +
	"\freportingTag\x18\x04 \x01(\fR\freportingTagB\t\n" +
	"\apayloadB4Z2go.mau.fi/whatsmeow/proto/waArmadilloBackupMessage"

var (
	file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDescOnce sync.Once
	file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDescData []byte
)

func file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDescGZIP() []byte {
	file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDescOnce.Do(func() {
		file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDesc), len(file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDesc)))
	})
	return file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDescData
}

var file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_goTypes = []any{
	(*BackupMessage)(nil),                           // 0: WAArmadilloBackupMessage.BackupMessage
	(*BackupMessage_Metadata)(nil),                  // 1: WAArmadilloBackupMessage.BackupMessage.Metadata
	(*BackupMessage_Metadata_FrankingMetadata)(nil), // 2: WAArmadilloBackupMessage.BackupMessage.Metadata.FrankingMetadata
	(*waArmadilloBackupCommon.Subprotocol)(nil),     // 3: WAArmadilloBackupCommon.Subprotocol
}
var file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_depIdxs = []int32{
	3, // 0: WAArmadilloBackupMessage.BackupMessage.encryptedTransportEvent:type_name -> WAArmadilloBackupCommon.Subprotocol
	3, // 1: WAArmadilloBackupMessage.BackupMessage.encryptedTransportLocallyTransformedMessage:type_name -> WAArmadilloBackupCommon.Subprotocol
	3, // 2: WAArmadilloBackupMessage.BackupMessage.miTransportAdminMessage:type_name -> WAArmadilloBackupCommon.Subprotocol
	1, // 3: WAArmadilloBackupMessage.BackupMessage.metadata:type_name -> WAArmadilloBackupMessage.BackupMessage.Metadata
	2, // 4: WAArmadilloBackupMessage.BackupMessage.Metadata.frankingMetadata:type_name -> WAArmadilloBackupMessage.BackupMessage.Metadata.FrankingMetadata
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_init() }
func file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_init() {
	if File_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto != nil {
		return
	}
	file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_msgTypes[0].OneofWrappers = []any{
		(*BackupMessage_EncryptedTransportMessage)(nil),
		(*BackupMessage_EncryptedTransportEvent)(nil),
		(*BackupMessage_EncryptedTransportLocallyTransformedMessage)(nil),
		(*BackupMessage_MiTransportAdminMessage)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDesc), len(file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_goTypes,
		DependencyIndexes: file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_depIdxs,
		MessageInfos:      file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_msgTypes,
	}.Build()
	File_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto = out.File
	file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_goTypes = nil
	file_waArmadilloBackupMessage_WAArmadilloBackupMessage_proto_depIdxs = nil
}
