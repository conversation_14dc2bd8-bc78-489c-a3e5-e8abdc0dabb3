// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waSyncAction/WASyncAction.proto

package waSyncAction

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	waChatLockSettings "go.mau.fi/whatsmeow/proto/waChatLockSettings"
	waCommon "go.mau.fi/whatsmeow/proto/waCommon"
	waDeviceCapabilities "go.mau.fi/whatsmeow/proto/waDeviceCapabilities"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CallLogRecord_CallType int32

const (
	CallLogRecord_REGULAR        CallLogRecord_CallType = 0
	CallLogRecord_SCHEDULED_CALL CallLogRecord_CallType = 1
	CallLogRecord_VOICE_CHAT     CallLogRecord_CallType = 2
)

// Enum value maps for CallLogRecord_CallType.
var (
	CallLogRecord_CallType_name = map[int32]string{
		0: "REGULAR",
		1: "SCHEDULED_CALL",
		2: "VOICE_CHAT",
	}
	CallLogRecord_CallType_value = map[string]int32{
		"REGULAR":        0,
		"SCHEDULED_CALL": 1,
		"VOICE_CHAT":     2,
	}
)

func (x CallLogRecord_CallType) Enum() *CallLogRecord_CallType {
	p := new(CallLogRecord_CallType)
	*p = x
	return p
}

func (x CallLogRecord_CallType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallLogRecord_CallType) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[0].Descriptor()
}

func (CallLogRecord_CallType) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[0]
}

func (x CallLogRecord_CallType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *CallLogRecord_CallType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = CallLogRecord_CallType(num)
	return nil
}

// Deprecated: Use CallLogRecord_CallType.Descriptor instead.
func (CallLogRecord_CallType) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{0, 0}
}

type CallLogRecord_SilenceReason int32

const (
	CallLogRecord_NONE        CallLogRecord_SilenceReason = 0
	CallLogRecord_SCHEDULED   CallLogRecord_SilenceReason = 1
	CallLogRecord_PRIVACY     CallLogRecord_SilenceReason = 2
	CallLogRecord_LIGHTWEIGHT CallLogRecord_SilenceReason = 3
)

// Enum value maps for CallLogRecord_SilenceReason.
var (
	CallLogRecord_SilenceReason_name = map[int32]string{
		0: "NONE",
		1: "SCHEDULED",
		2: "PRIVACY",
		3: "LIGHTWEIGHT",
	}
	CallLogRecord_SilenceReason_value = map[string]int32{
		"NONE":        0,
		"SCHEDULED":   1,
		"PRIVACY":     2,
		"LIGHTWEIGHT": 3,
	}
)

func (x CallLogRecord_SilenceReason) Enum() *CallLogRecord_SilenceReason {
	p := new(CallLogRecord_SilenceReason)
	*p = x
	return p
}

func (x CallLogRecord_SilenceReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallLogRecord_SilenceReason) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[1].Descriptor()
}

func (CallLogRecord_SilenceReason) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[1]
}

func (x CallLogRecord_SilenceReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *CallLogRecord_SilenceReason) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = CallLogRecord_SilenceReason(num)
	return nil
}

// Deprecated: Use CallLogRecord_SilenceReason.Descriptor instead.
func (CallLogRecord_SilenceReason) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{0, 1}
}

type CallLogRecord_CallResult int32

const (
	CallLogRecord_CONNECTED         CallLogRecord_CallResult = 0
	CallLogRecord_REJECTED          CallLogRecord_CallResult = 1
	CallLogRecord_CANCELLED         CallLogRecord_CallResult = 2
	CallLogRecord_ACCEPTEDELSEWHERE CallLogRecord_CallResult = 3
	CallLogRecord_MISSED            CallLogRecord_CallResult = 4
	CallLogRecord_INVALID           CallLogRecord_CallResult = 5
	CallLogRecord_UNAVAILABLE       CallLogRecord_CallResult = 6
	CallLogRecord_UPCOMING          CallLogRecord_CallResult = 7
	CallLogRecord_FAILED            CallLogRecord_CallResult = 8
	CallLogRecord_ABANDONED         CallLogRecord_CallResult = 9
	CallLogRecord_ONGOING           CallLogRecord_CallResult = 10
)

// Enum value maps for CallLogRecord_CallResult.
var (
	CallLogRecord_CallResult_name = map[int32]string{
		0:  "CONNECTED",
		1:  "REJECTED",
		2:  "CANCELLED",
		3:  "ACCEPTEDELSEWHERE",
		4:  "MISSED",
		5:  "INVALID",
		6:  "UNAVAILABLE",
		7:  "UPCOMING",
		8:  "FAILED",
		9:  "ABANDONED",
		10: "ONGOING",
	}
	CallLogRecord_CallResult_value = map[string]int32{
		"CONNECTED":         0,
		"REJECTED":          1,
		"CANCELLED":         2,
		"ACCEPTEDELSEWHERE": 3,
		"MISSED":            4,
		"INVALID":           5,
		"UNAVAILABLE":       6,
		"UPCOMING":          7,
		"FAILED":            8,
		"ABANDONED":         9,
		"ONGOING":           10,
	}
)

func (x CallLogRecord_CallResult) Enum() *CallLogRecord_CallResult {
	p := new(CallLogRecord_CallResult)
	*p = x
	return p
}

func (x CallLogRecord_CallResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallLogRecord_CallResult) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[2].Descriptor()
}

func (CallLogRecord_CallResult) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[2]
}

func (x CallLogRecord_CallResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *CallLogRecord_CallResult) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = CallLogRecord_CallResult(num)
	return nil
}

// Deprecated: Use CallLogRecord_CallResult.Descriptor instead.
func (CallLogRecord_CallResult) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{0, 2}
}

type MaibaAIFeaturesControlAction_MaibaAIFeatureStatus int32

const (
	MaibaAIFeaturesControlAction_ENABLED              MaibaAIFeaturesControlAction_MaibaAIFeatureStatus = 0
	MaibaAIFeaturesControlAction_ENABLED_HAS_LEARNING MaibaAIFeaturesControlAction_MaibaAIFeatureStatus = 1
	MaibaAIFeaturesControlAction_DISABLED             MaibaAIFeaturesControlAction_MaibaAIFeatureStatus = 2
)

// Enum value maps for MaibaAIFeaturesControlAction_MaibaAIFeatureStatus.
var (
	MaibaAIFeaturesControlAction_MaibaAIFeatureStatus_name = map[int32]string{
		0: "ENABLED",
		1: "ENABLED_HAS_LEARNING",
		2: "DISABLED",
	}
	MaibaAIFeaturesControlAction_MaibaAIFeatureStatus_value = map[string]int32{
		"ENABLED":              0,
		"ENABLED_HAS_LEARNING": 1,
		"DISABLED":             2,
	}
)

func (x MaibaAIFeaturesControlAction_MaibaAIFeatureStatus) Enum() *MaibaAIFeaturesControlAction_MaibaAIFeatureStatus {
	p := new(MaibaAIFeaturesControlAction_MaibaAIFeatureStatus)
	*p = x
	return p
}

func (x MaibaAIFeaturesControlAction_MaibaAIFeatureStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MaibaAIFeaturesControlAction_MaibaAIFeatureStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[3].Descriptor()
}

func (MaibaAIFeaturesControlAction_MaibaAIFeatureStatus) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[3]
}

func (x MaibaAIFeaturesControlAction_MaibaAIFeatureStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MaibaAIFeaturesControlAction_MaibaAIFeatureStatus) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MaibaAIFeaturesControlAction_MaibaAIFeatureStatus(num)
	return nil
}

// Deprecated: Use MaibaAIFeaturesControlAction_MaibaAIFeatureStatus.Descriptor instead.
func (MaibaAIFeaturesControlAction_MaibaAIFeatureStatus) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{1, 0}
}

type PaymentTosAction_PaymentNotice int32

const (
	PaymentTosAction_BR_PAY_PRIVACY_POLICY PaymentTosAction_PaymentNotice = 0
)

// Enum value maps for PaymentTosAction_PaymentNotice.
var (
	PaymentTosAction_PaymentNotice_name = map[int32]string{
		0: "BR_PAY_PRIVACY_POLICY",
	}
	PaymentTosAction_PaymentNotice_value = map[string]int32{
		"BR_PAY_PRIVACY_POLICY": 0,
	}
)

func (x PaymentTosAction_PaymentNotice) Enum() *PaymentTosAction_PaymentNotice {
	p := new(PaymentTosAction_PaymentNotice)
	*p = x
	return p
}

func (x PaymentTosAction_PaymentNotice) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentTosAction_PaymentNotice) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[4].Descriptor()
}

func (PaymentTosAction_PaymentNotice) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[4]
}

func (x PaymentTosAction_PaymentNotice) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PaymentTosAction_PaymentNotice) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PaymentTosAction_PaymentNotice(num)
	return nil
}

// Deprecated: Use PaymentTosAction_PaymentNotice.Descriptor instead.
func (PaymentTosAction_PaymentNotice) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{2, 0}
}

type NotificationActivitySettingAction_NotificationActivitySetting int32

const (
	NotificationActivitySettingAction_DEFAULT_ALL_MESSAGES NotificationActivitySettingAction_NotificationActivitySetting = 0
	NotificationActivitySettingAction_ALL_MESSAGES         NotificationActivitySettingAction_NotificationActivitySetting = 1
	NotificationActivitySettingAction_HIGHLIGHTS           NotificationActivitySettingAction_NotificationActivitySetting = 2
	NotificationActivitySettingAction_DEFAULT_HIGHLIGHTS   NotificationActivitySettingAction_NotificationActivitySetting = 3
)

// Enum value maps for NotificationActivitySettingAction_NotificationActivitySetting.
var (
	NotificationActivitySettingAction_NotificationActivitySetting_name = map[int32]string{
		0: "DEFAULT_ALL_MESSAGES",
		1: "ALL_MESSAGES",
		2: "HIGHLIGHTS",
		3: "DEFAULT_HIGHLIGHTS",
	}
	NotificationActivitySettingAction_NotificationActivitySetting_value = map[string]int32{
		"DEFAULT_ALL_MESSAGES": 0,
		"ALL_MESSAGES":         1,
		"HIGHLIGHTS":           2,
		"DEFAULT_HIGHLIGHTS":   3,
	}
)

func (x NotificationActivitySettingAction_NotificationActivitySetting) Enum() *NotificationActivitySettingAction_NotificationActivitySetting {
	p := new(NotificationActivitySettingAction_NotificationActivitySetting)
	*p = x
	return p
}

func (x NotificationActivitySettingAction_NotificationActivitySetting) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NotificationActivitySettingAction_NotificationActivitySetting) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[5].Descriptor()
}

func (NotificationActivitySettingAction_NotificationActivitySetting) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[5]
}

func (x NotificationActivitySettingAction_NotificationActivitySetting) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *NotificationActivitySettingAction_NotificationActivitySetting) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = NotificationActivitySettingAction_NotificationActivitySetting(num)
	return nil
}

// Deprecated: Use NotificationActivitySettingAction_NotificationActivitySetting.Descriptor instead.
func (NotificationActivitySettingAction_NotificationActivitySetting) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{3, 0}
}

type WaffleAccountLinkStateAction_AccountLinkState int32

const (
	WaffleAccountLinkStateAction_ACTIVE WaffleAccountLinkStateAction_AccountLinkState = 0
)

// Enum value maps for WaffleAccountLinkStateAction_AccountLinkState.
var (
	WaffleAccountLinkStateAction_AccountLinkState_name = map[int32]string{
		0: "ACTIVE",
	}
	WaffleAccountLinkStateAction_AccountLinkState_value = map[string]int32{
		"ACTIVE": 0,
	}
)

func (x WaffleAccountLinkStateAction_AccountLinkState) Enum() *WaffleAccountLinkStateAction_AccountLinkState {
	p := new(WaffleAccountLinkStateAction_AccountLinkState)
	*p = x
	return p
}

func (x WaffleAccountLinkStateAction_AccountLinkState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WaffleAccountLinkStateAction_AccountLinkState) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[6].Descriptor()
}

func (WaffleAccountLinkStateAction_AccountLinkState) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[6]
}

func (x WaffleAccountLinkStateAction_AccountLinkState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *WaffleAccountLinkStateAction_AccountLinkState) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = WaffleAccountLinkStateAction_AccountLinkState(num)
	return nil
}

// Deprecated: Use WaffleAccountLinkStateAction_AccountLinkState.Descriptor instead.
func (WaffleAccountLinkStateAction_AccountLinkState) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{4, 0}
}

type MerchantPaymentPartnerAction_Status int32

const (
	MerchantPaymentPartnerAction_ACTIVE   MerchantPaymentPartnerAction_Status = 0
	MerchantPaymentPartnerAction_INACTIVE MerchantPaymentPartnerAction_Status = 1
)

// Enum value maps for MerchantPaymentPartnerAction_Status.
var (
	MerchantPaymentPartnerAction_Status_name = map[int32]string{
		0: "ACTIVE",
		1: "INACTIVE",
	}
	MerchantPaymentPartnerAction_Status_value = map[string]int32{
		"ACTIVE":   0,
		"INACTIVE": 1,
	}
)

func (x MerchantPaymentPartnerAction_Status) Enum() *MerchantPaymentPartnerAction_Status {
	p := new(MerchantPaymentPartnerAction_Status)
	*p = x
	return p
}

func (x MerchantPaymentPartnerAction_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MerchantPaymentPartnerAction_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[7].Descriptor()
}

func (MerchantPaymentPartnerAction_Status) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[7]
}

func (x MerchantPaymentPartnerAction_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MerchantPaymentPartnerAction_Status) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MerchantPaymentPartnerAction_Status(num)
	return nil
}

// Deprecated: Use MerchantPaymentPartnerAction_Status.Descriptor instead.
func (MerchantPaymentPartnerAction_Status) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{5, 0}
}

type NoteEditAction_NoteType int32

const (
	NoteEditAction_UNSTRUCTURED NoteEditAction_NoteType = 1
	NoteEditAction_STRUCTURED   NoteEditAction_NoteType = 2
)

// Enum value maps for NoteEditAction_NoteType.
var (
	NoteEditAction_NoteType_name = map[int32]string{
		1: "UNSTRUCTURED",
		2: "STRUCTURED",
	}
	NoteEditAction_NoteType_value = map[string]int32{
		"UNSTRUCTURED": 1,
		"STRUCTURED":   2,
	}
)

func (x NoteEditAction_NoteType) Enum() *NoteEditAction_NoteType {
	p := new(NoteEditAction_NoteType)
	*p = x
	return p
}

func (x NoteEditAction_NoteType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NoteEditAction_NoteType) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[8].Descriptor()
}

func (NoteEditAction_NoteType) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[8]
}

func (x NoteEditAction_NoteType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *NoteEditAction_NoteType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = NoteEditAction_NoteType(num)
	return nil
}

// Deprecated: Use NoteEditAction_NoteType.Descriptor instead.
func (NoteEditAction_NoteType) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{6, 0}
}

type StatusPrivacyAction_StatusDistributionMode int32

const (
	StatusPrivacyAction_ALLOW_LIST StatusPrivacyAction_StatusDistributionMode = 0
	StatusPrivacyAction_DENY_LIST  StatusPrivacyAction_StatusDistributionMode = 1
	StatusPrivacyAction_CONTACTS   StatusPrivacyAction_StatusDistributionMode = 2
)

// Enum value maps for StatusPrivacyAction_StatusDistributionMode.
var (
	StatusPrivacyAction_StatusDistributionMode_name = map[int32]string{
		0: "ALLOW_LIST",
		1: "DENY_LIST",
		2: "CONTACTS",
	}
	StatusPrivacyAction_StatusDistributionMode_value = map[string]int32{
		"ALLOW_LIST": 0,
		"DENY_LIST":  1,
		"CONTACTS":   2,
	}
)

func (x StatusPrivacyAction_StatusDistributionMode) Enum() *StatusPrivacyAction_StatusDistributionMode {
	p := new(StatusPrivacyAction_StatusDistributionMode)
	*p = x
	return p
}

func (x StatusPrivacyAction_StatusDistributionMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusPrivacyAction_StatusDistributionMode) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[9].Descriptor()
}

func (StatusPrivacyAction_StatusDistributionMode) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[9]
}

func (x StatusPrivacyAction_StatusDistributionMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *StatusPrivacyAction_StatusDistributionMode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = StatusPrivacyAction_StatusDistributionMode(num)
	return nil
}

// Deprecated: Use StatusPrivacyAction_StatusDistributionMode.Descriptor instead.
func (StatusPrivacyAction_StatusDistributionMode) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{7, 0}
}

type MarketingMessageAction_MarketingMessagePrototypeType int32

const (
	MarketingMessageAction_PERSONALIZED MarketingMessageAction_MarketingMessagePrototypeType = 0
)

// Enum value maps for MarketingMessageAction_MarketingMessagePrototypeType.
var (
	MarketingMessageAction_MarketingMessagePrototypeType_name = map[int32]string{
		0: "PERSONALIZED",
	}
	MarketingMessageAction_MarketingMessagePrototypeType_value = map[string]int32{
		"PERSONALIZED": 0,
	}
)

func (x MarketingMessageAction_MarketingMessagePrototypeType) Enum() *MarketingMessageAction_MarketingMessagePrototypeType {
	p := new(MarketingMessageAction_MarketingMessagePrototypeType)
	*p = x
	return p
}

func (x MarketingMessageAction_MarketingMessagePrototypeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MarketingMessageAction_MarketingMessagePrototypeType) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[10].Descriptor()
}

func (MarketingMessageAction_MarketingMessagePrototypeType) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[10]
}

func (x MarketingMessageAction_MarketingMessagePrototypeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *MarketingMessageAction_MarketingMessagePrototypeType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = MarketingMessageAction_MarketingMessagePrototypeType(num)
	return nil
}

// Deprecated: Use MarketingMessageAction_MarketingMessagePrototypeType.Descriptor instead.
func (MarketingMessageAction_MarketingMessagePrototypeType) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{8, 0}
}

type UsernameChatStartModeAction_ChatStartMode int32

const (
	UsernameChatStartModeAction_LID UsernameChatStartModeAction_ChatStartMode = 1
	UsernameChatStartModeAction_PN  UsernameChatStartModeAction_ChatStartMode = 2
)

// Enum value maps for UsernameChatStartModeAction_ChatStartMode.
var (
	UsernameChatStartModeAction_ChatStartMode_name = map[int32]string{
		1: "LID",
		2: "PN",
	}
	UsernameChatStartModeAction_ChatStartMode_value = map[string]int32{
		"LID": 1,
		"PN":  2,
	}
)

func (x UsernameChatStartModeAction_ChatStartMode) Enum() *UsernameChatStartModeAction_ChatStartMode {
	p := new(UsernameChatStartModeAction_ChatStartMode)
	*p = x
	return p
}

func (x UsernameChatStartModeAction_ChatStartMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UsernameChatStartModeAction_ChatStartMode) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[11].Descriptor()
}

func (UsernameChatStartModeAction_ChatStartMode) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[11]
}

func (x UsernameChatStartModeAction_ChatStartMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *UsernameChatStartModeAction_ChatStartMode) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = UsernameChatStartModeAction_ChatStartMode(num)
	return nil
}

// Deprecated: Use UsernameChatStartModeAction_ChatStartMode.Descriptor instead.
func (UsernameChatStartModeAction_ChatStartMode) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{9, 0}
}

type LabelEditAction_ListType int32

const (
	LabelEditAction_NONE            LabelEditAction_ListType = 0
	LabelEditAction_UNREAD          LabelEditAction_ListType = 1
	LabelEditAction_GROUPS          LabelEditAction_ListType = 2
	LabelEditAction_FAVORITES       LabelEditAction_ListType = 3
	LabelEditAction_PREDEFINED      LabelEditAction_ListType = 4
	LabelEditAction_CUSTOM          LabelEditAction_ListType = 5
	LabelEditAction_COMMUNITY       LabelEditAction_ListType = 6
	LabelEditAction_SERVER_ASSIGNED LabelEditAction_ListType = 7
)

// Enum value maps for LabelEditAction_ListType.
var (
	LabelEditAction_ListType_name = map[int32]string{
		0: "NONE",
		1: "UNREAD",
		2: "GROUPS",
		3: "FAVORITES",
		4: "PREDEFINED",
		5: "CUSTOM",
		6: "COMMUNITY",
		7: "SERVER_ASSIGNED",
	}
	LabelEditAction_ListType_value = map[string]int32{
		"NONE":            0,
		"UNREAD":          1,
		"GROUPS":          2,
		"FAVORITES":       3,
		"PREDEFINED":      4,
		"CUSTOM":          5,
		"COMMUNITY":       6,
		"SERVER_ASSIGNED": 7,
	}
)

func (x LabelEditAction_ListType) Enum() *LabelEditAction_ListType {
	p := new(LabelEditAction_ListType)
	*p = x
	return p
}

func (x LabelEditAction_ListType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LabelEditAction_ListType) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[12].Descriptor()
}

func (LabelEditAction_ListType) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[12]
}

func (x LabelEditAction_ListType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *LabelEditAction_ListType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = LabelEditAction_ListType(num)
	return nil
}

// Deprecated: Use LabelEditAction_ListType.Descriptor instead.
func (LabelEditAction_ListType) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{10, 0}
}

type PatchDebugData_Platform int32

const (
	PatchDebugData_ANDROID PatchDebugData_Platform = 0
	PatchDebugData_SMBA    PatchDebugData_Platform = 1
	PatchDebugData_IPHONE  PatchDebugData_Platform = 2
	PatchDebugData_SMBI    PatchDebugData_Platform = 3
	PatchDebugData_WEB     PatchDebugData_Platform = 4
	PatchDebugData_UWP     PatchDebugData_Platform = 5
	PatchDebugData_DARWIN  PatchDebugData_Platform = 6
	PatchDebugData_IPAD    PatchDebugData_Platform = 7
	PatchDebugData_WEAROS  PatchDebugData_Platform = 8
)

// Enum value maps for PatchDebugData_Platform.
var (
	PatchDebugData_Platform_name = map[int32]string{
		0: "ANDROID",
		1: "SMBA",
		2: "IPHONE",
		3: "SMBI",
		4: "WEB",
		5: "UWP",
		6: "DARWIN",
		7: "IPAD",
		8: "WEAROS",
	}
	PatchDebugData_Platform_value = map[string]int32{
		"ANDROID": 0,
		"SMBA":    1,
		"IPHONE":  2,
		"SMBI":    3,
		"WEB":     4,
		"UWP":     5,
		"DARWIN":  6,
		"IPAD":    7,
		"WEAROS":  8,
	}
)

func (x PatchDebugData_Platform) Enum() *PatchDebugData_Platform {
	p := new(PatchDebugData_Platform)
	*p = x
	return p
}

func (x PatchDebugData_Platform) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PatchDebugData_Platform) Descriptor() protoreflect.EnumDescriptor {
	return file_waSyncAction_WASyncAction_proto_enumTypes[13].Descriptor()
}

func (PatchDebugData_Platform) Type() protoreflect.EnumType {
	return &file_waSyncAction_WASyncAction_proto_enumTypes[13]
}

func (x PatchDebugData_Platform) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PatchDebugData_Platform) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PatchDebugData_Platform(num)
	return nil
}

// Deprecated: Use PatchDebugData_Platform.Descriptor instead.
func (PatchDebugData_Platform) EnumDescriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{11, 0}
}

type CallLogRecord struct {
	state           protoimpl.MessageState           `protogen:"open.v1"`
	CallResult      *CallLogRecord_CallResult        `protobuf:"varint,1,opt,name=callResult,enum=WASyncAction.CallLogRecord_CallResult" json:"callResult,omitempty"`
	IsDndMode       *bool                            `protobuf:"varint,2,opt,name=isDndMode" json:"isDndMode,omitempty"`
	SilenceReason   *CallLogRecord_SilenceReason     `protobuf:"varint,3,opt,name=silenceReason,enum=WASyncAction.CallLogRecord_SilenceReason" json:"silenceReason,omitempty"`
	Duration        *int64                           `protobuf:"varint,4,opt,name=duration" json:"duration,omitempty"`
	StartTime       *int64                           `protobuf:"varint,5,opt,name=startTime" json:"startTime,omitempty"`
	IsIncoming      *bool                            `protobuf:"varint,6,opt,name=isIncoming" json:"isIncoming,omitempty"`
	IsVideo         *bool                            `protobuf:"varint,7,opt,name=isVideo" json:"isVideo,omitempty"`
	IsCallLink      *bool                            `protobuf:"varint,8,opt,name=isCallLink" json:"isCallLink,omitempty"`
	CallLinkToken   *string                          `protobuf:"bytes,9,opt,name=callLinkToken" json:"callLinkToken,omitempty"`
	ScheduledCallID *string                          `protobuf:"bytes,10,opt,name=scheduledCallID" json:"scheduledCallID,omitempty"`
	CallID          *string                          `protobuf:"bytes,11,opt,name=callID" json:"callID,omitempty"`
	CallCreatorJID  *string                          `protobuf:"bytes,12,opt,name=callCreatorJID" json:"callCreatorJID,omitempty"`
	GroupJID        *string                          `protobuf:"bytes,13,opt,name=groupJID" json:"groupJID,omitempty"`
	Participants    []*CallLogRecord_ParticipantInfo `protobuf:"bytes,14,rep,name=participants" json:"participants,omitempty"`
	CallType        *CallLogRecord_CallType          `protobuf:"varint,15,opt,name=callType,enum=WASyncAction.CallLogRecord_CallType" json:"callType,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CallLogRecord) Reset() {
	*x = CallLogRecord{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallLogRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallLogRecord) ProtoMessage() {}

func (x *CallLogRecord) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallLogRecord.ProtoReflect.Descriptor instead.
func (*CallLogRecord) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{0}
}

func (x *CallLogRecord) GetCallResult() CallLogRecord_CallResult {
	if x != nil && x.CallResult != nil {
		return *x.CallResult
	}
	return CallLogRecord_CONNECTED
}

func (x *CallLogRecord) GetIsDndMode() bool {
	if x != nil && x.IsDndMode != nil {
		return *x.IsDndMode
	}
	return false
}

func (x *CallLogRecord) GetSilenceReason() CallLogRecord_SilenceReason {
	if x != nil && x.SilenceReason != nil {
		return *x.SilenceReason
	}
	return CallLogRecord_NONE
}

func (x *CallLogRecord) GetDuration() int64 {
	if x != nil && x.Duration != nil {
		return *x.Duration
	}
	return 0
}

func (x *CallLogRecord) GetStartTime() int64 {
	if x != nil && x.StartTime != nil {
		return *x.StartTime
	}
	return 0
}

func (x *CallLogRecord) GetIsIncoming() bool {
	if x != nil && x.IsIncoming != nil {
		return *x.IsIncoming
	}
	return false
}

func (x *CallLogRecord) GetIsVideo() bool {
	if x != nil && x.IsVideo != nil {
		return *x.IsVideo
	}
	return false
}

func (x *CallLogRecord) GetIsCallLink() bool {
	if x != nil && x.IsCallLink != nil {
		return *x.IsCallLink
	}
	return false
}

func (x *CallLogRecord) GetCallLinkToken() string {
	if x != nil && x.CallLinkToken != nil {
		return *x.CallLinkToken
	}
	return ""
}

func (x *CallLogRecord) GetScheduledCallID() string {
	if x != nil && x.ScheduledCallID != nil {
		return *x.ScheduledCallID
	}
	return ""
}

func (x *CallLogRecord) GetCallID() string {
	if x != nil && x.CallID != nil {
		return *x.CallID
	}
	return ""
}

func (x *CallLogRecord) GetCallCreatorJID() string {
	if x != nil && x.CallCreatorJID != nil {
		return *x.CallCreatorJID
	}
	return ""
}

func (x *CallLogRecord) GetGroupJID() string {
	if x != nil && x.GroupJID != nil {
		return *x.GroupJID
	}
	return ""
}

func (x *CallLogRecord) GetParticipants() []*CallLogRecord_ParticipantInfo {
	if x != nil {
		return x.Participants
	}
	return nil
}

func (x *CallLogRecord) GetCallType() CallLogRecord_CallType {
	if x != nil && x.CallType != nil {
		return *x.CallType
	}
	return CallLogRecord_REGULAR
}

type MaibaAIFeaturesControlAction struct {
	state           protoimpl.MessageState                             `protogen:"open.v1"`
	AiFeatureStatus *MaibaAIFeaturesControlAction_MaibaAIFeatureStatus `protobuf:"varint,1,opt,name=aiFeatureStatus,enum=WASyncAction.MaibaAIFeaturesControlAction_MaibaAIFeatureStatus" json:"aiFeatureStatus,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *MaibaAIFeaturesControlAction) Reset() {
	*x = MaibaAIFeaturesControlAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaibaAIFeaturesControlAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaibaAIFeaturesControlAction) ProtoMessage() {}

func (x *MaibaAIFeaturesControlAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaibaAIFeaturesControlAction.ProtoReflect.Descriptor instead.
func (*MaibaAIFeaturesControlAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{1}
}

func (x *MaibaAIFeaturesControlAction) GetAiFeatureStatus() MaibaAIFeaturesControlAction_MaibaAIFeatureStatus {
	if x != nil && x.AiFeatureStatus != nil {
		return *x.AiFeatureStatus
	}
	return MaibaAIFeaturesControlAction_ENABLED
}

type PaymentTosAction struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	PaymentNotice *PaymentTosAction_PaymentNotice `protobuf:"varint,1,req,name=paymentNotice,enum=WASyncAction.PaymentTosAction_PaymentNotice" json:"paymentNotice,omitempty"`
	Accepted      *bool                           `protobuf:"varint,2,req,name=accepted" json:"accepted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaymentTosAction) Reset() {
	*x = PaymentTosAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentTosAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentTosAction) ProtoMessage() {}

func (x *PaymentTosAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentTosAction.ProtoReflect.Descriptor instead.
func (*PaymentTosAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{2}
}

func (x *PaymentTosAction) GetPaymentNotice() PaymentTosAction_PaymentNotice {
	if x != nil && x.PaymentNotice != nil {
		return *x.PaymentNotice
	}
	return PaymentTosAction_BR_PAY_PRIVACY_POLICY
}

func (x *PaymentTosAction) GetAccepted() bool {
	if x != nil && x.Accepted != nil {
		return *x.Accepted
	}
	return false
}

type NotificationActivitySettingAction struct {
	state                       protoimpl.MessageState                                         `protogen:"open.v1"`
	NotificationActivitySetting *NotificationActivitySettingAction_NotificationActivitySetting `protobuf:"varint,1,opt,name=notificationActivitySetting,enum=WASyncAction.NotificationActivitySettingAction_NotificationActivitySetting" json:"notificationActivitySetting,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *NotificationActivitySettingAction) Reset() {
	*x = NotificationActivitySettingAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationActivitySettingAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationActivitySettingAction) ProtoMessage() {}

func (x *NotificationActivitySettingAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationActivitySettingAction.ProtoReflect.Descriptor instead.
func (*NotificationActivitySettingAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{3}
}

func (x *NotificationActivitySettingAction) GetNotificationActivitySetting() NotificationActivitySettingAction_NotificationActivitySetting {
	if x != nil && x.NotificationActivitySetting != nil {
		return *x.NotificationActivitySetting
	}
	return NotificationActivitySettingAction_DEFAULT_ALL_MESSAGES
}

type WaffleAccountLinkStateAction struct {
	state         protoimpl.MessageState                         `protogen:"open.v1"`
	LinkState     *WaffleAccountLinkStateAction_AccountLinkState `protobuf:"varint,2,opt,name=linkState,enum=WASyncAction.WaffleAccountLinkStateAction_AccountLinkState" json:"linkState,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WaffleAccountLinkStateAction) Reset() {
	*x = WaffleAccountLinkStateAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WaffleAccountLinkStateAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WaffleAccountLinkStateAction) ProtoMessage() {}

func (x *WaffleAccountLinkStateAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WaffleAccountLinkStateAction.ProtoReflect.Descriptor instead.
func (*WaffleAccountLinkStateAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{4}
}

func (x *WaffleAccountLinkStateAction) GetLinkState() WaffleAccountLinkStateAction_AccountLinkState {
	if x != nil && x.LinkState != nil {
		return *x.LinkState
	}
	return WaffleAccountLinkStateAction_ACTIVE
}

type MerchantPaymentPartnerAction struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	Status        *MerchantPaymentPartnerAction_Status `protobuf:"varint,1,req,name=status,enum=WASyncAction.MerchantPaymentPartnerAction_Status" json:"status,omitempty"`
	Country       *string                              `protobuf:"bytes,2,req,name=country" json:"country,omitempty"`
	GatewayName   *string                              `protobuf:"bytes,3,opt,name=gatewayName" json:"gatewayName,omitempty"`
	CredentialID  *string                              `protobuf:"bytes,4,opt,name=credentialID" json:"credentialID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPaymentPartnerAction) Reset() {
	*x = MerchantPaymentPartnerAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentPartnerAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentPartnerAction) ProtoMessage() {}

func (x *MerchantPaymentPartnerAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentPartnerAction.ProtoReflect.Descriptor instead.
func (*MerchantPaymentPartnerAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{5}
}

func (x *MerchantPaymentPartnerAction) GetStatus() MerchantPaymentPartnerAction_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return MerchantPaymentPartnerAction_ACTIVE
}

func (x *MerchantPaymentPartnerAction) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *MerchantPaymentPartnerAction) GetGatewayName() string {
	if x != nil && x.GatewayName != nil {
		return *x.GatewayName
	}
	return ""
}

func (x *MerchantPaymentPartnerAction) GetCredentialID() string {
	if x != nil && x.CredentialID != nil {
		return *x.CredentialID
	}
	return ""
}

type NoteEditAction struct {
	state               protoimpl.MessageState   `protogen:"open.v1"`
	Type                *NoteEditAction_NoteType `protobuf:"varint,1,opt,name=type,enum=WASyncAction.NoteEditAction_NoteType" json:"type,omitempty"`
	ChatJID             *string                  `protobuf:"bytes,2,opt,name=chatJID" json:"chatJID,omitempty"`
	CreatedAt           *int64                   `protobuf:"varint,3,opt,name=createdAt" json:"createdAt,omitempty"`
	Deleted             *bool                    `protobuf:"varint,4,opt,name=deleted" json:"deleted,omitempty"`
	UnstructuredContent *string                  `protobuf:"bytes,5,opt,name=unstructuredContent" json:"unstructuredContent,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *NoteEditAction) Reset() {
	*x = NoteEditAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoteEditAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoteEditAction) ProtoMessage() {}

func (x *NoteEditAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoteEditAction.ProtoReflect.Descriptor instead.
func (*NoteEditAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{6}
}

func (x *NoteEditAction) GetType() NoteEditAction_NoteType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return NoteEditAction_UNSTRUCTURED
}

func (x *NoteEditAction) GetChatJID() string {
	if x != nil && x.ChatJID != nil {
		return *x.ChatJID
	}
	return ""
}

func (x *NoteEditAction) GetCreatedAt() int64 {
	if x != nil && x.CreatedAt != nil {
		return *x.CreatedAt
	}
	return 0
}

func (x *NoteEditAction) GetDeleted() bool {
	if x != nil && x.Deleted != nil {
		return *x.Deleted
	}
	return false
}

func (x *NoteEditAction) GetUnstructuredContent() string {
	if x != nil && x.UnstructuredContent != nil {
		return *x.UnstructuredContent
	}
	return ""
}

type StatusPrivacyAction struct {
	state         protoimpl.MessageState                      `protogen:"open.v1"`
	Mode          *StatusPrivacyAction_StatusDistributionMode `protobuf:"varint,1,opt,name=mode,enum=WASyncAction.StatusPrivacyAction_StatusDistributionMode" json:"mode,omitempty"`
	UserJID       []string                                    `protobuf:"bytes,2,rep,name=userJID" json:"userJID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StatusPrivacyAction) Reset() {
	*x = StatusPrivacyAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatusPrivacyAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusPrivacyAction) ProtoMessage() {}

func (x *StatusPrivacyAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusPrivacyAction.ProtoReflect.Descriptor instead.
func (*StatusPrivacyAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{7}
}

func (x *StatusPrivacyAction) GetMode() StatusPrivacyAction_StatusDistributionMode {
	if x != nil && x.Mode != nil {
		return *x.Mode
	}
	return StatusPrivacyAction_ALLOW_LIST
}

func (x *StatusPrivacyAction) GetUserJID() []string {
	if x != nil {
		return x.UserJID
	}
	return nil
}

type MarketingMessageAction struct {
	state         protoimpl.MessageState                                `protogen:"open.v1"`
	Name          *string                                               `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Message       *string                                               `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	Type          *MarketingMessageAction_MarketingMessagePrototypeType `protobuf:"varint,3,opt,name=type,enum=WASyncAction.MarketingMessageAction_MarketingMessagePrototypeType" json:"type,omitempty"`
	CreatedAt     *int64                                                `protobuf:"varint,4,opt,name=createdAt" json:"createdAt,omitempty"`
	LastSentAt    *int64                                                `protobuf:"varint,5,opt,name=lastSentAt" json:"lastSentAt,omitempty"`
	IsDeleted     *bool                                                 `protobuf:"varint,6,opt,name=isDeleted" json:"isDeleted,omitempty"`
	MediaID       *string                                               `protobuf:"bytes,7,opt,name=mediaID" json:"mediaID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarketingMessageAction) Reset() {
	*x = MarketingMessageAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarketingMessageAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingMessageAction) ProtoMessage() {}

func (x *MarketingMessageAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingMessageAction.ProtoReflect.Descriptor instead.
func (*MarketingMessageAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{8}
}

func (x *MarketingMessageAction) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *MarketingMessageAction) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *MarketingMessageAction) GetType() MarketingMessageAction_MarketingMessagePrototypeType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return MarketingMessageAction_PERSONALIZED
}

func (x *MarketingMessageAction) GetCreatedAt() int64 {
	if x != nil && x.CreatedAt != nil {
		return *x.CreatedAt
	}
	return 0
}

func (x *MarketingMessageAction) GetLastSentAt() int64 {
	if x != nil && x.LastSentAt != nil {
		return *x.LastSentAt
	}
	return 0
}

func (x *MarketingMessageAction) GetIsDeleted() bool {
	if x != nil && x.IsDeleted != nil {
		return *x.IsDeleted
	}
	return false
}

func (x *MarketingMessageAction) GetMediaID() string {
	if x != nil && x.MediaID != nil {
		return *x.MediaID
	}
	return ""
}

type UsernameChatStartModeAction struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	ChatStartMode *UsernameChatStartModeAction_ChatStartMode `protobuf:"varint,1,opt,name=chatStartMode,enum=WASyncAction.UsernameChatStartModeAction_ChatStartMode" json:"chatStartMode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UsernameChatStartModeAction) Reset() {
	*x = UsernameChatStartModeAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UsernameChatStartModeAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UsernameChatStartModeAction) ProtoMessage() {}

func (x *UsernameChatStartModeAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UsernameChatStartModeAction.ProtoReflect.Descriptor instead.
func (*UsernameChatStartModeAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{9}
}

func (x *UsernameChatStartModeAction) GetChatStartMode() UsernameChatStartModeAction_ChatStartMode {
	if x != nil && x.ChatStartMode != nil {
		return *x.ChatStartMode
	}
	return UsernameChatStartModeAction_LID
}

type LabelEditAction struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Name          *string                   `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	Color         *int32                    `protobuf:"varint,2,opt,name=color" json:"color,omitempty"`
	PredefinedID  *int32                    `protobuf:"varint,3,opt,name=predefinedID" json:"predefinedID,omitempty"`
	Deleted       *bool                     `protobuf:"varint,4,opt,name=deleted" json:"deleted,omitempty"`
	OrderIndex    *int32                    `protobuf:"varint,5,opt,name=orderIndex" json:"orderIndex,omitempty"`
	IsActive      *bool                     `protobuf:"varint,6,opt,name=isActive" json:"isActive,omitempty"`
	Type          *LabelEditAction_ListType `protobuf:"varint,7,opt,name=type,enum=WASyncAction.LabelEditAction_ListType" json:"type,omitempty"`
	IsImmutable   *bool                     `protobuf:"varint,8,opt,name=isImmutable" json:"isImmutable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LabelEditAction) Reset() {
	*x = LabelEditAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelEditAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelEditAction) ProtoMessage() {}

func (x *LabelEditAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelEditAction.ProtoReflect.Descriptor instead.
func (*LabelEditAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{10}
}

func (x *LabelEditAction) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *LabelEditAction) GetColor() int32 {
	if x != nil && x.Color != nil {
		return *x.Color
	}
	return 0
}

func (x *LabelEditAction) GetPredefinedID() int32 {
	if x != nil && x.PredefinedID != nil {
		return *x.PredefinedID
	}
	return 0
}

func (x *LabelEditAction) GetDeleted() bool {
	if x != nil && x.Deleted != nil {
		return *x.Deleted
	}
	return false
}

func (x *LabelEditAction) GetOrderIndex() int32 {
	if x != nil && x.OrderIndex != nil {
		return *x.OrderIndex
	}
	return 0
}

func (x *LabelEditAction) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *LabelEditAction) GetType() LabelEditAction_ListType {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return LabelEditAction_NONE
}

func (x *LabelEditAction) GetIsImmutable() bool {
	if x != nil && x.IsImmutable != nil {
		return *x.IsImmutable
	}
	return false
}

type PatchDebugData struct {
	state                                   protoimpl.MessageState   `protogen:"open.v1"`
	CurrentLthash                           []byte                   `protobuf:"bytes,1,opt,name=currentLthash" json:"currentLthash,omitempty"`
	NewLthash                               []byte                   `protobuf:"bytes,2,opt,name=newLthash" json:"newLthash,omitempty"`
	PatchVersion                            []byte                   `protobuf:"bytes,3,opt,name=patchVersion" json:"patchVersion,omitempty"`
	CollectionName                          []byte                   `protobuf:"bytes,4,opt,name=collectionName" json:"collectionName,omitempty"`
	FirstFourBytesFromAHashOfSnapshotMACKey []byte                   `protobuf:"bytes,5,opt,name=firstFourBytesFromAHashOfSnapshotMACKey" json:"firstFourBytesFromAHashOfSnapshotMACKey,omitempty"`
	NewLthashSubtract                       []byte                   `protobuf:"bytes,6,opt,name=newLthashSubtract" json:"newLthashSubtract,omitempty"`
	NumberAdd                               *int32                   `protobuf:"varint,7,opt,name=numberAdd" json:"numberAdd,omitempty"`
	NumberRemove                            *int32                   `protobuf:"varint,8,opt,name=numberRemove" json:"numberRemove,omitempty"`
	NumberOverride                          *int32                   `protobuf:"varint,9,opt,name=numberOverride" json:"numberOverride,omitempty"`
	SenderPlatform                          *PatchDebugData_Platform `protobuf:"varint,10,opt,name=senderPlatform,enum=WASyncAction.PatchDebugData_Platform" json:"senderPlatform,omitempty"`
	IsSenderPrimary                         *bool                    `protobuf:"varint,11,opt,name=isSenderPrimary" json:"isSenderPrimary,omitempty"`
	unknownFields                           protoimpl.UnknownFields
	sizeCache                               protoimpl.SizeCache
}

func (x *PatchDebugData) Reset() {
	*x = PatchDebugData{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PatchDebugData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PatchDebugData) ProtoMessage() {}

func (x *PatchDebugData) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PatchDebugData.ProtoReflect.Descriptor instead.
func (*PatchDebugData) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{11}
}

func (x *PatchDebugData) GetCurrentLthash() []byte {
	if x != nil {
		return x.CurrentLthash
	}
	return nil
}

func (x *PatchDebugData) GetNewLthash() []byte {
	if x != nil {
		return x.NewLthash
	}
	return nil
}

func (x *PatchDebugData) GetPatchVersion() []byte {
	if x != nil {
		return x.PatchVersion
	}
	return nil
}

func (x *PatchDebugData) GetCollectionName() []byte {
	if x != nil {
		return x.CollectionName
	}
	return nil
}

func (x *PatchDebugData) GetFirstFourBytesFromAHashOfSnapshotMACKey() []byte {
	if x != nil {
		return x.FirstFourBytesFromAHashOfSnapshotMACKey
	}
	return nil
}

func (x *PatchDebugData) GetNewLthashSubtract() []byte {
	if x != nil {
		return x.NewLthashSubtract
	}
	return nil
}

func (x *PatchDebugData) GetNumberAdd() int32 {
	if x != nil && x.NumberAdd != nil {
		return *x.NumberAdd
	}
	return 0
}

func (x *PatchDebugData) GetNumberRemove() int32 {
	if x != nil && x.NumberRemove != nil {
		return *x.NumberRemove
	}
	return 0
}

func (x *PatchDebugData) GetNumberOverride() int32 {
	if x != nil && x.NumberOverride != nil {
		return *x.NumberOverride
	}
	return 0
}

func (x *PatchDebugData) GetSenderPlatform() PatchDebugData_Platform {
	if x != nil && x.SenderPlatform != nil {
		return *x.SenderPlatform
	}
	return PatchDebugData_ANDROID
}

func (x *PatchDebugData) GetIsSenderPrimary() bool {
	if x != nil && x.IsSenderPrimary != nil {
		return *x.IsSenderPrimary
	}
	return false
}

type RecentEmojiWeight struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Emoji         *string                `protobuf:"bytes,1,opt,name=emoji" json:"emoji,omitempty"`
	Weight        *float32               `protobuf:"fixed32,2,opt,name=weight" json:"weight,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecentEmojiWeight) Reset() {
	*x = RecentEmojiWeight{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecentEmojiWeight) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecentEmojiWeight) ProtoMessage() {}

func (x *RecentEmojiWeight) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecentEmojiWeight.ProtoReflect.Descriptor instead.
func (*RecentEmojiWeight) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{12}
}

func (x *RecentEmojiWeight) GetEmoji() string {
	if x != nil && x.Emoji != nil {
		return *x.Emoji
	}
	return ""
}

func (x *RecentEmojiWeight) GetWeight() float32 {
	if x != nil && x.Weight != nil {
		return *x.Weight
	}
	return 0
}

type SyncActionValue struct {
	state                                                  protoimpl.MessageState                                  `protogen:"open.v1"`
	Timestamp                                              *int64                                                  `protobuf:"varint,1,opt,name=timestamp" json:"timestamp,omitempty"`
	StarAction                                             *StarAction                                             `protobuf:"bytes,2,opt,name=starAction" json:"starAction,omitempty"`
	ContactAction                                          *ContactAction                                          `protobuf:"bytes,3,opt,name=contactAction" json:"contactAction,omitempty"`
	MuteAction                                             *MuteAction                                             `protobuf:"bytes,4,opt,name=muteAction" json:"muteAction,omitempty"`
	PinAction                                              *PinAction                                              `protobuf:"bytes,5,opt,name=pinAction" json:"pinAction,omitempty"`
	SecurityNotificationSetting                            *SecurityNotificationSetting                            `protobuf:"bytes,6,opt,name=securityNotificationSetting" json:"securityNotificationSetting,omitempty"`
	PushNameSetting                                        *PushNameSetting                                        `protobuf:"bytes,7,opt,name=pushNameSetting" json:"pushNameSetting,omitempty"`
	QuickReplyAction                                       *QuickReplyAction                                       `protobuf:"bytes,8,opt,name=quickReplyAction" json:"quickReplyAction,omitempty"`
	RecentEmojiWeightsAction                               *RecentEmojiWeightsAction                               `protobuf:"bytes,11,opt,name=recentEmojiWeightsAction" json:"recentEmojiWeightsAction,omitempty"`
	LabelEditAction                                        *LabelEditAction                                        `protobuf:"bytes,14,opt,name=labelEditAction" json:"labelEditAction,omitempty"`
	LabelAssociationAction                                 *LabelAssociationAction                                 `protobuf:"bytes,15,opt,name=labelAssociationAction" json:"labelAssociationAction,omitempty"`
	LocaleSetting                                          *LocaleSetting                                          `protobuf:"bytes,16,opt,name=localeSetting" json:"localeSetting,omitempty"`
	ArchiveChatAction                                      *ArchiveChatAction                                      `protobuf:"bytes,17,opt,name=archiveChatAction" json:"archiveChatAction,omitempty"`
	DeleteMessageForMeAction                               *DeleteMessageForMeAction                               `protobuf:"bytes,18,opt,name=deleteMessageForMeAction" json:"deleteMessageForMeAction,omitempty"`
	KeyExpiration                                          *KeyExpiration                                          `protobuf:"bytes,19,opt,name=keyExpiration" json:"keyExpiration,omitempty"`
	MarkChatAsReadAction                                   *MarkChatAsReadAction                                   `protobuf:"bytes,20,opt,name=markChatAsReadAction" json:"markChatAsReadAction,omitempty"`
	ClearChatAction                                        *ClearChatAction                                        `protobuf:"bytes,21,opt,name=clearChatAction" json:"clearChatAction,omitempty"`
	DeleteChatAction                                       *DeleteChatAction                                       `protobuf:"bytes,22,opt,name=deleteChatAction" json:"deleteChatAction,omitempty"`
	UnarchiveChatsSetting                                  *UnarchiveChatsSetting                                  `protobuf:"bytes,23,opt,name=unarchiveChatsSetting" json:"unarchiveChatsSetting,omitempty"`
	PrimaryFeature                                         *PrimaryFeature                                         `protobuf:"bytes,24,opt,name=primaryFeature" json:"primaryFeature,omitempty"`
	AndroidUnsupportedActions                              *AndroidUnsupportedActions                              `protobuf:"bytes,26,opt,name=androidUnsupportedActions" json:"androidUnsupportedActions,omitempty"`
	AgentAction                                            *AgentAction                                            `protobuf:"bytes,27,opt,name=agentAction" json:"agentAction,omitempty"`
	SubscriptionAction                                     *SubscriptionAction                                     `protobuf:"bytes,28,opt,name=subscriptionAction" json:"subscriptionAction,omitempty"`
	UserStatusMuteAction                                   *UserStatusMuteAction                                   `protobuf:"bytes,29,opt,name=userStatusMuteAction" json:"userStatusMuteAction,omitempty"`
	TimeFormatAction                                       *TimeFormatAction                                       `protobuf:"bytes,30,opt,name=timeFormatAction" json:"timeFormatAction,omitempty"`
	NuxAction                                              *NuxAction                                              `protobuf:"bytes,31,opt,name=nuxAction" json:"nuxAction,omitempty"`
	PrimaryVersionAction                                   *PrimaryVersionAction                                   `protobuf:"bytes,32,opt,name=primaryVersionAction" json:"primaryVersionAction,omitempty"`
	StickerAction                                          *StickerAction                                          `protobuf:"bytes,33,opt,name=stickerAction" json:"stickerAction,omitempty"`
	RemoveRecentStickerAction                              *RemoveRecentStickerAction                              `protobuf:"bytes,34,opt,name=removeRecentStickerAction" json:"removeRecentStickerAction,omitempty"`
	ChatAssignment                                         *ChatAssignmentAction                                   `protobuf:"bytes,35,opt,name=chatAssignment" json:"chatAssignment,omitempty"`
	ChatAssignmentOpenedStatus                             *ChatAssignmentOpenedStatusAction                       `protobuf:"bytes,36,opt,name=chatAssignmentOpenedStatus" json:"chatAssignmentOpenedStatus,omitempty"`
	PnForLidChatAction                                     *PnForLidChatAction                                     `protobuf:"bytes,37,opt,name=pnForLidChatAction" json:"pnForLidChatAction,omitempty"`
	MarketingMessageAction                                 *MarketingMessageAction                                 `protobuf:"bytes,38,opt,name=marketingMessageAction" json:"marketingMessageAction,omitempty"`
	MarketingMessageBroadcastAction                        *MarketingMessageBroadcastAction                        `protobuf:"bytes,39,opt,name=marketingMessageBroadcastAction" json:"marketingMessageBroadcastAction,omitempty"`
	ExternalWebBetaAction                                  *ExternalWebBetaAction                                  `protobuf:"bytes,40,opt,name=externalWebBetaAction" json:"externalWebBetaAction,omitempty"`
	PrivacySettingRelayAllCalls                            *PrivacySettingRelayAllCalls                            `protobuf:"bytes,41,opt,name=privacySettingRelayAllCalls" json:"privacySettingRelayAllCalls,omitempty"`
	CallLogAction                                          *CallLogAction                                          `protobuf:"bytes,42,opt,name=callLogAction" json:"callLogAction,omitempty"`
	StatusPrivacy                                          *StatusPrivacyAction                                    `protobuf:"bytes,44,opt,name=statusPrivacy" json:"statusPrivacy,omitempty"`
	BotWelcomeRequestAction                                *BotWelcomeRequestAction                                `protobuf:"bytes,45,opt,name=botWelcomeRequestAction" json:"botWelcomeRequestAction,omitempty"`
	DeleteIndividualCallLog                                *DeleteIndividualCallLogAction                          `protobuf:"bytes,46,opt,name=deleteIndividualCallLog" json:"deleteIndividualCallLog,omitempty"`
	LabelReorderingAction                                  *LabelReorderingAction                                  `protobuf:"bytes,47,opt,name=labelReorderingAction" json:"labelReorderingAction,omitempty"`
	PaymentInfoAction                                      *PaymentInfoAction                                      `protobuf:"bytes,48,opt,name=paymentInfoAction" json:"paymentInfoAction,omitempty"`
	CustomPaymentMethodsAction                             *CustomPaymentMethodsAction                             `protobuf:"bytes,49,opt,name=customPaymentMethodsAction" json:"customPaymentMethodsAction,omitempty"`
	LockChatAction                                         *LockChatAction                                         `protobuf:"bytes,50,opt,name=lockChatAction" json:"lockChatAction,omitempty"`
	ChatLockSettings                                       *waChatLockSettings.ChatLockSettings                    `protobuf:"bytes,51,opt,name=chatLockSettings" json:"chatLockSettings,omitempty"`
	WamoUserIdentifierAction                               *WamoUserIdentifierAction                               `protobuf:"bytes,52,opt,name=wamoUserIdentifierAction" json:"wamoUserIdentifierAction,omitempty"`
	PrivacySettingDisableLinkPreviewsAction                *PrivacySettingDisableLinkPreviewsAction                `protobuf:"bytes,53,opt,name=privacySettingDisableLinkPreviewsAction" json:"privacySettingDisableLinkPreviewsAction,omitempty"`
	DeviceCapabilities                                     *waDeviceCapabilities.DeviceCapabilities                `protobuf:"bytes,54,opt,name=deviceCapabilities" json:"deviceCapabilities,omitempty"`
	NoteEditAction                                         *NoteEditAction                                         `protobuf:"bytes,55,opt,name=noteEditAction" json:"noteEditAction,omitempty"`
	FavoritesAction                                        *FavoritesAction                                        `protobuf:"bytes,56,opt,name=favoritesAction" json:"favoritesAction,omitempty"`
	MerchantPaymentPartnerAction                           *MerchantPaymentPartnerAction                           `protobuf:"bytes,57,opt,name=merchantPaymentPartnerAction" json:"merchantPaymentPartnerAction,omitempty"`
	WaffleAccountLinkStateAction                           *WaffleAccountLinkStateAction                           `protobuf:"bytes,58,opt,name=waffleAccountLinkStateAction" json:"waffleAccountLinkStateAction,omitempty"`
	UsernameChatStartMode                                  *UsernameChatStartModeAction                            `protobuf:"bytes,59,opt,name=usernameChatStartMode" json:"usernameChatStartMode,omitempty"`
	NotificationActivitySettingAction                      *NotificationActivitySettingAction                      `protobuf:"bytes,60,opt,name=notificationActivitySettingAction" json:"notificationActivitySettingAction,omitempty"`
	LidContactAction                                       *LidContactAction                                       `protobuf:"bytes,61,opt,name=lidContactAction" json:"lidContactAction,omitempty"`
	CtwaPerCustomerDataSharingAction                       *CtwaPerCustomerDataSharingAction                       `protobuf:"bytes,62,opt,name=ctwaPerCustomerDataSharingAction" json:"ctwaPerCustomerDataSharingAction,omitempty"`
	PaymentTosAction                                       *PaymentTosAction                                       `protobuf:"bytes,63,opt,name=paymentTosAction" json:"paymentTosAction,omitempty"`
	PrivacySettingChannelsPersonalisedRecommendationAction *PrivacySettingChannelsPersonalisedRecommendationAction `protobuf:"bytes,64,opt,name=privacySettingChannelsPersonalisedRecommendationAction" json:"privacySettingChannelsPersonalisedRecommendationAction,omitempty"`
	BusinessBroadcastAssociationAction                     *BusinessBroadcastAssociationAction                     `protobuf:"bytes,65,opt,name=businessBroadcastAssociationAction" json:"businessBroadcastAssociationAction,omitempty"`
	DetectedOutcomesStatusAction                           *DetectedOutcomesStatusAction                           `protobuf:"bytes,66,opt,name=detectedOutcomesStatusAction" json:"detectedOutcomesStatusAction,omitempty"`
	MaibaAiFeaturesControlAction                           *MaibaAIFeaturesControlAction                           `protobuf:"bytes,67,opt,name=maibaAiFeaturesControlAction" json:"maibaAiFeaturesControlAction,omitempty"`
	unknownFields                                          protoimpl.UnknownFields
	sizeCache                                              protoimpl.SizeCache
}

func (x *SyncActionValue) Reset() {
	*x = SyncActionValue{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncActionValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncActionValue) ProtoMessage() {}

func (x *SyncActionValue) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncActionValue.ProtoReflect.Descriptor instead.
func (*SyncActionValue) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{13}
}

func (x *SyncActionValue) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

func (x *SyncActionValue) GetStarAction() *StarAction {
	if x != nil {
		return x.StarAction
	}
	return nil
}

func (x *SyncActionValue) GetContactAction() *ContactAction {
	if x != nil {
		return x.ContactAction
	}
	return nil
}

func (x *SyncActionValue) GetMuteAction() *MuteAction {
	if x != nil {
		return x.MuteAction
	}
	return nil
}

func (x *SyncActionValue) GetPinAction() *PinAction {
	if x != nil {
		return x.PinAction
	}
	return nil
}

func (x *SyncActionValue) GetSecurityNotificationSetting() *SecurityNotificationSetting {
	if x != nil {
		return x.SecurityNotificationSetting
	}
	return nil
}

func (x *SyncActionValue) GetPushNameSetting() *PushNameSetting {
	if x != nil {
		return x.PushNameSetting
	}
	return nil
}

func (x *SyncActionValue) GetQuickReplyAction() *QuickReplyAction {
	if x != nil {
		return x.QuickReplyAction
	}
	return nil
}

func (x *SyncActionValue) GetRecentEmojiWeightsAction() *RecentEmojiWeightsAction {
	if x != nil {
		return x.RecentEmojiWeightsAction
	}
	return nil
}

func (x *SyncActionValue) GetLabelEditAction() *LabelEditAction {
	if x != nil {
		return x.LabelEditAction
	}
	return nil
}

func (x *SyncActionValue) GetLabelAssociationAction() *LabelAssociationAction {
	if x != nil {
		return x.LabelAssociationAction
	}
	return nil
}

func (x *SyncActionValue) GetLocaleSetting() *LocaleSetting {
	if x != nil {
		return x.LocaleSetting
	}
	return nil
}

func (x *SyncActionValue) GetArchiveChatAction() *ArchiveChatAction {
	if x != nil {
		return x.ArchiveChatAction
	}
	return nil
}

func (x *SyncActionValue) GetDeleteMessageForMeAction() *DeleteMessageForMeAction {
	if x != nil {
		return x.DeleteMessageForMeAction
	}
	return nil
}

func (x *SyncActionValue) GetKeyExpiration() *KeyExpiration {
	if x != nil {
		return x.KeyExpiration
	}
	return nil
}

func (x *SyncActionValue) GetMarkChatAsReadAction() *MarkChatAsReadAction {
	if x != nil {
		return x.MarkChatAsReadAction
	}
	return nil
}

func (x *SyncActionValue) GetClearChatAction() *ClearChatAction {
	if x != nil {
		return x.ClearChatAction
	}
	return nil
}

func (x *SyncActionValue) GetDeleteChatAction() *DeleteChatAction {
	if x != nil {
		return x.DeleteChatAction
	}
	return nil
}

func (x *SyncActionValue) GetUnarchiveChatsSetting() *UnarchiveChatsSetting {
	if x != nil {
		return x.UnarchiveChatsSetting
	}
	return nil
}

func (x *SyncActionValue) GetPrimaryFeature() *PrimaryFeature {
	if x != nil {
		return x.PrimaryFeature
	}
	return nil
}

func (x *SyncActionValue) GetAndroidUnsupportedActions() *AndroidUnsupportedActions {
	if x != nil {
		return x.AndroidUnsupportedActions
	}
	return nil
}

func (x *SyncActionValue) GetAgentAction() *AgentAction {
	if x != nil {
		return x.AgentAction
	}
	return nil
}

func (x *SyncActionValue) GetSubscriptionAction() *SubscriptionAction {
	if x != nil {
		return x.SubscriptionAction
	}
	return nil
}

func (x *SyncActionValue) GetUserStatusMuteAction() *UserStatusMuteAction {
	if x != nil {
		return x.UserStatusMuteAction
	}
	return nil
}

func (x *SyncActionValue) GetTimeFormatAction() *TimeFormatAction {
	if x != nil {
		return x.TimeFormatAction
	}
	return nil
}

func (x *SyncActionValue) GetNuxAction() *NuxAction {
	if x != nil {
		return x.NuxAction
	}
	return nil
}

func (x *SyncActionValue) GetPrimaryVersionAction() *PrimaryVersionAction {
	if x != nil {
		return x.PrimaryVersionAction
	}
	return nil
}

func (x *SyncActionValue) GetStickerAction() *StickerAction {
	if x != nil {
		return x.StickerAction
	}
	return nil
}

func (x *SyncActionValue) GetRemoveRecentStickerAction() *RemoveRecentStickerAction {
	if x != nil {
		return x.RemoveRecentStickerAction
	}
	return nil
}

func (x *SyncActionValue) GetChatAssignment() *ChatAssignmentAction {
	if x != nil {
		return x.ChatAssignment
	}
	return nil
}

func (x *SyncActionValue) GetChatAssignmentOpenedStatus() *ChatAssignmentOpenedStatusAction {
	if x != nil {
		return x.ChatAssignmentOpenedStatus
	}
	return nil
}

func (x *SyncActionValue) GetPnForLidChatAction() *PnForLidChatAction {
	if x != nil {
		return x.PnForLidChatAction
	}
	return nil
}

func (x *SyncActionValue) GetMarketingMessageAction() *MarketingMessageAction {
	if x != nil {
		return x.MarketingMessageAction
	}
	return nil
}

func (x *SyncActionValue) GetMarketingMessageBroadcastAction() *MarketingMessageBroadcastAction {
	if x != nil {
		return x.MarketingMessageBroadcastAction
	}
	return nil
}

func (x *SyncActionValue) GetExternalWebBetaAction() *ExternalWebBetaAction {
	if x != nil {
		return x.ExternalWebBetaAction
	}
	return nil
}

func (x *SyncActionValue) GetPrivacySettingRelayAllCalls() *PrivacySettingRelayAllCalls {
	if x != nil {
		return x.PrivacySettingRelayAllCalls
	}
	return nil
}

func (x *SyncActionValue) GetCallLogAction() *CallLogAction {
	if x != nil {
		return x.CallLogAction
	}
	return nil
}

func (x *SyncActionValue) GetStatusPrivacy() *StatusPrivacyAction {
	if x != nil {
		return x.StatusPrivacy
	}
	return nil
}

func (x *SyncActionValue) GetBotWelcomeRequestAction() *BotWelcomeRequestAction {
	if x != nil {
		return x.BotWelcomeRequestAction
	}
	return nil
}

func (x *SyncActionValue) GetDeleteIndividualCallLog() *DeleteIndividualCallLogAction {
	if x != nil {
		return x.DeleteIndividualCallLog
	}
	return nil
}

func (x *SyncActionValue) GetLabelReorderingAction() *LabelReorderingAction {
	if x != nil {
		return x.LabelReorderingAction
	}
	return nil
}

func (x *SyncActionValue) GetPaymentInfoAction() *PaymentInfoAction {
	if x != nil {
		return x.PaymentInfoAction
	}
	return nil
}

func (x *SyncActionValue) GetCustomPaymentMethodsAction() *CustomPaymentMethodsAction {
	if x != nil {
		return x.CustomPaymentMethodsAction
	}
	return nil
}

func (x *SyncActionValue) GetLockChatAction() *LockChatAction {
	if x != nil {
		return x.LockChatAction
	}
	return nil
}

func (x *SyncActionValue) GetChatLockSettings() *waChatLockSettings.ChatLockSettings {
	if x != nil {
		return x.ChatLockSettings
	}
	return nil
}

func (x *SyncActionValue) GetWamoUserIdentifierAction() *WamoUserIdentifierAction {
	if x != nil {
		return x.WamoUserIdentifierAction
	}
	return nil
}

func (x *SyncActionValue) GetPrivacySettingDisableLinkPreviewsAction() *PrivacySettingDisableLinkPreviewsAction {
	if x != nil {
		return x.PrivacySettingDisableLinkPreviewsAction
	}
	return nil
}

func (x *SyncActionValue) GetDeviceCapabilities() *waDeviceCapabilities.DeviceCapabilities {
	if x != nil {
		return x.DeviceCapabilities
	}
	return nil
}

func (x *SyncActionValue) GetNoteEditAction() *NoteEditAction {
	if x != nil {
		return x.NoteEditAction
	}
	return nil
}

func (x *SyncActionValue) GetFavoritesAction() *FavoritesAction {
	if x != nil {
		return x.FavoritesAction
	}
	return nil
}

func (x *SyncActionValue) GetMerchantPaymentPartnerAction() *MerchantPaymentPartnerAction {
	if x != nil {
		return x.MerchantPaymentPartnerAction
	}
	return nil
}

func (x *SyncActionValue) GetWaffleAccountLinkStateAction() *WaffleAccountLinkStateAction {
	if x != nil {
		return x.WaffleAccountLinkStateAction
	}
	return nil
}

func (x *SyncActionValue) GetUsernameChatStartMode() *UsernameChatStartModeAction {
	if x != nil {
		return x.UsernameChatStartMode
	}
	return nil
}

func (x *SyncActionValue) GetNotificationActivitySettingAction() *NotificationActivitySettingAction {
	if x != nil {
		return x.NotificationActivitySettingAction
	}
	return nil
}

func (x *SyncActionValue) GetLidContactAction() *LidContactAction {
	if x != nil {
		return x.LidContactAction
	}
	return nil
}

func (x *SyncActionValue) GetCtwaPerCustomerDataSharingAction() *CtwaPerCustomerDataSharingAction {
	if x != nil {
		return x.CtwaPerCustomerDataSharingAction
	}
	return nil
}

func (x *SyncActionValue) GetPaymentTosAction() *PaymentTosAction {
	if x != nil {
		return x.PaymentTosAction
	}
	return nil
}

func (x *SyncActionValue) GetPrivacySettingChannelsPersonalisedRecommendationAction() *PrivacySettingChannelsPersonalisedRecommendationAction {
	if x != nil {
		return x.PrivacySettingChannelsPersonalisedRecommendationAction
	}
	return nil
}

func (x *SyncActionValue) GetBusinessBroadcastAssociationAction() *BusinessBroadcastAssociationAction {
	if x != nil {
		return x.BusinessBroadcastAssociationAction
	}
	return nil
}

func (x *SyncActionValue) GetDetectedOutcomesStatusAction() *DetectedOutcomesStatusAction {
	if x != nil {
		return x.DetectedOutcomesStatusAction
	}
	return nil
}

func (x *SyncActionValue) GetMaibaAiFeaturesControlAction() *MaibaAIFeaturesControlAction {
	if x != nil {
		return x.MaibaAiFeaturesControlAction
	}
	return nil
}

type BusinessBroadcastAssociationAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Deleted       *bool                  `protobuf:"varint,1,opt,name=deleted" json:"deleted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BusinessBroadcastAssociationAction) Reset() {
	*x = BusinessBroadcastAssociationAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BusinessBroadcastAssociationAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusinessBroadcastAssociationAction) ProtoMessage() {}

func (x *BusinessBroadcastAssociationAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusinessBroadcastAssociationAction.ProtoReflect.Descriptor instead.
func (*BusinessBroadcastAssociationAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{14}
}

func (x *BusinessBroadcastAssociationAction) GetDeleted() bool {
	if x != nil && x.Deleted != nil {
		return *x.Deleted
	}
	return false
}

type CtwaPerCustomerDataSharingAction struct {
	state                               protoimpl.MessageState `protogen:"open.v1"`
	IsCtwaPerCustomerDataSharingEnabled *bool                  `protobuf:"varint,1,opt,name=isCtwaPerCustomerDataSharingEnabled" json:"isCtwaPerCustomerDataSharingEnabled,omitempty"`
	unknownFields                       protoimpl.UnknownFields
	sizeCache                           protoimpl.SizeCache
}

func (x *CtwaPerCustomerDataSharingAction) Reset() {
	*x = CtwaPerCustomerDataSharingAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CtwaPerCustomerDataSharingAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CtwaPerCustomerDataSharingAction) ProtoMessage() {}

func (x *CtwaPerCustomerDataSharingAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CtwaPerCustomerDataSharingAction.ProtoReflect.Descriptor instead.
func (*CtwaPerCustomerDataSharingAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{15}
}

func (x *CtwaPerCustomerDataSharingAction) GetIsCtwaPerCustomerDataSharingEnabled() bool {
	if x != nil && x.IsCtwaPerCustomerDataSharingEnabled != nil {
		return *x.IsCtwaPerCustomerDataSharingEnabled
	}
	return false
}

type LidContactAction struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	FullName                 *string                `protobuf:"bytes,1,opt,name=fullName" json:"fullName,omitempty"`
	FirstName                *string                `protobuf:"bytes,2,opt,name=firstName" json:"firstName,omitempty"`
	Username                 *string                `protobuf:"bytes,3,opt,name=username" json:"username,omitempty"`
	SaveOnPrimaryAddressbook *bool                  `protobuf:"varint,4,opt,name=saveOnPrimaryAddressbook" json:"saveOnPrimaryAddressbook,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *LidContactAction) Reset() {
	*x = LidContactAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LidContactAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LidContactAction) ProtoMessage() {}

func (x *LidContactAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LidContactAction.ProtoReflect.Descriptor instead.
func (*LidContactAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{16}
}

func (x *LidContactAction) GetFullName() string {
	if x != nil && x.FullName != nil {
		return *x.FullName
	}
	return ""
}

func (x *LidContactAction) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *LidContactAction) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *LidContactAction) GetSaveOnPrimaryAddressbook() bool {
	if x != nil && x.SaveOnPrimaryAddressbook != nil {
		return *x.SaveOnPrimaryAddressbook
	}
	return false
}

type FavoritesAction struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Favorites     []*FavoritesAction_Favorite `protobuf:"bytes,1,rep,name=favorites" json:"favorites,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FavoritesAction) Reset() {
	*x = FavoritesAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FavoritesAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FavoritesAction) ProtoMessage() {}

func (x *FavoritesAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FavoritesAction.ProtoReflect.Descriptor instead.
func (*FavoritesAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{17}
}

func (x *FavoritesAction) GetFavorites() []*FavoritesAction_Favorite {
	if x != nil {
		return x.Favorites
	}
	return nil
}

type PrivacySettingChannelsPersonalisedRecommendationAction struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IsUserOptedOut *bool                  `protobuf:"varint,1,opt,name=isUserOptedOut" json:"isUserOptedOut,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PrivacySettingChannelsPersonalisedRecommendationAction) Reset() {
	*x = PrivacySettingChannelsPersonalisedRecommendationAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrivacySettingChannelsPersonalisedRecommendationAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivacySettingChannelsPersonalisedRecommendationAction) ProtoMessage() {}

func (x *PrivacySettingChannelsPersonalisedRecommendationAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivacySettingChannelsPersonalisedRecommendationAction.ProtoReflect.Descriptor instead.
func (*PrivacySettingChannelsPersonalisedRecommendationAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{18}
}

func (x *PrivacySettingChannelsPersonalisedRecommendationAction) GetIsUserOptedOut() bool {
	if x != nil && x.IsUserOptedOut != nil {
		return *x.IsUserOptedOut
	}
	return false
}

type PrivacySettingDisableLinkPreviewsAction struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	IsPreviewsDisabled *bool                  `protobuf:"varint,1,opt,name=isPreviewsDisabled" json:"isPreviewsDisabled,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PrivacySettingDisableLinkPreviewsAction) Reset() {
	*x = PrivacySettingDisableLinkPreviewsAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrivacySettingDisableLinkPreviewsAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivacySettingDisableLinkPreviewsAction) ProtoMessage() {}

func (x *PrivacySettingDisableLinkPreviewsAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivacySettingDisableLinkPreviewsAction.ProtoReflect.Descriptor instead.
func (*PrivacySettingDisableLinkPreviewsAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{19}
}

func (x *PrivacySettingDisableLinkPreviewsAction) GetIsPreviewsDisabled() bool {
	if x != nil && x.IsPreviewsDisabled != nil {
		return *x.IsPreviewsDisabled
	}
	return false
}

type WamoUserIdentifierAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Identifier    *string                `protobuf:"bytes,1,opt,name=identifier" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WamoUserIdentifierAction) Reset() {
	*x = WamoUserIdentifierAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WamoUserIdentifierAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WamoUserIdentifierAction) ProtoMessage() {}

func (x *WamoUserIdentifierAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WamoUserIdentifierAction.ProtoReflect.Descriptor instead.
func (*WamoUserIdentifierAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{20}
}

func (x *WamoUserIdentifierAction) GetIdentifier() string {
	if x != nil && x.Identifier != nil {
		return *x.Identifier
	}
	return ""
}

type LockChatAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Locked        *bool                  `protobuf:"varint,1,opt,name=locked" json:"locked,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LockChatAction) Reset() {
	*x = LockChatAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LockChatAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LockChatAction) ProtoMessage() {}

func (x *LockChatAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LockChatAction.ProtoReflect.Descriptor instead.
func (*LockChatAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{21}
}

func (x *LockChatAction) GetLocked() bool {
	if x != nil && x.Locked != nil {
		return *x.Locked
	}
	return false
}

type CustomPaymentMethodsAction struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	CustomPaymentMethods []*CustomPaymentMethod `protobuf:"bytes,1,rep,name=customPaymentMethods" json:"customPaymentMethods,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CustomPaymentMethodsAction) Reset() {
	*x = CustomPaymentMethodsAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomPaymentMethodsAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomPaymentMethodsAction) ProtoMessage() {}

func (x *CustomPaymentMethodsAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomPaymentMethodsAction.ProtoReflect.Descriptor instead.
func (*CustomPaymentMethodsAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{22}
}

func (x *CustomPaymentMethodsAction) GetCustomPaymentMethods() []*CustomPaymentMethod {
	if x != nil {
		return x.CustomPaymentMethods
	}
	return nil
}

type CustomPaymentMethod struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	CredentialID  *string                        `protobuf:"bytes,1,req,name=credentialID" json:"credentialID,omitempty"`
	Country       *string                        `protobuf:"bytes,2,req,name=country" json:"country,omitempty"`
	Type          *string                        `protobuf:"bytes,3,req,name=type" json:"type,omitempty"`
	Metadata      []*CustomPaymentMethodMetadata `protobuf:"bytes,4,rep,name=metadata" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomPaymentMethod) Reset() {
	*x = CustomPaymentMethod{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomPaymentMethod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomPaymentMethod) ProtoMessage() {}

func (x *CustomPaymentMethod) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomPaymentMethod.ProtoReflect.Descriptor instead.
func (*CustomPaymentMethod) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{23}
}

func (x *CustomPaymentMethod) GetCredentialID() string {
	if x != nil && x.CredentialID != nil {
		return *x.CredentialID
	}
	return ""
}

func (x *CustomPaymentMethod) GetCountry() string {
	if x != nil && x.Country != nil {
		return *x.Country
	}
	return ""
}

func (x *CustomPaymentMethod) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

func (x *CustomPaymentMethod) GetMetadata() []*CustomPaymentMethodMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type CustomPaymentMethodMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           *string                `protobuf:"bytes,1,req,name=key" json:"key,omitempty"`
	Value         *string                `protobuf:"bytes,2,req,name=value" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomPaymentMethodMetadata) Reset() {
	*x = CustomPaymentMethodMetadata{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomPaymentMethodMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomPaymentMethodMetadata) ProtoMessage() {}

func (x *CustomPaymentMethodMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomPaymentMethodMetadata.ProtoReflect.Descriptor instead.
func (*CustomPaymentMethodMetadata) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{24}
}

func (x *CustomPaymentMethodMetadata) GetKey() string {
	if x != nil && x.Key != nil {
		return *x.Key
	}
	return ""
}

func (x *CustomPaymentMethodMetadata) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

type PaymentInfoAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Cpi           *string                `protobuf:"bytes,1,opt,name=cpi" json:"cpi,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaymentInfoAction) Reset() {
	*x = PaymentInfoAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentInfoAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentInfoAction) ProtoMessage() {}

func (x *PaymentInfoAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentInfoAction.ProtoReflect.Descriptor instead.
func (*PaymentInfoAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{25}
}

func (x *PaymentInfoAction) GetCpi() string {
	if x != nil && x.Cpi != nil {
		return *x.Cpi
	}
	return ""
}

type LabelReorderingAction struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	SortedLabelIDs []int32                `protobuf:"varint,1,rep,name=sortedLabelIDs" json:"sortedLabelIDs,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *LabelReorderingAction) Reset() {
	*x = LabelReorderingAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelReorderingAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelReorderingAction) ProtoMessage() {}

func (x *LabelReorderingAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelReorderingAction.ProtoReflect.Descriptor instead.
func (*LabelReorderingAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{26}
}

func (x *LabelReorderingAction) GetSortedLabelIDs() []int32 {
	if x != nil {
		return x.SortedLabelIDs
	}
	return nil
}

type DeleteIndividualCallLogAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PeerJID       *string                `protobuf:"bytes,1,opt,name=peerJID" json:"peerJID,omitempty"`
	IsIncoming    *bool                  `protobuf:"varint,2,opt,name=isIncoming" json:"isIncoming,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIndividualCallLogAction) Reset() {
	*x = DeleteIndividualCallLogAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIndividualCallLogAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIndividualCallLogAction) ProtoMessage() {}

func (x *DeleteIndividualCallLogAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIndividualCallLogAction.ProtoReflect.Descriptor instead.
func (*DeleteIndividualCallLogAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{27}
}

func (x *DeleteIndividualCallLogAction) GetPeerJID() string {
	if x != nil && x.PeerJID != nil {
		return *x.PeerJID
	}
	return ""
}

func (x *DeleteIndividualCallLogAction) GetIsIncoming() bool {
	if x != nil && x.IsIncoming != nil {
		return *x.IsIncoming
	}
	return false
}

type BotWelcomeRequestAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsSent        *bool                  `protobuf:"varint,1,opt,name=isSent" json:"isSent,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BotWelcomeRequestAction) Reset() {
	*x = BotWelcomeRequestAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BotWelcomeRequestAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BotWelcomeRequestAction) ProtoMessage() {}

func (x *BotWelcomeRequestAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BotWelcomeRequestAction.ProtoReflect.Descriptor instead.
func (*BotWelcomeRequestAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{28}
}

func (x *BotWelcomeRequestAction) GetIsSent() bool {
	if x != nil && x.IsSent != nil {
		return *x.IsSent
	}
	return false
}

type CallLogAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CallLogRecord *CallLogRecord         `protobuf:"bytes,1,opt,name=callLogRecord" json:"callLogRecord,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CallLogAction) Reset() {
	*x = CallLogAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallLogAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallLogAction) ProtoMessage() {}

func (x *CallLogAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallLogAction.ProtoReflect.Descriptor instead.
func (*CallLogAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{29}
}

func (x *CallLogAction) GetCallLogRecord() *CallLogRecord {
	if x != nil {
		return x.CallLogRecord
	}
	return nil
}

type PrivacySettingRelayAllCalls struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsEnabled     *bool                  `protobuf:"varint,1,opt,name=isEnabled" json:"isEnabled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrivacySettingRelayAllCalls) Reset() {
	*x = PrivacySettingRelayAllCalls{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrivacySettingRelayAllCalls) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivacySettingRelayAllCalls) ProtoMessage() {}

func (x *PrivacySettingRelayAllCalls) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivacySettingRelayAllCalls.ProtoReflect.Descriptor instead.
func (*PrivacySettingRelayAllCalls) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{30}
}

func (x *PrivacySettingRelayAllCalls) GetIsEnabled() bool {
	if x != nil && x.IsEnabled != nil {
		return *x.IsEnabled
	}
	return false
}

type DetectedOutcomesStatusAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsEnabled     *bool                  `protobuf:"varint,1,opt,name=isEnabled" json:"isEnabled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectedOutcomesStatusAction) Reset() {
	*x = DetectedOutcomesStatusAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectedOutcomesStatusAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectedOutcomesStatusAction) ProtoMessage() {}

func (x *DetectedOutcomesStatusAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectedOutcomesStatusAction.ProtoReflect.Descriptor instead.
func (*DetectedOutcomesStatusAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{31}
}

func (x *DetectedOutcomesStatusAction) GetIsEnabled() bool {
	if x != nil && x.IsEnabled != nil {
		return *x.IsEnabled
	}
	return false
}

type ExternalWebBetaAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsOptIn       *bool                  `protobuf:"varint,1,opt,name=isOptIn" json:"isOptIn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExternalWebBetaAction) Reset() {
	*x = ExternalWebBetaAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalWebBetaAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalWebBetaAction) ProtoMessage() {}

func (x *ExternalWebBetaAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalWebBetaAction.ProtoReflect.Descriptor instead.
func (*ExternalWebBetaAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{32}
}

func (x *ExternalWebBetaAction) GetIsOptIn() bool {
	if x != nil && x.IsOptIn != nil {
		return *x.IsOptIn
	}
	return false
}

type MarketingMessageBroadcastAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RepliedCount  *int32                 `protobuf:"varint,1,opt,name=repliedCount" json:"repliedCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarketingMessageBroadcastAction) Reset() {
	*x = MarketingMessageBroadcastAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarketingMessageBroadcastAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarketingMessageBroadcastAction) ProtoMessage() {}

func (x *MarketingMessageBroadcastAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarketingMessageBroadcastAction.ProtoReflect.Descriptor instead.
func (*MarketingMessageBroadcastAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{33}
}

func (x *MarketingMessageBroadcastAction) GetRepliedCount() int32 {
	if x != nil && x.RepliedCount != nil {
		return *x.RepliedCount
	}
	return 0
}

type PnForLidChatAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PnJID         *string                `protobuf:"bytes,1,opt,name=pnJID" json:"pnJID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PnForLidChatAction) Reset() {
	*x = PnForLidChatAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PnForLidChatAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PnForLidChatAction) ProtoMessage() {}

func (x *PnForLidChatAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PnForLidChatAction.ProtoReflect.Descriptor instead.
func (*PnForLidChatAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{34}
}

func (x *PnForLidChatAction) GetPnJID() string {
	if x != nil && x.PnJID != nil {
		return *x.PnJID
	}
	return ""
}

type ChatAssignmentOpenedStatusAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatOpened    *bool                  `protobuf:"varint,1,opt,name=chatOpened" json:"chatOpened,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatAssignmentOpenedStatusAction) Reset() {
	*x = ChatAssignmentOpenedStatusAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatAssignmentOpenedStatusAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatAssignmentOpenedStatusAction) ProtoMessage() {}

func (x *ChatAssignmentOpenedStatusAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatAssignmentOpenedStatusAction.ProtoReflect.Descriptor instead.
func (*ChatAssignmentOpenedStatusAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{35}
}

func (x *ChatAssignmentOpenedStatusAction) GetChatOpened() bool {
	if x != nil && x.ChatOpened != nil {
		return *x.ChatOpened
	}
	return false
}

type ChatAssignmentAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceAgentID *string                `protobuf:"bytes,1,opt,name=deviceAgentID" json:"deviceAgentID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatAssignmentAction) Reset() {
	*x = ChatAssignmentAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatAssignmentAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatAssignmentAction) ProtoMessage() {}

func (x *ChatAssignmentAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatAssignmentAction.ProtoReflect.Descriptor instead.
func (*ChatAssignmentAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{36}
}

func (x *ChatAssignmentAction) GetDeviceAgentID() string {
	if x != nil && x.DeviceAgentID != nil {
		return *x.DeviceAgentID
	}
	return ""
}

type StickerAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	URL           *string                `protobuf:"bytes,1,opt,name=URL" json:"URL,omitempty"`
	FileEncSHA256 []byte                 `protobuf:"bytes,2,opt,name=fileEncSHA256" json:"fileEncSHA256,omitempty"`
	MediaKey      []byte                 `protobuf:"bytes,3,opt,name=mediaKey" json:"mediaKey,omitempty"`
	Mimetype      *string                `protobuf:"bytes,4,opt,name=mimetype" json:"mimetype,omitempty"`
	Height        *uint32                `protobuf:"varint,5,opt,name=height" json:"height,omitempty"`
	Width         *uint32                `protobuf:"varint,6,opt,name=width" json:"width,omitempty"`
	DirectPath    *string                `protobuf:"bytes,7,opt,name=directPath" json:"directPath,omitempty"`
	FileLength    *uint64                `protobuf:"varint,8,opt,name=fileLength" json:"fileLength,omitempty"`
	IsFavorite    *bool                  `protobuf:"varint,9,opt,name=isFavorite" json:"isFavorite,omitempty"`
	DeviceIDHint  *uint32                `protobuf:"varint,10,opt,name=deviceIDHint" json:"deviceIDHint,omitempty"`
	IsLottie      *bool                  `protobuf:"varint,11,opt,name=isLottie" json:"isLottie,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StickerAction) Reset() {
	*x = StickerAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerAction) ProtoMessage() {}

func (x *StickerAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerAction.ProtoReflect.Descriptor instead.
func (*StickerAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{37}
}

func (x *StickerAction) GetURL() string {
	if x != nil && x.URL != nil {
		return *x.URL
	}
	return ""
}

func (x *StickerAction) GetFileEncSHA256() []byte {
	if x != nil {
		return x.FileEncSHA256
	}
	return nil
}

func (x *StickerAction) GetMediaKey() []byte {
	if x != nil {
		return x.MediaKey
	}
	return nil
}

func (x *StickerAction) GetMimetype() string {
	if x != nil && x.Mimetype != nil {
		return *x.Mimetype
	}
	return ""
}

func (x *StickerAction) GetHeight() uint32 {
	if x != nil && x.Height != nil {
		return *x.Height
	}
	return 0
}

func (x *StickerAction) GetWidth() uint32 {
	if x != nil && x.Width != nil {
		return *x.Width
	}
	return 0
}

func (x *StickerAction) GetDirectPath() string {
	if x != nil && x.DirectPath != nil {
		return *x.DirectPath
	}
	return ""
}

func (x *StickerAction) GetFileLength() uint64 {
	if x != nil && x.FileLength != nil {
		return *x.FileLength
	}
	return 0
}

func (x *StickerAction) GetIsFavorite() bool {
	if x != nil && x.IsFavorite != nil {
		return *x.IsFavorite
	}
	return false
}

func (x *StickerAction) GetDeviceIDHint() uint32 {
	if x != nil && x.DeviceIDHint != nil {
		return *x.DeviceIDHint
	}
	return 0
}

func (x *StickerAction) GetIsLottie() bool {
	if x != nil && x.IsLottie != nil {
		return *x.IsLottie
	}
	return false
}

type RemoveRecentStickerAction struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	LastStickerSentTS *int64                 `protobuf:"varint,1,opt,name=lastStickerSentTS" json:"lastStickerSentTS,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *RemoveRecentStickerAction) Reset() {
	*x = RemoveRecentStickerAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveRecentStickerAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRecentStickerAction) ProtoMessage() {}

func (x *RemoveRecentStickerAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRecentStickerAction.ProtoReflect.Descriptor instead.
func (*RemoveRecentStickerAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{38}
}

func (x *RemoveRecentStickerAction) GetLastStickerSentTS() int64 {
	if x != nil && x.LastStickerSentTS != nil {
		return *x.LastStickerSentTS
	}
	return 0
}

type PrimaryVersionAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Version       *string                `protobuf:"bytes,1,opt,name=version" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrimaryVersionAction) Reset() {
	*x = PrimaryVersionAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrimaryVersionAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrimaryVersionAction) ProtoMessage() {}

func (x *PrimaryVersionAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrimaryVersionAction.ProtoReflect.Descriptor instead.
func (*PrimaryVersionAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{39}
}

func (x *PrimaryVersionAction) GetVersion() string {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return ""
}

type NuxAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Acknowledged  *bool                  `protobuf:"varint,1,opt,name=acknowledged" json:"acknowledged,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NuxAction) Reset() {
	*x = NuxAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NuxAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NuxAction) ProtoMessage() {}

func (x *NuxAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NuxAction.ProtoReflect.Descriptor instead.
func (*NuxAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{40}
}

func (x *NuxAction) GetAcknowledged() bool {
	if x != nil && x.Acknowledged != nil {
		return *x.Acknowledged
	}
	return false
}

type TimeFormatAction struct {
	state                         protoimpl.MessageState `protogen:"open.v1"`
	IsTwentyFourHourFormatEnabled *bool                  `protobuf:"varint,1,opt,name=isTwentyFourHourFormatEnabled" json:"isTwentyFourHourFormatEnabled,omitempty"`
	unknownFields                 protoimpl.UnknownFields
	sizeCache                     protoimpl.SizeCache
}

func (x *TimeFormatAction) Reset() {
	*x = TimeFormatAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeFormatAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeFormatAction) ProtoMessage() {}

func (x *TimeFormatAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeFormatAction.ProtoReflect.Descriptor instead.
func (*TimeFormatAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{41}
}

func (x *TimeFormatAction) GetIsTwentyFourHourFormatEnabled() bool {
	if x != nil && x.IsTwentyFourHourFormatEnabled != nil {
		return *x.IsTwentyFourHourFormatEnabled
	}
	return false
}

type UserStatusMuteAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Muted         *bool                  `protobuf:"varint,1,opt,name=muted" json:"muted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserStatusMuteAction) Reset() {
	*x = UserStatusMuteAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserStatusMuteAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserStatusMuteAction) ProtoMessage() {}

func (x *UserStatusMuteAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserStatusMuteAction.ProtoReflect.Descriptor instead.
func (*UserStatusMuteAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{42}
}

func (x *UserStatusMuteAction) GetMuted() bool {
	if x != nil && x.Muted != nil {
		return *x.Muted
	}
	return false
}

type SubscriptionAction struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IsDeactivated  *bool                  `protobuf:"varint,1,opt,name=isDeactivated" json:"isDeactivated,omitempty"`
	IsAutoRenewing *bool                  `protobuf:"varint,2,opt,name=isAutoRenewing" json:"isAutoRenewing,omitempty"`
	ExpirationDate *int64                 `protobuf:"varint,3,opt,name=expirationDate" json:"expirationDate,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SubscriptionAction) Reset() {
	*x = SubscriptionAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriptionAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionAction) ProtoMessage() {}

func (x *SubscriptionAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionAction.ProtoReflect.Descriptor instead.
func (*SubscriptionAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{43}
}

func (x *SubscriptionAction) GetIsDeactivated() bool {
	if x != nil && x.IsDeactivated != nil {
		return *x.IsDeactivated
	}
	return false
}

func (x *SubscriptionAction) GetIsAutoRenewing() bool {
	if x != nil && x.IsAutoRenewing != nil {
		return *x.IsAutoRenewing
	}
	return false
}

func (x *SubscriptionAction) GetExpirationDate() int64 {
	if x != nil && x.ExpirationDate != nil {
		return *x.ExpirationDate
	}
	return 0
}

type AgentAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	DeviceID      *int32                 `protobuf:"varint,2,opt,name=deviceID" json:"deviceID,omitempty"`
	IsDeleted     *bool                  `protobuf:"varint,3,opt,name=isDeleted" json:"isDeleted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AgentAction) Reset() {
	*x = AgentAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentAction) ProtoMessage() {}

func (x *AgentAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentAction.ProtoReflect.Descriptor instead.
func (*AgentAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{44}
}

func (x *AgentAction) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *AgentAction) GetDeviceID() int32 {
	if x != nil && x.DeviceID != nil {
		return *x.DeviceID
	}
	return 0
}

func (x *AgentAction) GetIsDeleted() bool {
	if x != nil && x.IsDeleted != nil {
		return *x.IsDeleted
	}
	return false
}

type AndroidUnsupportedActions struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Allowed       *bool                  `protobuf:"varint,1,opt,name=allowed" json:"allowed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AndroidUnsupportedActions) Reset() {
	*x = AndroidUnsupportedActions{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AndroidUnsupportedActions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AndroidUnsupportedActions) ProtoMessage() {}

func (x *AndroidUnsupportedActions) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AndroidUnsupportedActions.ProtoReflect.Descriptor instead.
func (*AndroidUnsupportedActions) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{45}
}

func (x *AndroidUnsupportedActions) GetAllowed() bool {
	if x != nil && x.Allowed != nil {
		return *x.Allowed
	}
	return false
}

type PrimaryFeature struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Flags         []string               `protobuf:"bytes,1,rep,name=flags" json:"flags,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrimaryFeature) Reset() {
	*x = PrimaryFeature{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrimaryFeature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrimaryFeature) ProtoMessage() {}

func (x *PrimaryFeature) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrimaryFeature.ProtoReflect.Descriptor instead.
func (*PrimaryFeature) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{46}
}

func (x *PrimaryFeature) GetFlags() []string {
	if x != nil {
		return x.Flags
	}
	return nil
}

type KeyExpiration struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ExpiredKeyEpoch *int32                 `protobuf:"varint,1,opt,name=expiredKeyEpoch" json:"expiredKeyEpoch,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *KeyExpiration) Reset() {
	*x = KeyExpiration{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KeyExpiration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeyExpiration) ProtoMessage() {}

func (x *KeyExpiration) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeyExpiration.ProtoReflect.Descriptor instead.
func (*KeyExpiration) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{47}
}

func (x *KeyExpiration) GetExpiredKeyEpoch() int32 {
	if x != nil && x.ExpiredKeyEpoch != nil {
		return *x.ExpiredKeyEpoch
	}
	return 0
}

type SyncActionMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           *waCommon.MessageKey   `protobuf:"bytes,1,opt,name=key" json:"key,omitempty"`
	Timestamp     *int64                 `protobuf:"varint,2,opt,name=timestamp" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncActionMessage) Reset() {
	*x = SyncActionMessage{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncActionMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncActionMessage) ProtoMessage() {}

func (x *SyncActionMessage) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncActionMessage.ProtoReflect.Descriptor instead.
func (*SyncActionMessage) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{48}
}

func (x *SyncActionMessage) GetKey() *waCommon.MessageKey {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *SyncActionMessage) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

type SyncActionMessageRange struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	LastMessageTimestamp       *int64                 `protobuf:"varint,1,opt,name=lastMessageTimestamp" json:"lastMessageTimestamp,omitempty"`
	LastSystemMessageTimestamp *int64                 `protobuf:"varint,2,opt,name=lastSystemMessageTimestamp" json:"lastSystemMessageTimestamp,omitempty"`
	Messages                   []*SyncActionMessage   `protobuf:"bytes,3,rep,name=messages" json:"messages,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *SyncActionMessageRange) Reset() {
	*x = SyncActionMessageRange{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncActionMessageRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncActionMessageRange) ProtoMessage() {}

func (x *SyncActionMessageRange) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncActionMessageRange.ProtoReflect.Descriptor instead.
func (*SyncActionMessageRange) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{49}
}

func (x *SyncActionMessageRange) GetLastMessageTimestamp() int64 {
	if x != nil && x.LastMessageTimestamp != nil {
		return *x.LastMessageTimestamp
	}
	return 0
}

func (x *SyncActionMessageRange) GetLastSystemMessageTimestamp() int64 {
	if x != nil && x.LastSystemMessageTimestamp != nil {
		return *x.LastSystemMessageTimestamp
	}
	return 0
}

func (x *SyncActionMessageRange) GetMessages() []*SyncActionMessage {
	if x != nil {
		return x.Messages
	}
	return nil
}

type UnarchiveChatsSetting struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UnarchiveChats *bool                  `protobuf:"varint,1,opt,name=unarchiveChats" json:"unarchiveChats,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UnarchiveChatsSetting) Reset() {
	*x = UnarchiveChatsSetting{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnarchiveChatsSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnarchiveChatsSetting) ProtoMessage() {}

func (x *UnarchiveChatsSetting) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnarchiveChatsSetting.ProtoReflect.Descriptor instead.
func (*UnarchiveChatsSetting) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{50}
}

func (x *UnarchiveChatsSetting) GetUnarchiveChats() bool {
	if x != nil && x.UnarchiveChats != nil {
		return *x.UnarchiveChats
	}
	return false
}

type DeleteChatAction struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	MessageRange  *SyncActionMessageRange `protobuf:"bytes,1,opt,name=messageRange" json:"messageRange,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteChatAction) Reset() {
	*x = DeleteChatAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteChatAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteChatAction) ProtoMessage() {}

func (x *DeleteChatAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteChatAction.ProtoReflect.Descriptor instead.
func (*DeleteChatAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{51}
}

func (x *DeleteChatAction) GetMessageRange() *SyncActionMessageRange {
	if x != nil {
		return x.MessageRange
	}
	return nil
}

type ClearChatAction struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	MessageRange  *SyncActionMessageRange `protobuf:"bytes,1,opt,name=messageRange" json:"messageRange,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClearChatAction) Reset() {
	*x = ClearChatAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClearChatAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearChatAction) ProtoMessage() {}

func (x *ClearChatAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearChatAction.ProtoReflect.Descriptor instead.
func (*ClearChatAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{52}
}

func (x *ClearChatAction) GetMessageRange() *SyncActionMessageRange {
	if x != nil {
		return x.MessageRange
	}
	return nil
}

type MarkChatAsReadAction struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Read          *bool                   `protobuf:"varint,1,opt,name=read" json:"read,omitempty"`
	MessageRange  *SyncActionMessageRange `protobuf:"bytes,2,opt,name=messageRange" json:"messageRange,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkChatAsReadAction) Reset() {
	*x = MarkChatAsReadAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkChatAsReadAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkChatAsReadAction) ProtoMessage() {}

func (x *MarkChatAsReadAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkChatAsReadAction.ProtoReflect.Descriptor instead.
func (*MarkChatAsReadAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{53}
}

func (x *MarkChatAsReadAction) GetRead() bool {
	if x != nil && x.Read != nil {
		return *x.Read
	}
	return false
}

func (x *MarkChatAsReadAction) GetMessageRange() *SyncActionMessageRange {
	if x != nil {
		return x.MessageRange
	}
	return nil
}

type DeleteMessageForMeAction struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DeleteMedia      *bool                  `protobuf:"varint,1,opt,name=deleteMedia" json:"deleteMedia,omitempty"`
	MessageTimestamp *int64                 `protobuf:"varint,2,opt,name=messageTimestamp" json:"messageTimestamp,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *DeleteMessageForMeAction) Reset() {
	*x = DeleteMessageForMeAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMessageForMeAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMessageForMeAction) ProtoMessage() {}

func (x *DeleteMessageForMeAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMessageForMeAction.ProtoReflect.Descriptor instead.
func (*DeleteMessageForMeAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{54}
}

func (x *DeleteMessageForMeAction) GetDeleteMedia() bool {
	if x != nil && x.DeleteMedia != nil {
		return *x.DeleteMedia
	}
	return false
}

func (x *DeleteMessageForMeAction) GetMessageTimestamp() int64 {
	if x != nil && x.MessageTimestamp != nil {
		return *x.MessageTimestamp
	}
	return 0
}

type ArchiveChatAction struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Archived      *bool                   `protobuf:"varint,1,opt,name=archived" json:"archived,omitempty"`
	MessageRange  *SyncActionMessageRange `protobuf:"bytes,2,opt,name=messageRange" json:"messageRange,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArchiveChatAction) Reset() {
	*x = ArchiveChatAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveChatAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveChatAction) ProtoMessage() {}

func (x *ArchiveChatAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveChatAction.ProtoReflect.Descriptor instead.
func (*ArchiveChatAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{55}
}

func (x *ArchiveChatAction) GetArchived() bool {
	if x != nil && x.Archived != nil {
		return *x.Archived
	}
	return false
}

func (x *ArchiveChatAction) GetMessageRange() *SyncActionMessageRange {
	if x != nil {
		return x.MessageRange
	}
	return nil
}

type RecentEmojiWeightsAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Weights       []*RecentEmojiWeight   `protobuf:"bytes,1,rep,name=weights" json:"weights,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecentEmojiWeightsAction) Reset() {
	*x = RecentEmojiWeightsAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecentEmojiWeightsAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecentEmojiWeightsAction) ProtoMessage() {}

func (x *RecentEmojiWeightsAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecentEmojiWeightsAction.ProtoReflect.Descriptor instead.
func (*RecentEmojiWeightsAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{56}
}

func (x *RecentEmojiWeightsAction) GetWeights() []*RecentEmojiWeight {
	if x != nil {
		return x.Weights
	}
	return nil
}

type LabelAssociationAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Labeled       *bool                  `protobuf:"varint,1,opt,name=labeled" json:"labeled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LabelAssociationAction) Reset() {
	*x = LabelAssociationAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelAssociationAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelAssociationAction) ProtoMessage() {}

func (x *LabelAssociationAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelAssociationAction.ProtoReflect.Descriptor instead.
func (*LabelAssociationAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{57}
}

func (x *LabelAssociationAction) GetLabeled() bool {
	if x != nil && x.Labeled != nil {
		return *x.Labeled
	}
	return false
}

type QuickReplyAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Shortcut      *string                `protobuf:"bytes,1,opt,name=shortcut" json:"shortcut,omitempty"`
	Message       *string                `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	Keywords      []string               `protobuf:"bytes,3,rep,name=keywords" json:"keywords,omitempty"`
	Count         *int32                 `protobuf:"varint,4,opt,name=count" json:"count,omitempty"`
	Deleted       *bool                  `protobuf:"varint,5,opt,name=deleted" json:"deleted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QuickReplyAction) Reset() {
	*x = QuickReplyAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuickReplyAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuickReplyAction) ProtoMessage() {}

func (x *QuickReplyAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuickReplyAction.ProtoReflect.Descriptor instead.
func (*QuickReplyAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{58}
}

func (x *QuickReplyAction) GetShortcut() string {
	if x != nil && x.Shortcut != nil {
		return *x.Shortcut
	}
	return ""
}

func (x *QuickReplyAction) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}
	return ""
}

func (x *QuickReplyAction) GetKeywords() []string {
	if x != nil {
		return x.Keywords
	}
	return nil
}

func (x *QuickReplyAction) GetCount() int32 {
	if x != nil && x.Count != nil {
		return *x.Count
	}
	return 0
}

func (x *QuickReplyAction) GetDeleted() bool {
	if x != nil && x.Deleted != nil {
		return *x.Deleted
	}
	return false
}

type LocaleSetting struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Locale        *string                `protobuf:"bytes,1,opt,name=locale" json:"locale,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocaleSetting) Reset() {
	*x = LocaleSetting{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocaleSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocaleSetting) ProtoMessage() {}

func (x *LocaleSetting) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocaleSetting.ProtoReflect.Descriptor instead.
func (*LocaleSetting) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{59}
}

func (x *LocaleSetting) GetLocale() string {
	if x != nil && x.Locale != nil {
		return *x.Locale
	}
	return ""
}

type PushNameSetting struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          *string                `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushNameSetting) Reset() {
	*x = PushNameSetting{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushNameSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushNameSetting) ProtoMessage() {}

func (x *PushNameSetting) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushNameSetting.ProtoReflect.Descriptor instead.
func (*PushNameSetting) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{60}
}

func (x *PushNameSetting) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type SecurityNotificationSetting struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ShowNotification *bool                  `protobuf:"varint,1,opt,name=showNotification" json:"showNotification,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *SecurityNotificationSetting) Reset() {
	*x = SecurityNotificationSetting{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecurityNotificationSetting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityNotificationSetting) ProtoMessage() {}

func (x *SecurityNotificationSetting) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityNotificationSetting.ProtoReflect.Descriptor instead.
func (*SecurityNotificationSetting) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{61}
}

func (x *SecurityNotificationSetting) GetShowNotification() bool {
	if x != nil && x.ShowNotification != nil {
		return *x.ShowNotification
	}
	return false
}

type PinAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pinned        *bool                  `protobuf:"varint,1,opt,name=pinned" json:"pinned,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PinAction) Reset() {
	*x = PinAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PinAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinAction) ProtoMessage() {}

func (x *PinAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinAction.ProtoReflect.Descriptor instead.
func (*PinAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{62}
}

func (x *PinAction) GetPinned() bool {
	if x != nil && x.Pinned != nil {
		return *x.Pinned
	}
	return false
}

type MuteAction struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Muted            *bool                  `protobuf:"varint,1,opt,name=muted" json:"muted,omitempty"`
	MuteEndTimestamp *int64                 `protobuf:"varint,2,opt,name=muteEndTimestamp" json:"muteEndTimestamp,omitempty"`
	AutoMuted        *bool                  `protobuf:"varint,3,opt,name=autoMuted" json:"autoMuted,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *MuteAction) Reset() {
	*x = MuteAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MuteAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MuteAction) ProtoMessage() {}

func (x *MuteAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MuteAction.ProtoReflect.Descriptor instead.
func (*MuteAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{63}
}

func (x *MuteAction) GetMuted() bool {
	if x != nil && x.Muted != nil {
		return *x.Muted
	}
	return false
}

func (x *MuteAction) GetMuteEndTimestamp() int64 {
	if x != nil && x.MuteEndTimestamp != nil {
		return *x.MuteEndTimestamp
	}
	return 0
}

func (x *MuteAction) GetAutoMuted() bool {
	if x != nil && x.AutoMuted != nil {
		return *x.AutoMuted
	}
	return false
}

type ContactAction struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	FullName                 *string                `protobuf:"bytes,1,opt,name=fullName" json:"fullName,omitempty"`
	FirstName                *string                `protobuf:"bytes,2,opt,name=firstName" json:"firstName,omitempty"`
	LidJID                   *string                `protobuf:"bytes,3,opt,name=lidJID" json:"lidJID,omitempty"`
	SaveOnPrimaryAddressbook *bool                  `protobuf:"varint,4,opt,name=saveOnPrimaryAddressbook" json:"saveOnPrimaryAddressbook,omitempty"`
	PnJID                    *string                `protobuf:"bytes,5,opt,name=pnJID" json:"pnJID,omitempty"`
	Username                 *string                `protobuf:"bytes,6,opt,name=username" json:"username,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *ContactAction) Reset() {
	*x = ContactAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactAction) ProtoMessage() {}

func (x *ContactAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactAction.ProtoReflect.Descriptor instead.
func (*ContactAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{64}
}

func (x *ContactAction) GetFullName() string {
	if x != nil && x.FullName != nil {
		return *x.FullName
	}
	return ""
}

func (x *ContactAction) GetFirstName() string {
	if x != nil && x.FirstName != nil {
		return *x.FirstName
	}
	return ""
}

func (x *ContactAction) GetLidJID() string {
	if x != nil && x.LidJID != nil {
		return *x.LidJID
	}
	return ""
}

func (x *ContactAction) GetSaveOnPrimaryAddressbook() bool {
	if x != nil && x.SaveOnPrimaryAddressbook != nil {
		return *x.SaveOnPrimaryAddressbook
	}
	return false
}

func (x *ContactAction) GetPnJID() string {
	if x != nil && x.PnJID != nil {
		return *x.PnJID
	}
	return ""
}

func (x *ContactAction) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

type StarAction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Starred       *bool                  `protobuf:"varint,1,opt,name=starred" json:"starred,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StarAction) Reset() {
	*x = StarAction{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StarAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StarAction) ProtoMessage() {}

func (x *StarAction) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StarAction.ProtoReflect.Descriptor instead.
func (*StarAction) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{65}
}

func (x *StarAction) GetStarred() bool {
	if x != nil && x.Starred != nil {
		return *x.Starred
	}
	return false
}

type SyncActionData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Index         []byte                 `protobuf:"bytes,1,opt,name=index" json:"index,omitempty"`
	Value         *SyncActionValue       `protobuf:"bytes,2,opt,name=value" json:"value,omitempty"`
	Padding       []byte                 `protobuf:"bytes,3,opt,name=padding" json:"padding,omitempty"`
	Version       *int32                 `protobuf:"varint,4,opt,name=version" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncActionData) Reset() {
	*x = SyncActionData{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncActionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncActionData) ProtoMessage() {}

func (x *SyncActionData) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncActionData.ProtoReflect.Descriptor instead.
func (*SyncActionData) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{66}
}

func (x *SyncActionData) GetIndex() []byte {
	if x != nil {
		return x.Index
	}
	return nil
}

func (x *SyncActionData) GetValue() *SyncActionValue {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *SyncActionData) GetPadding() []byte {
	if x != nil {
		return x.Padding
	}
	return nil
}

func (x *SyncActionData) GetVersion() int32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

type CallLogRecord_ParticipantInfo struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	UserJID       *string                   `protobuf:"bytes,1,opt,name=userJID" json:"userJID,omitempty"`
	CallResult    *CallLogRecord_CallResult `protobuf:"varint,2,opt,name=callResult,enum=WASyncAction.CallLogRecord_CallResult" json:"callResult,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CallLogRecord_ParticipantInfo) Reset() {
	*x = CallLogRecord_ParticipantInfo{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallLogRecord_ParticipantInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallLogRecord_ParticipantInfo) ProtoMessage() {}

func (x *CallLogRecord_ParticipantInfo) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallLogRecord_ParticipantInfo.ProtoReflect.Descriptor instead.
func (*CallLogRecord_ParticipantInfo) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CallLogRecord_ParticipantInfo) GetUserJID() string {
	if x != nil && x.UserJID != nil {
		return *x.UserJID
	}
	return ""
}

func (x *CallLogRecord_ParticipantInfo) GetCallResult() CallLogRecord_CallResult {
	if x != nil && x.CallResult != nil {
		return *x.CallResult
	}
	return CallLogRecord_CONNECTED
}

type FavoritesAction_Favorite struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ID            *string                `protobuf:"bytes,1,opt,name=ID" json:"ID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FavoritesAction_Favorite) Reset() {
	*x = FavoritesAction_Favorite{}
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FavoritesAction_Favorite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FavoritesAction_Favorite) ProtoMessage() {}

func (x *FavoritesAction_Favorite) ProtoReflect() protoreflect.Message {
	mi := &file_waSyncAction_WASyncAction_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FavoritesAction_Favorite.ProtoReflect.Descriptor instead.
func (*FavoritesAction_Favorite) Descriptor() ([]byte, []int) {
	return file_waSyncAction_WASyncAction_proto_rawDescGZIP(), []int{17, 0}
}

func (x *FavoritesAction_Favorite) GetID() string {
	if x != nil && x.ID != nil {
		return *x.ID
	}
	return ""
}

var File_waSyncAction_WASyncAction_proto protoreflect.FileDescriptor

const file_waSyncAction_WASyncAction_proto_rawDesc = "" +
	"\n" +
	"\x1fwaSyncAction/WASyncAction.proto\x12\fWASyncAction\x1a4waChatLockSettings/WAProtobufsChatLockSettings.proto\x1a8waDeviceCapabilities/WAProtobufsDeviceCapabilities.proto\x1a\x17waCommon/WACommon.proto\"\xc5\b\n" +
	"\rCallLogRecord\x12F\n" +
	"\n" +
	"callResult\x18\x01 \x01(\x0e2&.WASyncAction.CallLogRecord.CallResultR\n" +
	"callResult\x12\x1c\n" +
	"\tisDndMode\x18\x02 \x01(\bR\tisDndMode\x12O\n" +
	"\rsilenceReason\x18\x03 \x01(\x0e2).WASyncAction.CallLogRecord.SilenceReasonR\rsilenceReason\x12\x1a\n" +
	"\bduration\x18\x04 \x01(\x03R\bduration\x12\x1c\n" +
	"\tstartTime\x18\x05 \x01(\x03R\tstartTime\x12\x1e\n" +
	"\n" +
	"isIncoming\x18\x06 \x01(\bR\n" +
	"isIncoming\x12\x18\n" +
	"\aisVideo\x18\a \x01(\bR\aisVideo\x12\x1e\n" +
	"\n" +
	"isCallLink\x18\b \x01(\bR\n" +
	"isCallLink\x12$\n" +
	"\rcallLinkToken\x18\t \x01(\tR\rcallLinkToken\x12(\n" +
	"\x0fscheduledCallID\x18\n" +
	" \x01(\tR\x0fscheduledCallID\x12\x16\n" +
	"\x06callID\x18\v \x01(\tR\x06callID\x12&\n" +
	"\x0ecallCreatorJID\x18\f \x01(\tR\x0ecallCreatorJID\x12\x1a\n" +
	"\bgroupJID\x18\r \x01(\tR\bgroupJID\x12O\n" +
	"\fparticipants\x18\x0e \x03(\v2+.WASyncAction.CallLogRecord.ParticipantInfoR\fparticipants\x12@\n" +
	"\bcallType\x18\x0f \x01(\x0e2$.WASyncAction.CallLogRecord.CallTypeR\bcallType\x1as\n" +
	"\x0fParticipantInfo\x12\x18\n" +
	"\auserJID\x18\x01 \x01(\tR\auserJID\x12F\n" +
	"\n" +
	"callResult\x18\x02 \x01(\x0e2&.WASyncAction.CallLogRecord.CallResultR\n" +
	"callResult\";\n" +
	"\bCallType\x12\v\n" +
	"\aREGULAR\x10\x00\x12\x12\n" +
	"\x0eSCHEDULED_CALL\x10\x01\x12\x0e\n" +
	"\n" +
	"VOICE_CHAT\x10\x02\"F\n" +
	"\rSilenceReason\x12\b\n" +
	"\x04NONE\x10\x00\x12\r\n" +
	"\tSCHEDULED\x10\x01\x12\v\n" +
	"\aPRIVACY\x10\x02\x12\x0f\n" +
	"\vLIGHTWEIGHT\x10\x03\"\xaf\x01\n" +
	"\n" +
	"CallResult\x12\r\n" +
	"\tCONNECTED\x10\x00\x12\f\n" +
	"\bREJECTED\x10\x01\x12\r\n" +
	"\tCANCELLED\x10\x02\x12\x15\n" +
	"\x11ACCEPTEDELSEWHERE\x10\x03\x12\n" +
	"\n" +
	"\x06MISSED\x10\x04\x12\v\n" +
	"\aINVALID\x10\x05\x12\x0f\n" +
	"\vUNAVAILABLE\x10\x06\x12\f\n" +
	"\bUPCOMING\x10\a\x12\n" +
	"\n" +
	"\x06FAILED\x10\b\x12\r\n" +
	"\tABANDONED\x10\t\x12\v\n" +
	"\aONGOING\x10\n" +
	"\"\xd6\x01\n" +
	"\x1cMaibaAIFeaturesControlAction\x12i\n" +
	"\x0faiFeatureStatus\x18\x01 \x01(\x0e2?.WASyncAction.MaibaAIFeaturesControlAction.MaibaAIFeatureStatusR\x0faiFeatureStatus\"K\n" +
	"\x14MaibaAIFeatureStatus\x12\v\n" +
	"\aENABLED\x10\x00\x12\x18\n" +
	"\x14ENABLED_HAS_LEARNING\x10\x01\x12\f\n" +
	"\bDISABLED\x10\x02\"\xae\x01\n" +
	"\x10PaymentTosAction\x12R\n" +
	"\rpaymentNotice\x18\x01 \x02(\x0e2,.WASyncAction.PaymentTosAction.PaymentNoticeR\rpaymentNotice\x12\x1a\n" +
	"\baccepted\x18\x02 \x02(\bR\baccepted\"*\n" +
	"\rPaymentNotice\x12\x19\n" +
	"\x15BR_PAY_PRIVACY_POLICY\x10\x00\"\xa6\x02\n" +
	"!NotificationActivitySettingAction\x12\x8d\x01\n" +
	"\x1bnotificationActivitySetting\x18\x01 \x01(\x0e2K.WASyncAction.NotificationActivitySettingAction.NotificationActivitySettingR\x1bnotificationActivitySetting\"q\n" +
	"\x1bNotificationActivitySetting\x12\x18\n" +
	"\x14DEFAULT_ALL_MESSAGES\x10\x00\x12\x10\n" +
	"\fALL_MESSAGES\x10\x01\x12\x0e\n" +
	"\n" +
	"HIGHLIGHTS\x10\x02\x12\x16\n" +
	"\x12DEFAULT_HIGHLIGHTS\x10\x03\"\x99\x01\n" +
	"\x1cWaffleAccountLinkStateAction\x12Y\n" +
	"\tlinkState\x18\x02 \x01(\x0e2;.WASyncAction.WaffleAccountLinkStateAction.AccountLinkStateR\tlinkState\"\x1e\n" +
	"\x10AccountLinkState\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x00\"\xed\x01\n" +
	"\x1cMerchantPaymentPartnerAction\x12I\n" +
	"\x06status\x18\x01 \x02(\x0e21.WASyncAction.MerchantPaymentPartnerAction.StatusR\x06status\x12\x18\n" +
	"\acountry\x18\x02 \x02(\tR\acountry\x12 \n" +
	"\vgatewayName\x18\x03 \x01(\tR\vgatewayName\x12\"\n" +
	"\fcredentialID\x18\x04 \x01(\tR\fcredentialID\"\"\n" +
	"\x06Status\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x00\x12\f\n" +
	"\bINACTIVE\x10\x01\"\xfd\x01\n" +
	"\x0eNoteEditAction\x129\n" +
	"\x04type\x18\x01 \x01(\x0e2%.WASyncAction.NoteEditAction.NoteTypeR\x04type\x12\x18\n" +
	"\achatJID\x18\x02 \x01(\tR\achatJID\x12\x1c\n" +
	"\tcreatedAt\x18\x03 \x01(\x03R\tcreatedAt\x12\x18\n" +
	"\adeleted\x18\x04 \x01(\bR\adeleted\x120\n" +
	"\x13unstructuredContent\x18\x05 \x01(\tR\x13unstructuredContent\",\n" +
	"\bNoteType\x12\x10\n" +
	"\fUNSTRUCTURED\x10\x01\x12\x0e\n" +
	"\n" +
	"STRUCTURED\x10\x02\"\xc4\x01\n" +
	"\x13StatusPrivacyAction\x12L\n" +
	"\x04mode\x18\x01 \x01(\x0e28.WASyncAction.StatusPrivacyAction.StatusDistributionModeR\x04mode\x12\x18\n" +
	"\auserJID\x18\x02 \x03(\tR\auserJID\"E\n" +
	"\x16StatusDistributionMode\x12\x0e\n" +
	"\n" +
	"ALLOW_LIST\x10\x00\x12\r\n" +
	"\tDENY_LIST\x10\x01\x12\f\n" +
	"\bCONTACTS\x10\x02\"\xc7\x02\n" +
	"\x16MarketingMessageAction\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12V\n" +
	"\x04type\x18\x03 \x01(\x0e2B.WASyncAction.MarketingMessageAction.MarketingMessagePrototypeTypeR\x04type\x12\x1c\n" +
	"\tcreatedAt\x18\x04 \x01(\x03R\tcreatedAt\x12\x1e\n" +
	"\n" +
	"lastSentAt\x18\x05 \x01(\x03R\n" +
	"lastSentAt\x12\x1c\n" +
	"\tisDeleted\x18\x06 \x01(\bR\tisDeleted\x12\x18\n" +
	"\amediaID\x18\a \x01(\tR\amediaID\"1\n" +
	"\x1dMarketingMessagePrototypeType\x12\x10\n" +
	"\fPERSONALIZED\x10\x00\"\x9e\x01\n" +
	"\x1bUsernameChatStartModeAction\x12]\n" +
	"\rchatStartMode\x18\x01 \x01(\x0e27.WASyncAction.UsernameChatStartModeAction.ChatStartModeR\rchatStartMode\" \n" +
	"\rChatStartMode\x12\a\n" +
	"\x03LID\x10\x01\x12\x06\n" +
	"\x02PN\x10\x02\"\x90\x03\n" +
	"\x0fLabelEditAction\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05color\x18\x02 \x01(\x05R\x05color\x12\"\n" +
	"\fpredefinedID\x18\x03 \x01(\x05R\fpredefinedID\x12\x18\n" +
	"\adeleted\x18\x04 \x01(\bR\adeleted\x12\x1e\n" +
	"\n" +
	"orderIndex\x18\x05 \x01(\x05R\n" +
	"orderIndex\x12\x1a\n" +
	"\bisActive\x18\x06 \x01(\bR\bisActive\x12:\n" +
	"\x04type\x18\a \x01(\x0e2&.WASyncAction.LabelEditAction.ListTypeR\x04type\x12 \n" +
	"\visImmutable\x18\b \x01(\bR\visImmutable\"{\n" +
	"\bListType\x12\b\n" +
	"\x04NONE\x10\x00\x12\n" +
	"\n" +
	"\x06UNREAD\x10\x01\x12\n" +
	"\n" +
	"\x06GROUPS\x10\x02\x12\r\n" +
	"\tFAVORITES\x10\x03\x12\x0e\n" +
	"\n" +
	"PREDEFINED\x10\x04\x12\n" +
	"\n" +
	"\x06CUSTOM\x10\x05\x12\r\n" +
	"\tCOMMUNITY\x10\x06\x12\x13\n" +
	"\x0fSERVER_ASSIGNED\x10\a\"\xf8\x04\n" +
	"\x0ePatchDebugData\x12$\n" +
	"\rcurrentLthash\x18\x01 \x01(\fR\rcurrentLthash\x12\x1c\n" +
	"\tnewLthash\x18\x02 \x01(\fR\tnewLthash\x12\"\n" +
	"\fpatchVersion\x18\x03 \x01(\fR\fpatchVersion\x12&\n" +
	"\x0ecollectionName\x18\x04 \x01(\fR\x0ecollectionName\x12X\n" +
	"'firstFourBytesFromAHashOfSnapshotMACKey\x18\x05 \x01(\fR'firstFourBytesFromAHashOfSnapshotMACKey\x12,\n" +
	"\x11newLthashSubtract\x18\x06 \x01(\fR\x11newLthashSubtract\x12\x1c\n" +
	"\tnumberAdd\x18\a \x01(\x05R\tnumberAdd\x12\"\n" +
	"\fnumberRemove\x18\b \x01(\x05R\fnumberRemove\x12&\n" +
	"\x0enumberOverride\x18\t \x01(\x05R\x0enumberOverride\x12M\n" +
	"\x0esenderPlatform\x18\n" +
	" \x01(\x0e2%.WASyncAction.PatchDebugData.PlatformR\x0esenderPlatform\x12(\n" +
	"\x0fisSenderPrimary\x18\v \x01(\bR\x0fisSenderPrimary\"k\n" +
	"\bPlatform\x12\v\n" +
	"\aANDROID\x10\x00\x12\b\n" +
	"\x04SMBA\x10\x01\x12\n" +
	"\n" +
	"\x06IPHONE\x10\x02\x12\b\n" +
	"\x04SMBI\x10\x03\x12\a\n" +
	"\x03WEB\x10\x04\x12\a\n" +
	"\x03UWP\x10\x05\x12\n" +
	"\n" +
	"\x06DARWIN\x10\x06\x12\b\n" +
	"\x04IPAD\x10\a\x12\n" +
	"\n" +
	"\x06WEAROS\x10\b\"A\n" +
	"\x11RecentEmojiWeight\x12\x14\n" +
	"\x05emoji\x18\x01 \x01(\tR\x05emoji\x12\x16\n" +
	"\x06weight\x18\x02 \x01(\x02R\x06weight\"\xd8*\n" +
	"\x0fSyncActionValue\x12\x1c\n" +
	"\ttimestamp\x18\x01 \x01(\x03R\ttimestamp\x128\n" +
	"\n" +
	"starAction\x18\x02 \x01(\v2\x18.WASyncAction.StarActionR\n" +
	"starAction\x12A\n" +
	"\rcontactAction\x18\x03 \x01(\v2\x1b.WASyncAction.ContactActionR\rcontactAction\x128\n" +
	"\n" +
	"muteAction\x18\x04 \x01(\v2\x18.WASyncAction.MuteActionR\n" +
	"muteAction\x125\n" +
	"\tpinAction\x18\x05 \x01(\v2\x17.WASyncAction.PinActionR\tpinAction\x12k\n" +
	"\x1bsecurityNotificationSetting\x18\x06 \x01(\v2).WASyncAction.SecurityNotificationSettingR\x1bsecurityNotificationSetting\x12G\n" +
	"\x0fpushNameSetting\x18\a \x01(\v2\x1d.WASyncAction.PushNameSettingR\x0fpushNameSetting\x12J\n" +
	"\x10quickReplyAction\x18\b \x01(\v2\x1e.WASyncAction.QuickReplyActionR\x10quickReplyAction\x12b\n" +
	"\x18recentEmojiWeightsAction\x18\v \x01(\v2&.WASyncAction.RecentEmojiWeightsActionR\x18recentEmojiWeightsAction\x12G\n" +
	"\x0flabelEditAction\x18\x0e \x01(\v2\x1d.WASyncAction.LabelEditActionR\x0flabelEditAction\x12\\\n" +
	"\x16labelAssociationAction\x18\x0f \x01(\v2$.WASyncAction.LabelAssociationActionR\x16labelAssociationAction\x12A\n" +
	"\rlocaleSetting\x18\x10 \x01(\v2\x1b.WASyncAction.LocaleSettingR\rlocaleSetting\x12M\n" +
	"\x11archiveChatAction\x18\x11 \x01(\v2\x1f.WASyncAction.ArchiveChatActionR\x11archiveChatAction\x12b\n" +
	"\x18deleteMessageForMeAction\x18\x12 \x01(\v2&.WASyncAction.DeleteMessageForMeActionR\x18deleteMessageForMeAction\x12A\n" +
	"\rkeyExpiration\x18\x13 \x01(\v2\x1b.WASyncAction.KeyExpirationR\rkeyExpiration\x12V\n" +
	"\x14markChatAsReadAction\x18\x14 \x01(\v2\".WASyncAction.MarkChatAsReadActionR\x14markChatAsReadAction\x12G\n" +
	"\x0fclearChatAction\x18\x15 \x01(\v2\x1d.WASyncAction.ClearChatActionR\x0fclearChatAction\x12J\n" +
	"\x10deleteChatAction\x18\x16 \x01(\v2\x1e.WASyncAction.DeleteChatActionR\x10deleteChatAction\x12Y\n" +
	"\x15unarchiveChatsSetting\x18\x17 \x01(\v2#.WASyncAction.UnarchiveChatsSettingR\x15unarchiveChatsSetting\x12D\n" +
	"\x0eprimaryFeature\x18\x18 \x01(\v2\x1c.WASyncAction.PrimaryFeatureR\x0eprimaryFeature\x12e\n" +
	"\x19androidUnsupportedActions\x18\x1a \x01(\v2'.WASyncAction.AndroidUnsupportedActionsR\x19androidUnsupportedActions\x12;\n" +
	"\vagentAction\x18\x1b \x01(\v2\x19.WASyncAction.AgentActionR\vagentAction\x12P\n" +
	"\x12subscriptionAction\x18\x1c \x01(\v2 .WASyncAction.SubscriptionActionR\x12subscriptionAction\x12V\n" +
	"\x14userStatusMuteAction\x18\x1d \x01(\v2\".WASyncAction.UserStatusMuteActionR\x14userStatusMuteAction\x12J\n" +
	"\x10timeFormatAction\x18\x1e \x01(\v2\x1e.WASyncAction.TimeFormatActionR\x10timeFormatAction\x125\n" +
	"\tnuxAction\x18\x1f \x01(\v2\x17.WASyncAction.NuxActionR\tnuxAction\x12V\n" +
	"\x14primaryVersionAction\x18  \x01(\v2\".WASyncAction.PrimaryVersionActionR\x14primaryVersionAction\x12A\n" +
	"\rstickerAction\x18! \x01(\v2\x1b.WASyncAction.StickerActionR\rstickerAction\x12e\n" +
	"\x19removeRecentStickerAction\x18\" \x01(\v2'.WASyncAction.RemoveRecentStickerActionR\x19removeRecentStickerAction\x12J\n" +
	"\x0echatAssignment\x18# \x01(\v2\".WASyncAction.ChatAssignmentActionR\x0echatAssignment\x12n\n" +
	"\x1achatAssignmentOpenedStatus\x18$ \x01(\v2..WASyncAction.ChatAssignmentOpenedStatusActionR\x1achatAssignmentOpenedStatus\x12P\n" +
	"\x12pnForLidChatAction\x18% \x01(\v2 .WASyncAction.PnForLidChatActionR\x12pnForLidChatAction\x12\\\n" +
	"\x16marketingMessageAction\x18& \x01(\v2$.WASyncAction.MarketingMessageActionR\x16marketingMessageAction\x12w\n" +
	"\x1fmarketingMessageBroadcastAction\x18' \x01(\v2-.WASyncAction.MarketingMessageBroadcastActionR\x1fmarketingMessageBroadcastAction\x12Y\n" +
	"\x15externalWebBetaAction\x18( \x01(\v2#.WASyncAction.ExternalWebBetaActionR\x15externalWebBetaAction\x12k\n" +
	"\x1bprivacySettingRelayAllCalls\x18) \x01(\v2).WASyncAction.PrivacySettingRelayAllCallsR\x1bprivacySettingRelayAllCalls\x12A\n" +
	"\rcallLogAction\x18* \x01(\v2\x1b.WASyncAction.CallLogActionR\rcallLogAction\x12G\n" +
	"\rstatusPrivacy\x18, \x01(\v2!.WASyncAction.StatusPrivacyActionR\rstatusPrivacy\x12_\n" +
	"\x17botWelcomeRequestAction\x18- \x01(\v2%.WASyncAction.BotWelcomeRequestActionR\x17botWelcomeRequestAction\x12e\n" +
	"\x17deleteIndividualCallLog\x18. \x01(\v2+.WASyncAction.DeleteIndividualCallLogActionR\x17deleteIndividualCallLog\x12Y\n" +
	"\x15labelReorderingAction\x18/ \x01(\v2#.WASyncAction.LabelReorderingActionR\x15labelReorderingAction\x12M\n" +
	"\x11paymentInfoAction\x180 \x01(\v2\x1f.WASyncAction.PaymentInfoActionR\x11paymentInfoAction\x12h\n" +
	"\x1acustomPaymentMethodsAction\x181 \x01(\v2(.WASyncAction.CustomPaymentMethodsActionR\x1acustomPaymentMethodsAction\x12D\n" +
	"\x0elockChatAction\x182 \x01(\v2\x1c.WASyncAction.LockChatActionR\x0elockChatAction\x12Y\n" +
	"\x10chatLockSettings\x183 \x01(\v2-.WAProtobufsChatLockSettings.ChatLockSettingsR\x10chatLockSettings\x12b\n" +
	"\x18wamoUserIdentifierAction\x184 \x01(\v2&.WASyncAction.WamoUserIdentifierActionR\x18wamoUserIdentifierAction\x12\x8f\x01\n" +
	"'privacySettingDisableLinkPreviewsAction\x185 \x01(\v25.WASyncAction.PrivacySettingDisableLinkPreviewsActionR'privacySettingDisableLinkPreviewsAction\x12a\n" +
	"\x12deviceCapabilities\x186 \x01(\v21.WAProtobufsDeviceCapabilities.DeviceCapabilitiesR\x12deviceCapabilities\x12D\n" +
	"\x0enoteEditAction\x187 \x01(\v2\x1c.WASyncAction.NoteEditActionR\x0enoteEditAction\x12G\n" +
	"\x0ffavoritesAction\x188 \x01(\v2\x1d.WASyncAction.FavoritesActionR\x0ffavoritesAction\x12n\n" +
	"\x1cmerchantPaymentPartnerAction\x189 \x01(\v2*.WASyncAction.MerchantPaymentPartnerActionR\x1cmerchantPaymentPartnerAction\x12n\n" +
	"\x1cwaffleAccountLinkStateAction\x18: \x01(\v2*.WASyncAction.WaffleAccountLinkStateActionR\x1cwaffleAccountLinkStateAction\x12_\n" +
	"\x15usernameChatStartMode\x18; \x01(\v2).WASyncAction.UsernameChatStartModeActionR\x15usernameChatStartMode\x12}\n" +
	"!notificationActivitySettingAction\x18< \x01(\v2/.WASyncAction.NotificationActivitySettingActionR!notificationActivitySettingAction\x12J\n" +
	"\x10lidContactAction\x18= \x01(\v2\x1e.WASyncAction.LidContactActionR\x10lidContactAction\x12z\n" +
	" ctwaPerCustomerDataSharingAction\x18> \x01(\v2..WASyncAction.CtwaPerCustomerDataSharingActionR ctwaPerCustomerDataSharingAction\x12J\n" +
	"\x10paymentTosAction\x18? \x01(\v2\x1e.WASyncAction.PaymentTosActionR\x10paymentTosAction\x12\xbc\x01\n" +
	"6privacySettingChannelsPersonalisedRecommendationAction\x18@ \x01(\v2D.WASyncAction.PrivacySettingChannelsPersonalisedRecommendationActionR6privacySettingChannelsPersonalisedRecommendationAction\x12\x80\x01\n" +
	"\"businessBroadcastAssociationAction\x18A \x01(\v20.WASyncAction.BusinessBroadcastAssociationActionR\"businessBroadcastAssociationAction\x12n\n" +
	"\x1cdetectedOutcomesStatusAction\x18B \x01(\v2*.WASyncAction.DetectedOutcomesStatusActionR\x1cdetectedOutcomesStatusAction\x12n\n" +
	"\x1cmaibaAiFeaturesControlAction\x18C \x01(\v2*.WASyncAction.MaibaAIFeaturesControlActionR\x1cmaibaAiFeaturesControlAction\">\n" +
	"\"BusinessBroadcastAssociationAction\x12\x18\n" +
	"\adeleted\x18\x01 \x01(\bR\adeleted\"t\n" +
	" CtwaPerCustomerDataSharingAction\x12P\n" +
	"#isCtwaPerCustomerDataSharingEnabled\x18\x01 \x01(\bR#isCtwaPerCustomerDataSharingEnabled\"\xa4\x01\n" +
	"\x10LidContactAction\x12\x1a\n" +
	"\bfullName\x18\x01 \x01(\tR\bfullName\x12\x1c\n" +
	"\tfirstName\x18\x02 \x01(\tR\tfirstName\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12:\n" +
	"\x18saveOnPrimaryAddressbook\x18\x04 \x01(\bR\x18saveOnPrimaryAddressbook\"s\n" +
	"\x0fFavoritesAction\x12D\n" +
	"\tfavorites\x18\x01 \x03(\v2&.WASyncAction.FavoritesAction.FavoriteR\tfavorites\x1a\x1a\n" +
	"\bFavorite\x12\x0e\n" +
	"\x02ID\x18\x01 \x01(\tR\x02ID\"`\n" +
	"6PrivacySettingChannelsPersonalisedRecommendationAction\x12&\n" +
	"\x0eisUserOptedOut\x18\x01 \x01(\bR\x0eisUserOptedOut\"Y\n" +
	"'PrivacySettingDisableLinkPreviewsAction\x12.\n" +
	"\x12isPreviewsDisabled\x18\x01 \x01(\bR\x12isPreviewsDisabled\":\n" +
	"\x18WamoUserIdentifierAction\x12\x1e\n" +
	"\n" +
	"identifier\x18\x01 \x01(\tR\n" +
	"identifier\"(\n" +
	"\x0eLockChatAction\x12\x16\n" +
	"\x06locked\x18\x01 \x01(\bR\x06locked\"s\n" +
	"\x1aCustomPaymentMethodsAction\x12U\n" +
	"\x14customPaymentMethods\x18\x01 \x03(\v2!.WASyncAction.CustomPaymentMethodR\x14customPaymentMethods\"\xae\x01\n" +
	"\x13CustomPaymentMethod\x12\"\n" +
	"\fcredentialID\x18\x01 \x02(\tR\fcredentialID\x12\x18\n" +
	"\acountry\x18\x02 \x02(\tR\acountry\x12\x12\n" +
	"\x04type\x18\x03 \x02(\tR\x04type\x12E\n" +
	"\bmetadata\x18\x04 \x03(\v2).WASyncAction.CustomPaymentMethodMetadataR\bmetadata\"E\n" +
	"\x1bCustomPaymentMethodMetadata\x12\x10\n" +
	"\x03key\x18\x01 \x02(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x02(\tR\x05value\"%\n" +
	"\x11PaymentInfoAction\x12\x10\n" +
	"\x03cpi\x18\x01 \x01(\tR\x03cpi\"?\n" +
	"\x15LabelReorderingAction\x12&\n" +
	"\x0esortedLabelIDs\x18\x01 \x03(\x05R\x0esortedLabelIDs\"Y\n" +
	"\x1dDeleteIndividualCallLogAction\x12\x18\n" +
	"\apeerJID\x18\x01 \x01(\tR\apeerJID\x12\x1e\n" +
	"\n" +
	"isIncoming\x18\x02 \x01(\bR\n" +
	"isIncoming\"1\n" +
	"\x17BotWelcomeRequestAction\x12\x16\n" +
	"\x06isSent\x18\x01 \x01(\bR\x06isSent\"R\n" +
	"\rCallLogAction\x12A\n" +
	"\rcallLogRecord\x18\x01 \x01(\v2\x1b.WASyncAction.CallLogRecordR\rcallLogRecord\";\n" +
	"\x1bPrivacySettingRelayAllCalls\x12\x1c\n" +
	"\tisEnabled\x18\x01 \x01(\bR\tisEnabled\"<\n" +
	"\x1cDetectedOutcomesStatusAction\x12\x1c\n" +
	"\tisEnabled\x18\x01 \x01(\bR\tisEnabled\"1\n" +
	"\x15ExternalWebBetaAction\x12\x18\n" +
	"\aisOptIn\x18\x01 \x01(\bR\aisOptIn\"E\n" +
	"\x1fMarketingMessageBroadcastAction\x12\"\n" +
	"\frepliedCount\x18\x01 \x01(\x05R\frepliedCount\"*\n" +
	"\x12PnForLidChatAction\x12\x14\n" +
	"\x05pnJID\x18\x01 \x01(\tR\x05pnJID\"B\n" +
	" ChatAssignmentOpenedStatusAction\x12\x1e\n" +
	"\n" +
	"chatOpened\x18\x01 \x01(\bR\n" +
	"chatOpened\"<\n" +
	"\x14ChatAssignmentAction\x12$\n" +
	"\rdeviceAgentID\x18\x01 \x01(\tR\rdeviceAgentID\"\xcd\x02\n" +
	"\rStickerAction\x12\x10\n" +
	"\x03URL\x18\x01 \x01(\tR\x03URL\x12$\n" +
	"\rfileEncSHA256\x18\x02 \x01(\fR\rfileEncSHA256\x12\x1a\n" +
	"\bmediaKey\x18\x03 \x01(\fR\bmediaKey\x12\x1a\n" +
	"\bmimetype\x18\x04 \x01(\tR\bmimetype\x12\x16\n" +
	"\x06height\x18\x05 \x01(\rR\x06height\x12\x14\n" +
	"\x05width\x18\x06 \x01(\rR\x05width\x12\x1e\n" +
	"\n" +
	"directPath\x18\a \x01(\tR\n" +
	"directPath\x12\x1e\n" +
	"\n" +
	"fileLength\x18\b \x01(\x04R\n" +
	"fileLength\x12\x1e\n" +
	"\n" +
	"isFavorite\x18\t \x01(\bR\n" +
	"isFavorite\x12\"\n" +
	"\fdeviceIDHint\x18\n" +
	" \x01(\rR\fdeviceIDHint\x12\x1a\n" +
	"\bisLottie\x18\v \x01(\bR\bisLottie\"I\n" +
	"\x19RemoveRecentStickerAction\x12,\n" +
	"\x11lastStickerSentTS\x18\x01 \x01(\x03R\x11lastStickerSentTS\"0\n" +
	"\x14PrimaryVersionAction\x12\x18\n" +
	"\aversion\x18\x01 \x01(\tR\aversion\"/\n" +
	"\tNuxAction\x12\"\n" +
	"\facknowledged\x18\x01 \x01(\bR\facknowledged\"X\n" +
	"\x10TimeFormatAction\x12D\n" +
	"\x1disTwentyFourHourFormatEnabled\x18\x01 \x01(\bR\x1disTwentyFourHourFormatEnabled\",\n" +
	"\x14UserStatusMuteAction\x12\x14\n" +
	"\x05muted\x18\x01 \x01(\bR\x05muted\"\x8a\x01\n" +
	"\x12SubscriptionAction\x12$\n" +
	"\risDeactivated\x18\x01 \x01(\bR\risDeactivated\x12&\n" +
	"\x0eisAutoRenewing\x18\x02 \x01(\bR\x0eisAutoRenewing\x12&\n" +
	"\x0eexpirationDate\x18\x03 \x01(\x03R\x0eexpirationDate\"[\n" +
	"\vAgentAction\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1a\n" +
	"\bdeviceID\x18\x02 \x01(\x05R\bdeviceID\x12\x1c\n" +
	"\tisDeleted\x18\x03 \x01(\bR\tisDeleted\"5\n" +
	"\x19AndroidUnsupportedActions\x12\x18\n" +
	"\aallowed\x18\x01 \x01(\bR\aallowed\"&\n" +
	"\x0ePrimaryFeature\x12\x14\n" +
	"\x05flags\x18\x01 \x03(\tR\x05flags\"9\n" +
	"\rKeyExpiration\x12(\n" +
	"\x0fexpiredKeyEpoch\x18\x01 \x01(\x05R\x0fexpiredKeyEpoch\"Y\n" +
	"\x11SyncActionMessage\x12&\n" +
	"\x03key\x18\x01 \x01(\v2\x14.WACommon.MessageKeyR\x03key\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\"\xc9\x01\n" +
	"\x16SyncActionMessageRange\x122\n" +
	"\x14lastMessageTimestamp\x18\x01 \x01(\x03R\x14lastMessageTimestamp\x12>\n" +
	"\x1alastSystemMessageTimestamp\x18\x02 \x01(\x03R\x1alastSystemMessageTimestamp\x12;\n" +
	"\bmessages\x18\x03 \x03(\v2\x1f.WASyncAction.SyncActionMessageR\bmessages\"?\n" +
	"\x15UnarchiveChatsSetting\x12&\n" +
	"\x0eunarchiveChats\x18\x01 \x01(\bR\x0eunarchiveChats\"\\\n" +
	"\x10DeleteChatAction\x12H\n" +
	"\fmessageRange\x18\x01 \x01(\v2$.WASyncAction.SyncActionMessageRangeR\fmessageRange\"[\n" +
	"\x0fClearChatAction\x12H\n" +
	"\fmessageRange\x18\x01 \x01(\v2$.WASyncAction.SyncActionMessageRangeR\fmessageRange\"t\n" +
	"\x14MarkChatAsReadAction\x12\x12\n" +
	"\x04read\x18\x01 \x01(\bR\x04read\x12H\n" +
	"\fmessageRange\x18\x02 \x01(\v2$.WASyncAction.SyncActionMessageRangeR\fmessageRange\"h\n" +
	"\x18DeleteMessageForMeAction\x12 \n" +
	"\vdeleteMedia\x18\x01 \x01(\bR\vdeleteMedia\x12*\n" +
	"\x10messageTimestamp\x18\x02 \x01(\x03R\x10messageTimestamp\"y\n" +
	"\x11ArchiveChatAction\x12\x1a\n" +
	"\barchived\x18\x01 \x01(\bR\barchived\x12H\n" +
	"\fmessageRange\x18\x02 \x01(\v2$.WASyncAction.SyncActionMessageRangeR\fmessageRange\"U\n" +
	"\x18RecentEmojiWeightsAction\x129\n" +
	"\aweights\x18\x01 \x03(\v2\x1f.WASyncAction.RecentEmojiWeightR\aweights\"2\n" +
	"\x16LabelAssociationAction\x12\x18\n" +
	"\alabeled\x18\x01 \x01(\bR\alabeled\"\x94\x01\n" +
	"\x10QuickReplyAction\x12\x1a\n" +
	"\bshortcut\x18\x01 \x01(\tR\bshortcut\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1a\n" +
	"\bkeywords\x18\x03 \x03(\tR\bkeywords\x12\x14\n" +
	"\x05count\x18\x04 \x01(\x05R\x05count\x12\x18\n" +
	"\adeleted\x18\x05 \x01(\bR\adeleted\"'\n" +
	"\rLocaleSetting\x12\x16\n" +
	"\x06locale\x18\x01 \x01(\tR\x06locale\"%\n" +
	"\x0fPushNameSetting\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"I\n" +
	"\x1bSecurityNotificationSetting\x12*\n" +
	"\x10showNotification\x18\x01 \x01(\bR\x10showNotification\"#\n" +
	"\tPinAction\x12\x16\n" +
	"\x06pinned\x18\x01 \x01(\bR\x06pinned\"l\n" +
	"\n" +
	"MuteAction\x12\x14\n" +
	"\x05muted\x18\x01 \x01(\bR\x05muted\x12*\n" +
	"\x10muteEndTimestamp\x18\x02 \x01(\x03R\x10muteEndTimestamp\x12\x1c\n" +
	"\tautoMuted\x18\x03 \x01(\bR\tautoMuted\"\xcf\x01\n" +
	"\rContactAction\x12\x1a\n" +
	"\bfullName\x18\x01 \x01(\tR\bfullName\x12\x1c\n" +
	"\tfirstName\x18\x02 \x01(\tR\tfirstName\x12\x16\n" +
	"\x06lidJID\x18\x03 \x01(\tR\x06lidJID\x12:\n" +
	"\x18saveOnPrimaryAddressbook\x18\x04 \x01(\bR\x18saveOnPrimaryAddressbook\x12\x14\n" +
	"\x05pnJID\x18\x05 \x01(\tR\x05pnJID\x12\x1a\n" +
	"\busername\x18\x06 \x01(\tR\busername\"&\n" +
	"\n" +
	"StarAction\x12\x18\n" +
	"\astarred\x18\x01 \x01(\bR\astarred\"\x8f\x01\n" +
	"\x0eSyncActionData\x12\x14\n" +
	"\x05index\x18\x01 \x01(\fR\x05index\x123\n" +
	"\x05value\x18\x02 \x01(\v2\x1d.WASyncAction.SyncActionValueR\x05value\x12\x18\n" +
	"\apadding\x18\x03 \x01(\fR\apadding\x12\x18\n" +
	"\aversion\x18\x04 \x01(\x05R\aversionB(Z&go.mau.fi/whatsmeow/proto/waSyncAction"

var (
	file_waSyncAction_WASyncAction_proto_rawDescOnce sync.Once
	file_waSyncAction_WASyncAction_proto_rawDescData []byte
)

func file_waSyncAction_WASyncAction_proto_rawDescGZIP() []byte {
	file_waSyncAction_WASyncAction_proto_rawDescOnce.Do(func() {
		file_waSyncAction_WASyncAction_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waSyncAction_WASyncAction_proto_rawDesc), len(file_waSyncAction_WASyncAction_proto_rawDesc)))
	})
	return file_waSyncAction_WASyncAction_proto_rawDescData
}

var file_waSyncAction_WASyncAction_proto_enumTypes = make([]protoimpl.EnumInfo, 14)
var file_waSyncAction_WASyncAction_proto_msgTypes = make([]protoimpl.MessageInfo, 69)
var file_waSyncAction_WASyncAction_proto_goTypes = []any{
	(CallLogRecord_CallType)(0),                                        // 0: WASyncAction.CallLogRecord.CallType
	(CallLogRecord_SilenceReason)(0),                                   // 1: WASyncAction.CallLogRecord.SilenceReason
	(CallLogRecord_CallResult)(0),                                      // 2: WASyncAction.CallLogRecord.CallResult
	(MaibaAIFeaturesControlAction_MaibaAIFeatureStatus)(0),             // 3: WASyncAction.MaibaAIFeaturesControlAction.MaibaAIFeatureStatus
	(PaymentTosAction_PaymentNotice)(0),                                // 4: WASyncAction.PaymentTosAction.PaymentNotice
	(NotificationActivitySettingAction_NotificationActivitySetting)(0), // 5: WASyncAction.NotificationActivitySettingAction.NotificationActivitySetting
	(WaffleAccountLinkStateAction_AccountLinkState)(0),                 // 6: WASyncAction.WaffleAccountLinkStateAction.AccountLinkState
	(MerchantPaymentPartnerAction_Status)(0),                           // 7: WASyncAction.MerchantPaymentPartnerAction.Status
	(NoteEditAction_NoteType)(0),                                       // 8: WASyncAction.NoteEditAction.NoteType
	(StatusPrivacyAction_StatusDistributionMode)(0),                    // 9: WASyncAction.StatusPrivacyAction.StatusDistributionMode
	(MarketingMessageAction_MarketingMessagePrototypeType)(0),          // 10: WASyncAction.MarketingMessageAction.MarketingMessagePrototypeType
	(UsernameChatStartModeAction_ChatStartMode)(0),                     // 11: WASyncAction.UsernameChatStartModeAction.ChatStartMode
	(LabelEditAction_ListType)(0),                                      // 12: WASyncAction.LabelEditAction.ListType
	(PatchDebugData_Platform)(0),                                       // 13: WASyncAction.PatchDebugData.Platform
	(*CallLogRecord)(nil),                                              // 14: WASyncAction.CallLogRecord
	(*MaibaAIFeaturesControlAction)(nil),                               // 15: WASyncAction.MaibaAIFeaturesControlAction
	(*PaymentTosAction)(nil),                                           // 16: WASyncAction.PaymentTosAction
	(*NotificationActivitySettingAction)(nil),                          // 17: WASyncAction.NotificationActivitySettingAction
	(*WaffleAccountLinkStateAction)(nil),                               // 18: WASyncAction.WaffleAccountLinkStateAction
	(*MerchantPaymentPartnerAction)(nil),                               // 19: WASyncAction.MerchantPaymentPartnerAction
	(*NoteEditAction)(nil),                                             // 20: WASyncAction.NoteEditAction
	(*StatusPrivacyAction)(nil),                                        // 21: WASyncAction.StatusPrivacyAction
	(*MarketingMessageAction)(nil),                                     // 22: WASyncAction.MarketingMessageAction
	(*UsernameChatStartModeAction)(nil),                                // 23: WASyncAction.UsernameChatStartModeAction
	(*LabelEditAction)(nil),                                            // 24: WASyncAction.LabelEditAction
	(*PatchDebugData)(nil),                                             // 25: WASyncAction.PatchDebugData
	(*RecentEmojiWeight)(nil),                                          // 26: WASyncAction.RecentEmojiWeight
	(*SyncActionValue)(nil),                                            // 27: WASyncAction.SyncActionValue
	(*BusinessBroadcastAssociationAction)(nil),                         // 28: WASyncAction.BusinessBroadcastAssociationAction
	(*CtwaPerCustomerDataSharingAction)(nil),                           // 29: WASyncAction.CtwaPerCustomerDataSharingAction
	(*LidContactAction)(nil),                                           // 30: WASyncAction.LidContactAction
	(*FavoritesAction)(nil),                                            // 31: WASyncAction.FavoritesAction
	(*PrivacySettingChannelsPersonalisedRecommendationAction)(nil),     // 32: WASyncAction.PrivacySettingChannelsPersonalisedRecommendationAction
	(*PrivacySettingDisableLinkPreviewsAction)(nil),                    // 33: WASyncAction.PrivacySettingDisableLinkPreviewsAction
	(*WamoUserIdentifierAction)(nil),                                   // 34: WASyncAction.WamoUserIdentifierAction
	(*LockChatAction)(nil),                                             // 35: WASyncAction.LockChatAction
	(*CustomPaymentMethodsAction)(nil),                                 // 36: WASyncAction.CustomPaymentMethodsAction
	(*CustomPaymentMethod)(nil),                                        // 37: WASyncAction.CustomPaymentMethod
	(*CustomPaymentMethodMetadata)(nil),                                // 38: WASyncAction.CustomPaymentMethodMetadata
	(*PaymentInfoAction)(nil),                                          // 39: WASyncAction.PaymentInfoAction
	(*LabelReorderingAction)(nil),                                      // 40: WASyncAction.LabelReorderingAction
	(*DeleteIndividualCallLogAction)(nil),                              // 41: WASyncAction.DeleteIndividualCallLogAction
	(*BotWelcomeRequestAction)(nil),                                    // 42: WASyncAction.BotWelcomeRequestAction
	(*CallLogAction)(nil),                                              // 43: WASyncAction.CallLogAction
	(*PrivacySettingRelayAllCalls)(nil),                                // 44: WASyncAction.PrivacySettingRelayAllCalls
	(*DetectedOutcomesStatusAction)(nil),                               // 45: WASyncAction.DetectedOutcomesStatusAction
	(*ExternalWebBetaAction)(nil),                                      // 46: WASyncAction.ExternalWebBetaAction
	(*MarketingMessageBroadcastAction)(nil),                            // 47: WASyncAction.MarketingMessageBroadcastAction
	(*PnForLidChatAction)(nil),                                         // 48: WASyncAction.PnForLidChatAction
	(*ChatAssignmentOpenedStatusAction)(nil),                           // 49: WASyncAction.ChatAssignmentOpenedStatusAction
	(*ChatAssignmentAction)(nil),                                       // 50: WASyncAction.ChatAssignmentAction
	(*StickerAction)(nil),                                              // 51: WASyncAction.StickerAction
	(*RemoveRecentStickerAction)(nil),                                  // 52: WASyncAction.RemoveRecentStickerAction
	(*PrimaryVersionAction)(nil),                                       // 53: WASyncAction.PrimaryVersionAction
	(*NuxAction)(nil),                                                  // 54: WASyncAction.NuxAction
	(*TimeFormatAction)(nil),                                           // 55: WASyncAction.TimeFormatAction
	(*UserStatusMuteAction)(nil),                                       // 56: WASyncAction.UserStatusMuteAction
	(*SubscriptionAction)(nil),                                         // 57: WASyncAction.SubscriptionAction
	(*AgentAction)(nil),                                                // 58: WASyncAction.AgentAction
	(*AndroidUnsupportedActions)(nil),                                  // 59: WASyncAction.AndroidUnsupportedActions
	(*PrimaryFeature)(nil),                                             // 60: WASyncAction.PrimaryFeature
	(*KeyExpiration)(nil),                                              // 61: WASyncAction.KeyExpiration
	(*SyncActionMessage)(nil),                                          // 62: WASyncAction.SyncActionMessage
	(*SyncActionMessageRange)(nil),                                     // 63: WASyncAction.SyncActionMessageRange
	(*UnarchiveChatsSetting)(nil),                                      // 64: WASyncAction.UnarchiveChatsSetting
	(*DeleteChatAction)(nil),                                           // 65: WASyncAction.DeleteChatAction
	(*ClearChatAction)(nil),                                            // 66: WASyncAction.ClearChatAction
	(*MarkChatAsReadAction)(nil),                                       // 67: WASyncAction.MarkChatAsReadAction
	(*DeleteMessageForMeAction)(nil),                                   // 68: WASyncAction.DeleteMessageForMeAction
	(*ArchiveChatAction)(nil),                                          // 69: WASyncAction.ArchiveChatAction
	(*RecentEmojiWeightsAction)(nil),                                   // 70: WASyncAction.RecentEmojiWeightsAction
	(*LabelAssociationAction)(nil),                                     // 71: WASyncAction.LabelAssociationAction
	(*QuickReplyAction)(nil),                                           // 72: WASyncAction.QuickReplyAction
	(*LocaleSetting)(nil),                                              // 73: WASyncAction.LocaleSetting
	(*PushNameSetting)(nil),                                            // 74: WASyncAction.PushNameSetting
	(*SecurityNotificationSetting)(nil),                                // 75: WASyncAction.SecurityNotificationSetting
	(*PinAction)(nil),                                                  // 76: WASyncAction.PinAction
	(*MuteAction)(nil),                                                 // 77: WASyncAction.MuteAction
	(*ContactAction)(nil),                                              // 78: WASyncAction.ContactAction
	(*StarAction)(nil),                                                 // 79: WASyncAction.StarAction
	(*SyncActionData)(nil),                                             // 80: WASyncAction.SyncActionData
	(*CallLogRecord_ParticipantInfo)(nil),                              // 81: WASyncAction.CallLogRecord.ParticipantInfo
	(*FavoritesAction_Favorite)(nil),                                   // 82: WASyncAction.FavoritesAction.Favorite
	(*waChatLockSettings.ChatLockSettings)(nil),                        // 83: WAProtobufsChatLockSettings.ChatLockSettings
	(*waDeviceCapabilities.DeviceCapabilities)(nil),                    // 84: WAProtobufsDeviceCapabilities.DeviceCapabilities
	(*waCommon.MessageKey)(nil),                                        // 85: WACommon.MessageKey
}
var file_waSyncAction_WASyncAction_proto_depIdxs = []int32{
	2,  // 0: WASyncAction.CallLogRecord.callResult:type_name -> WASyncAction.CallLogRecord.CallResult
	1,  // 1: WASyncAction.CallLogRecord.silenceReason:type_name -> WASyncAction.CallLogRecord.SilenceReason
	81, // 2: WASyncAction.CallLogRecord.participants:type_name -> WASyncAction.CallLogRecord.ParticipantInfo
	0,  // 3: WASyncAction.CallLogRecord.callType:type_name -> WASyncAction.CallLogRecord.CallType
	3,  // 4: WASyncAction.MaibaAIFeaturesControlAction.aiFeatureStatus:type_name -> WASyncAction.MaibaAIFeaturesControlAction.MaibaAIFeatureStatus
	4,  // 5: WASyncAction.PaymentTosAction.paymentNotice:type_name -> WASyncAction.PaymentTosAction.PaymentNotice
	5,  // 6: WASyncAction.NotificationActivitySettingAction.notificationActivitySetting:type_name -> WASyncAction.NotificationActivitySettingAction.NotificationActivitySetting
	6,  // 7: WASyncAction.WaffleAccountLinkStateAction.linkState:type_name -> WASyncAction.WaffleAccountLinkStateAction.AccountLinkState
	7,  // 8: WASyncAction.MerchantPaymentPartnerAction.status:type_name -> WASyncAction.MerchantPaymentPartnerAction.Status
	8,  // 9: WASyncAction.NoteEditAction.type:type_name -> WASyncAction.NoteEditAction.NoteType
	9,  // 10: WASyncAction.StatusPrivacyAction.mode:type_name -> WASyncAction.StatusPrivacyAction.StatusDistributionMode
	10, // 11: WASyncAction.MarketingMessageAction.type:type_name -> WASyncAction.MarketingMessageAction.MarketingMessagePrototypeType
	11, // 12: WASyncAction.UsernameChatStartModeAction.chatStartMode:type_name -> WASyncAction.UsernameChatStartModeAction.ChatStartMode
	12, // 13: WASyncAction.LabelEditAction.type:type_name -> WASyncAction.LabelEditAction.ListType
	13, // 14: WASyncAction.PatchDebugData.senderPlatform:type_name -> WASyncAction.PatchDebugData.Platform
	79, // 15: WASyncAction.SyncActionValue.starAction:type_name -> WASyncAction.StarAction
	78, // 16: WASyncAction.SyncActionValue.contactAction:type_name -> WASyncAction.ContactAction
	77, // 17: WASyncAction.SyncActionValue.muteAction:type_name -> WASyncAction.MuteAction
	76, // 18: WASyncAction.SyncActionValue.pinAction:type_name -> WASyncAction.PinAction
	75, // 19: WASyncAction.SyncActionValue.securityNotificationSetting:type_name -> WASyncAction.SecurityNotificationSetting
	74, // 20: WASyncAction.SyncActionValue.pushNameSetting:type_name -> WASyncAction.PushNameSetting
	72, // 21: WASyncAction.SyncActionValue.quickReplyAction:type_name -> WASyncAction.QuickReplyAction
	70, // 22: WASyncAction.SyncActionValue.recentEmojiWeightsAction:type_name -> WASyncAction.RecentEmojiWeightsAction
	24, // 23: WASyncAction.SyncActionValue.labelEditAction:type_name -> WASyncAction.LabelEditAction
	71, // 24: WASyncAction.SyncActionValue.labelAssociationAction:type_name -> WASyncAction.LabelAssociationAction
	73, // 25: WASyncAction.SyncActionValue.localeSetting:type_name -> WASyncAction.LocaleSetting
	69, // 26: WASyncAction.SyncActionValue.archiveChatAction:type_name -> WASyncAction.ArchiveChatAction
	68, // 27: WASyncAction.SyncActionValue.deleteMessageForMeAction:type_name -> WASyncAction.DeleteMessageForMeAction
	61, // 28: WASyncAction.SyncActionValue.keyExpiration:type_name -> WASyncAction.KeyExpiration
	67, // 29: WASyncAction.SyncActionValue.markChatAsReadAction:type_name -> WASyncAction.MarkChatAsReadAction
	66, // 30: WASyncAction.SyncActionValue.clearChatAction:type_name -> WASyncAction.ClearChatAction
	65, // 31: WASyncAction.SyncActionValue.deleteChatAction:type_name -> WASyncAction.DeleteChatAction
	64, // 32: WASyncAction.SyncActionValue.unarchiveChatsSetting:type_name -> WASyncAction.UnarchiveChatsSetting
	60, // 33: WASyncAction.SyncActionValue.primaryFeature:type_name -> WASyncAction.PrimaryFeature
	59, // 34: WASyncAction.SyncActionValue.androidUnsupportedActions:type_name -> WASyncAction.AndroidUnsupportedActions
	58, // 35: WASyncAction.SyncActionValue.agentAction:type_name -> WASyncAction.AgentAction
	57, // 36: WASyncAction.SyncActionValue.subscriptionAction:type_name -> WASyncAction.SubscriptionAction
	56, // 37: WASyncAction.SyncActionValue.userStatusMuteAction:type_name -> WASyncAction.UserStatusMuteAction
	55, // 38: WASyncAction.SyncActionValue.timeFormatAction:type_name -> WASyncAction.TimeFormatAction
	54, // 39: WASyncAction.SyncActionValue.nuxAction:type_name -> WASyncAction.NuxAction
	53, // 40: WASyncAction.SyncActionValue.primaryVersionAction:type_name -> WASyncAction.PrimaryVersionAction
	51, // 41: WASyncAction.SyncActionValue.stickerAction:type_name -> WASyncAction.StickerAction
	52, // 42: WASyncAction.SyncActionValue.removeRecentStickerAction:type_name -> WASyncAction.RemoveRecentStickerAction
	50, // 43: WASyncAction.SyncActionValue.chatAssignment:type_name -> WASyncAction.ChatAssignmentAction
	49, // 44: WASyncAction.SyncActionValue.chatAssignmentOpenedStatus:type_name -> WASyncAction.ChatAssignmentOpenedStatusAction
	48, // 45: WASyncAction.SyncActionValue.pnForLidChatAction:type_name -> WASyncAction.PnForLidChatAction
	22, // 46: WASyncAction.SyncActionValue.marketingMessageAction:type_name -> WASyncAction.MarketingMessageAction
	47, // 47: WASyncAction.SyncActionValue.marketingMessageBroadcastAction:type_name -> WASyncAction.MarketingMessageBroadcastAction
	46, // 48: WASyncAction.SyncActionValue.externalWebBetaAction:type_name -> WASyncAction.ExternalWebBetaAction
	44, // 49: WASyncAction.SyncActionValue.privacySettingRelayAllCalls:type_name -> WASyncAction.PrivacySettingRelayAllCalls
	43, // 50: WASyncAction.SyncActionValue.callLogAction:type_name -> WASyncAction.CallLogAction
	21, // 51: WASyncAction.SyncActionValue.statusPrivacy:type_name -> WASyncAction.StatusPrivacyAction
	42, // 52: WASyncAction.SyncActionValue.botWelcomeRequestAction:type_name -> WASyncAction.BotWelcomeRequestAction
	41, // 53: WASyncAction.SyncActionValue.deleteIndividualCallLog:type_name -> WASyncAction.DeleteIndividualCallLogAction
	40, // 54: WASyncAction.SyncActionValue.labelReorderingAction:type_name -> WASyncAction.LabelReorderingAction
	39, // 55: WASyncAction.SyncActionValue.paymentInfoAction:type_name -> WASyncAction.PaymentInfoAction
	36, // 56: WASyncAction.SyncActionValue.customPaymentMethodsAction:type_name -> WASyncAction.CustomPaymentMethodsAction
	35, // 57: WASyncAction.SyncActionValue.lockChatAction:type_name -> WASyncAction.LockChatAction
	83, // 58: WASyncAction.SyncActionValue.chatLockSettings:type_name -> WAProtobufsChatLockSettings.ChatLockSettings
	34, // 59: WASyncAction.SyncActionValue.wamoUserIdentifierAction:type_name -> WASyncAction.WamoUserIdentifierAction
	33, // 60: WASyncAction.SyncActionValue.privacySettingDisableLinkPreviewsAction:type_name -> WASyncAction.PrivacySettingDisableLinkPreviewsAction
	84, // 61: WASyncAction.SyncActionValue.deviceCapabilities:type_name -> WAProtobufsDeviceCapabilities.DeviceCapabilities
	20, // 62: WASyncAction.SyncActionValue.noteEditAction:type_name -> WASyncAction.NoteEditAction
	31, // 63: WASyncAction.SyncActionValue.favoritesAction:type_name -> WASyncAction.FavoritesAction
	19, // 64: WASyncAction.SyncActionValue.merchantPaymentPartnerAction:type_name -> WASyncAction.MerchantPaymentPartnerAction
	18, // 65: WASyncAction.SyncActionValue.waffleAccountLinkStateAction:type_name -> WASyncAction.WaffleAccountLinkStateAction
	23, // 66: WASyncAction.SyncActionValue.usernameChatStartMode:type_name -> WASyncAction.UsernameChatStartModeAction
	17, // 67: WASyncAction.SyncActionValue.notificationActivitySettingAction:type_name -> WASyncAction.NotificationActivitySettingAction
	30, // 68: WASyncAction.SyncActionValue.lidContactAction:type_name -> WASyncAction.LidContactAction
	29, // 69: WASyncAction.SyncActionValue.ctwaPerCustomerDataSharingAction:type_name -> WASyncAction.CtwaPerCustomerDataSharingAction
	16, // 70: WASyncAction.SyncActionValue.paymentTosAction:type_name -> WASyncAction.PaymentTosAction
	32, // 71: WASyncAction.SyncActionValue.privacySettingChannelsPersonalisedRecommendationAction:type_name -> WASyncAction.PrivacySettingChannelsPersonalisedRecommendationAction
	28, // 72: WASyncAction.SyncActionValue.businessBroadcastAssociationAction:type_name -> WASyncAction.BusinessBroadcastAssociationAction
	45, // 73: WASyncAction.SyncActionValue.detectedOutcomesStatusAction:type_name -> WASyncAction.DetectedOutcomesStatusAction
	15, // 74: WASyncAction.SyncActionValue.maibaAiFeaturesControlAction:type_name -> WASyncAction.MaibaAIFeaturesControlAction
	82, // 75: WASyncAction.FavoritesAction.favorites:type_name -> WASyncAction.FavoritesAction.Favorite
	37, // 76: WASyncAction.CustomPaymentMethodsAction.customPaymentMethods:type_name -> WASyncAction.CustomPaymentMethod
	38, // 77: WASyncAction.CustomPaymentMethod.metadata:type_name -> WASyncAction.CustomPaymentMethodMetadata
	14, // 78: WASyncAction.CallLogAction.callLogRecord:type_name -> WASyncAction.CallLogRecord
	85, // 79: WASyncAction.SyncActionMessage.key:type_name -> WACommon.MessageKey
	62, // 80: WASyncAction.SyncActionMessageRange.messages:type_name -> WASyncAction.SyncActionMessage
	63, // 81: WASyncAction.DeleteChatAction.messageRange:type_name -> WASyncAction.SyncActionMessageRange
	63, // 82: WASyncAction.ClearChatAction.messageRange:type_name -> WASyncAction.SyncActionMessageRange
	63, // 83: WASyncAction.MarkChatAsReadAction.messageRange:type_name -> WASyncAction.SyncActionMessageRange
	63, // 84: WASyncAction.ArchiveChatAction.messageRange:type_name -> WASyncAction.SyncActionMessageRange
	26, // 85: WASyncAction.RecentEmojiWeightsAction.weights:type_name -> WASyncAction.RecentEmojiWeight
	27, // 86: WASyncAction.SyncActionData.value:type_name -> WASyncAction.SyncActionValue
	2,  // 87: WASyncAction.CallLogRecord.ParticipantInfo.callResult:type_name -> WASyncAction.CallLogRecord.CallResult
	88, // [88:88] is the sub-list for method output_type
	88, // [88:88] is the sub-list for method input_type
	88, // [88:88] is the sub-list for extension type_name
	88, // [88:88] is the sub-list for extension extendee
	0,  // [0:88] is the sub-list for field type_name
}

func init() { file_waSyncAction_WASyncAction_proto_init() }
func file_waSyncAction_WASyncAction_proto_init() {
	if File_waSyncAction_WASyncAction_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waSyncAction_WASyncAction_proto_rawDesc), len(file_waSyncAction_WASyncAction_proto_rawDesc)),
			NumEnums:      14,
			NumMessages:   69,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waSyncAction_WASyncAction_proto_goTypes,
		DependencyIndexes: file_waSyncAction_WASyncAction_proto_depIdxs,
		EnumInfos:         file_waSyncAction_WASyncAction_proto_enumTypes,
		MessageInfos:      file_waSyncAction_WASyncAction_proto_msgTypes,
	}.Build()
	File_waSyncAction_WASyncAction_proto = out.File
	file_waSyncAction_WASyncAction_proto_goTypes = nil
	file_waSyncAction_WASyncAction_proto_depIdxs = nil
}
