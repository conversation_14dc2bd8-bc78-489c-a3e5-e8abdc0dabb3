// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: instamadilloCoreTypeAdminMessage/InstamadilloCoreTypeAdminMessage.proto

package instamadilloCoreTypeAdminMessage

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeviceAdminMessage_Type int32

const (
	DeviceAdminMessage_DEVICE_ADMIN_MESSAGE_TYPE_NONE                                         DeviceAdminMessage_Type = 0
	DeviceAdminMessage_DEVICE_ADMIN_MESSAGE_TYPE_LOCAL_USER_CHANGED_IDENTITY_KEY_NAMED_DEVICE DeviceAdminMessage_Type = 1
	DeviceAdminMessage_DEVICE_ADMIN_MESSAGE_TYPE_SECURITY_ALERT_PARTICIPANT_KEY_CHANGE        DeviceAdminMessage_Type = 2
	DeviceAdminMessage_DEVICE_ADMIN_MESSAGE_TYPE_SECURITY_ALERT_PARTICIPANT_NEW_LOGIN         DeviceAdminMessage_Type = 3
)

// Enum value maps for DeviceAdminMessage_Type.
var (
	DeviceAdminMessage_Type_name = map[int32]string{
		0: "DEVICE_ADMIN_MESSAGE_TYPE_NONE",
		1: "DEVICE_ADMIN_MESSAGE_TYPE_LOCAL_USER_CHANGED_IDENTITY_KEY_NAMED_DEVICE",
		2: "DEVICE_ADMIN_MESSAGE_TYPE_SECURITY_ALERT_PARTICIPANT_KEY_CHANGE",
		3: "DEVICE_ADMIN_MESSAGE_TYPE_SECURITY_ALERT_PARTICIPANT_NEW_LOGIN",
	}
	DeviceAdminMessage_Type_value = map[string]int32{
		"DEVICE_ADMIN_MESSAGE_TYPE_NONE":                                         0,
		"DEVICE_ADMIN_MESSAGE_TYPE_LOCAL_USER_CHANGED_IDENTITY_KEY_NAMED_DEVICE": 1,
		"DEVICE_ADMIN_MESSAGE_TYPE_SECURITY_ALERT_PARTICIPANT_KEY_CHANGE":        2,
		"DEVICE_ADMIN_MESSAGE_TYPE_SECURITY_ALERT_PARTICIPANT_NEW_LOGIN":         3,
	}
)

func (x DeviceAdminMessage_Type) Enum() *DeviceAdminMessage_Type {
	p := new(DeviceAdminMessage_Type)
	*p = x
	return p
}

func (x DeviceAdminMessage_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceAdminMessage_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_enumTypes[0].Descriptor()
}

func (DeviceAdminMessage_Type) Type() protoreflect.EnumType {
	return &file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_enumTypes[0]
}

func (x DeviceAdminMessage_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DeviceAdminMessage_Type) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DeviceAdminMessage_Type(num)
	return nil
}

// Deprecated: Use DeviceAdminMessage_Type.Descriptor instead.
func (DeviceAdminMessage_Type) EnumDescriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDescGZIP(), []int{1, 0}
}

type AdminMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to AdminMessageSubtype:
	//
	//	*AdminMessage_DeviceAdminMessage
	AdminMessageSubtype isAdminMessage_AdminMessageSubtype `protobuf_oneof:"adminMessageSubtype"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *AdminMessage) Reset() {
	*x = AdminMessage{}
	mi := &file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AdminMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AdminMessage) ProtoMessage() {}

func (x *AdminMessage) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AdminMessage.ProtoReflect.Descriptor instead.
func (*AdminMessage) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDescGZIP(), []int{0}
}

func (x *AdminMessage) GetAdminMessageSubtype() isAdminMessage_AdminMessageSubtype {
	if x != nil {
		return x.AdminMessageSubtype
	}
	return nil
}

func (x *AdminMessage) GetDeviceAdminMessage() *DeviceAdminMessage {
	if x != nil {
		if x, ok := x.AdminMessageSubtype.(*AdminMessage_DeviceAdminMessage); ok {
			return x.DeviceAdminMessage
		}
	}
	return nil
}

type isAdminMessage_AdminMessageSubtype interface {
	isAdminMessage_AdminMessageSubtype()
}

type AdminMessage_DeviceAdminMessage struct {
	DeviceAdminMessage *DeviceAdminMessage `protobuf:"bytes,1,opt,name=deviceAdminMessage,oneof"`
}

func (*AdminMessage_DeviceAdminMessage) isAdminMessage_AdminMessageSubtype() {}

type DeviceAdminMessage struct {
	state                  protoimpl.MessageState   `protogen:"open.v1"`
	DeviceAdminMessageType *DeviceAdminMessage_Type `protobuf:"varint,1,opt,name=deviceAdminMessageType,enum=InstamadilloCoreTypeAdminMessage.DeviceAdminMessage_Type" json:"deviceAdminMessageType,omitempty"`
	DeviceName             *string                  `protobuf:"bytes,2,opt,name=deviceName" json:"deviceName,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *DeviceAdminMessage) Reset() {
	*x = DeviceAdminMessage{}
	mi := &file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceAdminMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceAdminMessage) ProtoMessage() {}

func (x *DeviceAdminMessage) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceAdminMessage.ProtoReflect.Descriptor instead.
func (*DeviceAdminMessage) Descriptor() ([]byte, []int) {
	return file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceAdminMessage) GetDeviceAdminMessageType() DeviceAdminMessage_Type {
	if x != nil && x.DeviceAdminMessageType != nil {
		return *x.DeviceAdminMessageType
	}
	return DeviceAdminMessage_DEVICE_ADMIN_MESSAGE_TYPE_NONE
}

func (x *DeviceAdminMessage) GetDeviceName() string {
	if x != nil && x.DeviceName != nil {
		return *x.DeviceName
	}
	return ""
}

var File_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto protoreflect.FileDescriptor

const file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDesc = "" +
	"\n" +
	"GinstamadilloCoreTypeAdminMessage/InstamadilloCoreTypeAdminMessage.proto\x12 InstamadilloCoreTypeAdminMessage\"\x8d\x01\n" +
	"\fAdminMessage\x12f\n" +
	"\x12deviceAdminMessage\x18\x01 \x01(\v24.InstamadilloCoreTypeAdminMessage.DeviceAdminMessageH\x00R\x12deviceAdminMessageB\x15\n" +
	"\x13adminMessageSubtype\"\xa9\x03\n" +
	"\x12DeviceAdminMessage\x12q\n" +
	"\x16deviceAdminMessageType\x18\x01 \x01(\x0e29.InstamadilloCoreTypeAdminMessage.DeviceAdminMessage.TypeR\x16deviceAdminMessageType\x12\x1e\n" +
	"\n" +
	"deviceName\x18\x02 \x01(\tR\n" +
	"deviceName\"\xff\x01\n" +
	"\x04Type\x12\"\n" +
	"\x1eDEVICE_ADMIN_MESSAGE_TYPE_NONE\x10\x00\x12J\n" +
	"FDEVICE_ADMIN_MESSAGE_TYPE_LOCAL_USER_CHANGED_IDENTITY_KEY_NAMED_DEVICE\x10\x01\x12C\n" +
	"?DEVICE_ADMIN_MESSAGE_TYPE_SECURITY_ALERT_PARTICIPANT_KEY_CHANGE\x10\x02\x12B\n" +
	">DEVICE_ADMIN_MESSAGE_TYPE_SECURITY_ALERT_PARTICIPANT_NEW_LOGIN\x10\x03B<Z:go.mau.fi/whatsmeow/proto/instamadilloCoreTypeAdminMessage"

var (
	file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDescOnce sync.Once
	file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDescData []byte
)

func file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDescGZIP() []byte {
	file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDescOnce.Do(func() {
		file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDesc), len(file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDesc)))
	})
	return file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDescData
}

var file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_goTypes = []any{
	(DeviceAdminMessage_Type)(0), // 0: InstamadilloCoreTypeAdminMessage.DeviceAdminMessage.Type
	(*AdminMessage)(nil),         // 1: InstamadilloCoreTypeAdminMessage.AdminMessage
	(*DeviceAdminMessage)(nil),   // 2: InstamadilloCoreTypeAdminMessage.DeviceAdminMessage
}
var file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_depIdxs = []int32{
	2, // 0: InstamadilloCoreTypeAdminMessage.AdminMessage.deviceAdminMessage:type_name -> InstamadilloCoreTypeAdminMessage.DeviceAdminMessage
	0, // 1: InstamadilloCoreTypeAdminMessage.DeviceAdminMessage.deviceAdminMessageType:type_name -> InstamadilloCoreTypeAdminMessage.DeviceAdminMessage.Type
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_init() }
func file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_init() {
	if File_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto != nil {
		return
	}
	file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_msgTypes[0].OneofWrappers = []any{
		(*AdminMessage_DeviceAdminMessage)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDesc), len(file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_goTypes,
		DependencyIndexes: file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_depIdxs,
		EnumInfos:         file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_enumTypes,
		MessageInfos:      file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_msgTypes,
	}.Build()
	File_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto = out.File
	file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_goTypes = nil
	file_instamadilloCoreTypeAdminMessage_InstamadilloCoreTypeAdminMessage_proto_depIdxs = nil
}
