//! Error types for WhatsApp client

use thiserror::Error;

/// Main error type for WhatsApp operations
#[derive(Debug, Error)]
pub enum WhatsAppError {
    #[error("Connection error: {0}")]
    Connection(#[from] ConnectionError),

    #[error("Protocol error: {0}")]
    Protocol(#[from] ProtocolError),

    #[error("Cryptography error: {0}")]
    Crypto(#[from] CryptoError),

    #[error("Storage error: {0}")]
    Store(#[from] StoreError),

    #[error("Serialization error: {0}")]
    Serialization(#[from] SerializationError),

    #[error("Authentication failed: {reason}")]
    AuthenticationFailed { reason: String },

    #[error("Not connected")]
    NotConnected,

    #[error("Invalid JID: {0}")]
    InvalidJid(String),

    #[error("Feature not implemented: {0}")]
    NotImplemented(String),
}

/// Connection-related errors
#[derive(Debug, Error)]
pub enum ConnectionError {
    #[error("WebSocket error: {0}")]
    WebSocket(String),

    #[error("Noise handshake failed: {0}")]
    NoiseHandshake(String),

    #[error("Connection timeout")]
    Timeout,

    #[error("Unexpected disconnection")]
    UnexpectedDisconnection,
}

/// Protocol-related errors
#[derive(Debug, Error)]
pub enum ProtocolError {
    #[error("Invalid message format")]
    InvalidMessageFormat,

    #[error("Unsupported protocol version")]
    UnsupportedProtocolVersion,

    #[error("Protocol violation: {0}")]
    ProtocolViolation(String),
}

/// Cryptography-related errors
#[derive(Debug, Error)]
pub enum CryptoError {
    #[error("Encryption failed: {0}")]
    EncryptionFailed(String),

    #[error("Decryption failed: {0}")]
    DecryptionFailed(String),

    #[error("Key generation failed: {0}")]
    KeyGenerationFailed(String),

    #[error("Invalid key: {0}")]
    InvalidKey(String),
}

/// Storage-related errors
#[derive(Debug, Error)]
pub enum StoreError {
    #[error("Database error: {0}")]
    Database(String),

    #[error("Serialization error: {0}")]
    Serialization(String),

    #[error("Not found: {0}")]
    NotFound(String),
}

/// Serialization-related errors
#[derive(Debug, Error)]
pub enum SerializationError {
    #[error("Protocol buffer error: {0}")]
    ProtocolBuffer(String),

    #[error("JSON error: {0}")]
    Json(String),

    #[error("Binary parsing error: {0}")]
    BinaryParsing(String),
}

/// Result type alias for convenience
pub type Result<T> = std::result::Result<T, WhatsAppError>;
