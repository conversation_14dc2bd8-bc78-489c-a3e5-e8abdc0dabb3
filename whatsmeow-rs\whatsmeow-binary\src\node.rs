//! Binary node representation

use crate::error::BinaryError;
use bytes::Bytes;
use std::collections::HashMap;

/// Binary node structure for WhatsApp protocol
#[derive(Debug, Clone)]
pub struct Node {
    pub tag: String,
    pub attributes: HashMap<String, String>,
    pub content: NodeContent,
}

/// Content of a binary node
#[derive(Debug, Clone)]
pub enum NodeContent {
    None,
    Text(String),
    Binary(Bytes),
    Children(Vec<Node>),
}

impl Node {
    /// Create a new node with tag
    pub fn new(tag: String) -> Self {
        Self {
            tag,
            attributes: HashMap::new(),
            content: NodeContent::None,
        }
    }

    /// Create a node with text content
    pub fn with_text(tag: String, text: String) -> Self {
        Self {
            tag,
            attributes: HashMap::new(),
            content: NodeContent::Text(text),
        }
    }

    /// Create a node with binary content
    pub fn with_binary(tag: String, data: Bytes) -> Self {
        Self {
            tag,
            attributes: HashMap::new(),
            content: NodeContent::Binary(data),
        }
    }

    /// Create a node with children
    pub fn with_children(tag: String, children: Vec<Node>) -> Self {
        Self {
            tag,
            attributes: HashMap::new(),
            content: NodeContent::Children(children),
        }
    }

    /// Add an attribute to the node
    pub fn with_attribute(mut self, key: String, value: String) -> Self {
        self.attributes.insert(key, value);
        self
    }

    /// Get an attribute value
    pub fn get_attribute(&self, key: &str) -> Option<&String> {
        self.attributes.get(key)
    }

    /// Parse a binary node from bytes
    pub fn parse(_data: &[u8]) -> Result<Self, BinaryError> {
        // TODO: Implement binary node parsing
        Err(BinaryError::DeserializationFailed(
            "Not implemented".to_string(),
        ))
    }

    /// Serialize the node to bytes
    pub fn serialize(&self) -> Result<Vec<u8>, BinaryError> {
        // TODO: Implement binary node serialization
        Err(BinaryError::SerializationFailed(
            "Not implemented".to_string(),
        ))
    }

    /// Get text content if available
    pub fn text(&self) -> Option<&String> {
        match &self.content {
            NodeContent::Text(text) => Some(text),
            _ => None,
        }
    }

    /// Get binary content if available
    pub fn binary(&self) -> Option<&Bytes> {
        match &self.content {
            NodeContent::Binary(data) => Some(data),
            _ => None,
        }
    }

    /// Get children if available
    pub fn children(&self) -> Option<&Vec<Node>> {
        match &self.content {
            NodeContent::Children(children) => Some(children),
            _ => None,
        }
    }
}
