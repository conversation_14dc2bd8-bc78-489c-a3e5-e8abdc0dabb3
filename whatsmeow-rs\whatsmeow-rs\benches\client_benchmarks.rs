use criterion::{criterion_group, criterion_main, Criterion};
use std::hint::black_box;

fn benchmark_jid_parsing(c: &mut Criterion) {
    c.bench_function("parse_jid", |b| {
        b.iter(|| {
            let jid =
                whatsmeow_types::Jid::from_string(black_box("<EMAIL>")).unwrap();
            black_box(jid);
        })
    });
}

fn benchmark_jid_to_string(c: &mut Criterion) {
    let jid = whatsmeow_types::Jid::new_unchecked(
        "1234567890".to_string(),
        "s.whatsapp.net".to_string(),
        0,
    );

    c.bench_function("jid_to_string", |b| {
        b.iter(|| {
            let s = black_box(&jid).to_string();
            black_box(s);
        })
    });
}

criterion_group!(benches, benchmark_jid_parsing, benchmark_jid_to_string);
criterion_main!(benches);
