// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waFingerprint/WAFingerprint.proto

package waFingerprint

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HostedState int32

const (
	HostedState_E2EE   HostedState = 0
	HostedState_HOSTED HostedState = 1
)

// Enum value maps for HostedState.
var (
	HostedState_name = map[int32]string{
		0: "E2EE",
		1: "HOSTED",
	}
	HostedState_value = map[string]int32{
		"E2EE":   0,
		"HOSTED": 1,
	}
)

func (x HostedState) Enum() *HostedState {
	p := new(HostedState)
	*p = x
	return p
}

func (x HostedState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HostedState) Descriptor() protoreflect.EnumDescriptor {
	return file_waFingerprint_WAFingerprint_proto_enumTypes[0].Descriptor()
}

func (HostedState) Type() protoreflect.EnumType {
	return &file_waFingerprint_WAFingerprint_proto_enumTypes[0]
}

func (x HostedState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *HostedState) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = HostedState(num)
	return nil
}

// Deprecated: Use HostedState.Descriptor instead.
func (HostedState) EnumDescriptor() ([]byte, []int) {
	return file_waFingerprint_WAFingerprint_proto_rawDescGZIP(), []int{0}
}

type FingerprintData struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PublicKey          []byte                 `protobuf:"bytes,1,opt,name=publicKey" json:"publicKey,omitempty"`
	PnIdentifier       []byte                 `protobuf:"bytes,2,opt,name=pnIdentifier" json:"pnIdentifier,omitempty"`
	LidIdentifier      []byte                 `protobuf:"bytes,3,opt,name=lidIdentifier" json:"lidIdentifier,omitempty"`
	UsernameIdentifier []byte                 `protobuf:"bytes,4,opt,name=usernameIdentifier" json:"usernameIdentifier,omitempty"`
	HostedState        *HostedState           `protobuf:"varint,5,opt,name=hostedState,enum=WAFingerprint.HostedState" json:"hostedState,omitempty"`
	HashedPublicKey    []byte                 `protobuf:"bytes,6,opt,name=hashedPublicKey" json:"hashedPublicKey,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *FingerprintData) Reset() {
	*x = FingerprintData{}
	mi := &file_waFingerprint_WAFingerprint_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FingerprintData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FingerprintData) ProtoMessage() {}

func (x *FingerprintData) ProtoReflect() protoreflect.Message {
	mi := &file_waFingerprint_WAFingerprint_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FingerprintData.ProtoReflect.Descriptor instead.
func (*FingerprintData) Descriptor() ([]byte, []int) {
	return file_waFingerprint_WAFingerprint_proto_rawDescGZIP(), []int{0}
}

func (x *FingerprintData) GetPublicKey() []byte {
	if x != nil {
		return x.PublicKey
	}
	return nil
}

func (x *FingerprintData) GetPnIdentifier() []byte {
	if x != nil {
		return x.PnIdentifier
	}
	return nil
}

func (x *FingerprintData) GetLidIdentifier() []byte {
	if x != nil {
		return x.LidIdentifier
	}
	return nil
}

func (x *FingerprintData) GetUsernameIdentifier() []byte {
	if x != nil {
		return x.UsernameIdentifier
	}
	return nil
}

func (x *FingerprintData) GetHostedState() HostedState {
	if x != nil && x.HostedState != nil {
		return *x.HostedState
	}
	return HostedState_E2EE
}

func (x *FingerprintData) GetHashedPublicKey() []byte {
	if x != nil {
		return x.HashedPublicKey
	}
	return nil
}

type CombinedFingerprint struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Version           *uint32                `protobuf:"varint,1,opt,name=version" json:"version,omitempty"`
	LocalFingerprint  *FingerprintData       `protobuf:"bytes,2,opt,name=localFingerprint" json:"localFingerprint,omitempty"`
	RemoteFingerprint *FingerprintData       `protobuf:"bytes,3,opt,name=remoteFingerprint" json:"remoteFingerprint,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CombinedFingerprint) Reset() {
	*x = CombinedFingerprint{}
	mi := &file_waFingerprint_WAFingerprint_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CombinedFingerprint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CombinedFingerprint) ProtoMessage() {}

func (x *CombinedFingerprint) ProtoReflect() protoreflect.Message {
	mi := &file_waFingerprint_WAFingerprint_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CombinedFingerprint.ProtoReflect.Descriptor instead.
func (*CombinedFingerprint) Descriptor() ([]byte, []int) {
	return file_waFingerprint_WAFingerprint_proto_rawDescGZIP(), []int{1}
}

func (x *CombinedFingerprint) GetVersion() uint32 {
	if x != nil && x.Version != nil {
		return *x.Version
	}
	return 0
}

func (x *CombinedFingerprint) GetLocalFingerprint() *FingerprintData {
	if x != nil {
		return x.LocalFingerprint
	}
	return nil
}

func (x *CombinedFingerprint) GetRemoteFingerprint() *FingerprintData {
	if x != nil {
		return x.RemoteFingerprint
	}
	return nil
}

var File_waFingerprint_WAFingerprint_proto protoreflect.FileDescriptor

const file_waFingerprint_WAFingerprint_proto_rawDesc = "" +
	"\n" +
	"!waFingerprint/WAFingerprint.proto\x12\rWAFingerprint\"\x91\x02\n" +
	"\x0fFingerprintData\x12\x1c\n" +
	"\tpublicKey\x18\x01 \x01(\fR\tpublicKey\x12\"\n" +
	"\fpnIdentifier\x18\x02 \x01(\fR\fpnIdentifier\x12$\n" +
	"\rlidIdentifier\x18\x03 \x01(\fR\rlidIdentifier\x12.\n" +
	"\x12usernameIdentifier\x18\x04 \x01(\fR\x12usernameIdentifier\x12<\n" +
	"\vhostedState\x18\x05 \x01(\x0e2\x1a.WAFingerprint.HostedStateR\vhostedState\x12(\n" +
	"\x0fhashedPublicKey\x18\x06 \x01(\fR\x0fhashedPublicKey\"\xc9\x01\n" +
	"\x13CombinedFingerprint\x12\x18\n" +
	"\aversion\x18\x01 \x01(\rR\aversion\x12J\n" +
	"\x10localFingerprint\x18\x02 \x01(\v2\x1e.WAFingerprint.FingerprintDataR\x10localFingerprint\x12L\n" +
	"\x11remoteFingerprint\x18\x03 \x01(\v2\x1e.WAFingerprint.FingerprintDataR\x11remoteFingerprint*#\n" +
	"\vHostedState\x12\b\n" +
	"\x04E2EE\x10\x00\x12\n" +
	"\n" +
	"\x06HOSTED\x10\x01B)Z'go.mau.fi/whatsmeow/proto/waFingerprint"

var (
	file_waFingerprint_WAFingerprint_proto_rawDescOnce sync.Once
	file_waFingerprint_WAFingerprint_proto_rawDescData []byte
)

func file_waFingerprint_WAFingerprint_proto_rawDescGZIP() []byte {
	file_waFingerprint_WAFingerprint_proto_rawDescOnce.Do(func() {
		file_waFingerprint_WAFingerprint_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waFingerprint_WAFingerprint_proto_rawDesc), len(file_waFingerprint_WAFingerprint_proto_rawDesc)))
	})
	return file_waFingerprint_WAFingerprint_proto_rawDescData
}

var file_waFingerprint_WAFingerprint_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_waFingerprint_WAFingerprint_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_waFingerprint_WAFingerprint_proto_goTypes = []any{
	(HostedState)(0),            // 0: WAFingerprint.HostedState
	(*FingerprintData)(nil),     // 1: WAFingerprint.FingerprintData
	(*CombinedFingerprint)(nil), // 2: WAFingerprint.CombinedFingerprint
}
var file_waFingerprint_WAFingerprint_proto_depIdxs = []int32{
	0, // 0: WAFingerprint.FingerprintData.hostedState:type_name -> WAFingerprint.HostedState
	1, // 1: WAFingerprint.CombinedFingerprint.localFingerprint:type_name -> WAFingerprint.FingerprintData
	1, // 2: WAFingerprint.CombinedFingerprint.remoteFingerprint:type_name -> WAFingerprint.FingerprintData
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_waFingerprint_WAFingerprint_proto_init() }
func file_waFingerprint_WAFingerprint_proto_init() {
	if File_waFingerprint_WAFingerprint_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waFingerprint_WAFingerprint_proto_rawDesc), len(file_waFingerprint_WAFingerprint_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waFingerprint_WAFingerprint_proto_goTypes,
		DependencyIndexes: file_waFingerprint_WAFingerprint_proto_depIdxs,
		EnumInfos:         file_waFingerprint_WAFingerprint_proto_enumTypes,
		MessageInfos:      file_waFingerprint_WAFingerprint_proto_msgTypes,
	}.Build()
	File_waFingerprint_WAFingerprint_proto = out.File
	file_waFingerprint_WAFingerprint_proto_goTypes = nil
	file_waFingerprint_WAFingerprint_proto_depIdxs = nil
}
