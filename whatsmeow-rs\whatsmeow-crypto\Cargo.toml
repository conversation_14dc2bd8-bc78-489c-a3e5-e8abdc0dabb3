[package]
name = "whatsmeow-crypto"
version = "0.1.0"
edition = "2021"
description = "Cryptographic operations for WhatsApp Web API (Signal protocol, Noise protocol)"
license = "MIT"

[dependencies]
whatsmeow-types = { path = "../whatsmeow-types" }

ring = { workspace = true }
ed25519-dalek = { workspace = true }
x25519-dalek = { workspace = true }
aes-gcm = { workspace = true }
hkdf = { workspace = true }
thiserror = { workspace = true }
bytes = { workspace = true }
async-trait = { workspace = true }

[dev-dependencies]
tokio-test = { workspace = true }
proptest = { workspace = true }