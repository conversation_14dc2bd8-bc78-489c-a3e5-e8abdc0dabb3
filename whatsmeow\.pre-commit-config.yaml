repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        exclude_types: [markdown]
        exclude: LICENSE
      - id: end-of-file-fixer
        exclude: LICENSE
      - id: check-yaml
      - id: check-added-large-files

  - repo: https://github.com/tekwizely/pre-commit-golang
    rev: v1.0.0-rc.1
    hooks:
      - id: go-imports-repo
        args:
          - "-local"
          - "go.mau.fi/whatsmeow"
          - "-w"
      - id: go-vet-repo-mod
      # TODO enable this
      #- id: go-staticcheck-repo-mod
      - id: go-mod-tidy

  - repo: https://github.com/beeper/pre-commit-go
    rev: v0.3.1
    hooks:
      # TODO enable this
      #- id: zerolog-ban-msgf
      - id: zerolog-use-stringer
