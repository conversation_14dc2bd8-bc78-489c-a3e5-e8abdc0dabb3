// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: instamadilloDeleteMessage/InstamadilloDeleteMessage.proto

package instamadilloDeleteMessage

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeleteMessagePayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MessageOtid   *string                `protobuf:"bytes,1,opt,name=messageOtid" json:"messageOtid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMessagePayload) Reset() {
	*x = DeleteMessagePayload{}
	mi := &file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMessagePayload) ProtoMessage() {}

func (x *DeleteMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMessagePayload.ProtoReflect.Descriptor instead.
func (*DeleteMessagePayload) Descriptor() ([]byte, []int) {
	return file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDescGZIP(), []int{0}
}

func (x *DeleteMessagePayload) GetMessageOtid() string {
	if x != nil && x.MessageOtid != nil {
		return *x.MessageOtid
	}
	return ""
}

var File_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto protoreflect.FileDescriptor

const file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDesc = "" +
	"\n" +
	"9instamadilloDeleteMessage/InstamadilloDeleteMessage.proto\x12\x19InstamadilloDeleteMessage\"8\n" +
	"\x14DeleteMessagePayload\x12 \n" +
	"\vmessageOtid\x18\x01 \x01(\tR\vmessageOtidB5Z3go.mau.fi/whatsmeow/proto/instamadilloDeleteMessage"

var (
	file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDescOnce sync.Once
	file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDescData []byte
)

func file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDescGZIP() []byte {
	file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDescOnce.Do(func() {
		file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDesc), len(file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDesc)))
	})
	return file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDescData
}

var file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_goTypes = []any{
	(*DeleteMessagePayload)(nil), // 0: InstamadilloDeleteMessage.DeleteMessagePayload
}
var file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_init() }
func file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_init() {
	if File_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDesc), len(file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_goTypes,
		DependencyIndexes: file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_depIdxs,
		MessageInfos:      file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_msgTypes,
	}.Build()
	File_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto = out.File
	file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_goTypes = nil
	file_instamadilloDeleteMessage_InstamadilloDeleteMessage_proto_depIdxs = nil
}
