//! Binary node serializer

use crate::error::BinaryError;
use crate::node::Node;

/// Binary node serializer
pub struct Serializer {
    buffer: Vec<u8>,
}

impl Serializer {
    /// Create a new serializer
    pub fn new() -> Self {
        Self { buffer: Vec::new() }
    }

    /// Serialize a node to bytes
    pub fn serialize_node(&mut self, _node: &Node) -> Result<(), BinaryError> {
        // TODO: Implement binary node serialization logic
        Err(BinaryError::SerializationFailed(
            "Serializer not implemented".to_string(),
        ))
    }

    /// Get the serialized bytes
    pub fn into_bytes(self) -> Vec<u8> {
        self.buffer
    }

    /// Write a byte to the buffer
    fn write_byte(&mut self, byte: u8) {
        self.buffer.push(byte);
    }

    /// Write multiple bytes to the buffer
    fn write_bytes(&mut self, bytes: &[u8]) {
        self.buffer.extend_from_slice(bytes);
    }
}

impl Default for Serializer {
    fn default() -> Self {
        Self::new()
    }
}
