[package]
name = "whatsmeow-types"
version = "0.1.0"
edition = "2021"
description = "Common types and utilities for WhatsApp Web API"
license = "MIT"

[dependencies]
thiserror = { workspace = true }
chrono = { workspace = true, features = ["serde"] }
uuid = { workspace = true, features = ["serde"] }
base64 = { workspace = true }
hex = { workspace = true }
serde = { version = "1.0", features = ["derive"] }
rand = "0.8"

[dev-dependencies]
proptest = { workspace = true }
serde_json = "1.0"