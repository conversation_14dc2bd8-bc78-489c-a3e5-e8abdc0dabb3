//! Binary parsing error types

use thiserror::Error;

/// Errors that can occur during binary parsing
#[derive(Debug, Error)]
pub enum BinaryError {
    #[error("Invalid node format: {0}")]
    InvalidNodeFormat(String),

    #[error("Unexpected end of data")]
    UnexpectedEndOfData,

    #[error("Invalid attribute: {0}")]
    InvalidAttribute(String),

    #[error("Serialization failed: {0}")]
    SerializationFailed(String),

    #[error("Deserialization failed: {0}")]
    DeserializationFailed(String),

    #[error("Invalid tag: {0}")]
    InvalidTag(u8),
}
