// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waChatLockSettings/WAProtobufsChatLockSettings.proto

package waChatLockSettings

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	waUserPassword "go.mau.fi/whatsmeow/proto/waUserPassword"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChatLockSettings struct {
	state           protoimpl.MessageState       `protogen:"open.v1"`
	HideLockedChats *bool                        `protobuf:"varint,1,opt,name=hideLockedChats" json:"hideLockedChats,omitempty"`
	SecretCode      *waUserPassword.UserPassword `protobuf:"bytes,2,opt,name=secretCode" json:"secretCode,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ChatLockSettings) Reset() {
	*x = ChatLockSettings{}
	mi := &file_waChatLockSettings_WAProtobufsChatLockSettings_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatLockSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatLockSettings) ProtoMessage() {}

func (x *ChatLockSettings) ProtoReflect() protoreflect.Message {
	mi := &file_waChatLockSettings_WAProtobufsChatLockSettings_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatLockSettings.ProtoReflect.Descriptor instead.
func (*ChatLockSettings) Descriptor() ([]byte, []int) {
	return file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDescGZIP(), []int{0}
}

func (x *ChatLockSettings) GetHideLockedChats() bool {
	if x != nil && x.HideLockedChats != nil {
		return *x.HideLockedChats
	}
	return false
}

func (x *ChatLockSettings) GetSecretCode() *waUserPassword.UserPassword {
	if x != nil {
		return x.SecretCode
	}
	return nil
}

var File_waChatLockSettings_WAProtobufsChatLockSettings_proto protoreflect.FileDescriptor

const file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDesc = "" +
	"\n" +
	"4waChatLockSettings/WAProtobufsChatLockSettings.proto\x12\x1bWAProtobufsChatLockSettings\x1a,waUserPassword/WAProtobufsUserPassword.proto\"\x83\x01\n" +
	"\x10ChatLockSettings\x12(\n" +
	"\x0fhideLockedChats\x18\x01 \x01(\bR\x0fhideLockedChats\x12E\n" +
	"\n" +
	"secretCode\x18\x02 \x01(\v2%.WAProtobufsUserPassword.UserPasswordR\n" +
	"secretCodeB.Z,go.mau.fi/whatsmeow/proto/waChatLockSettings"

var (
	file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDescOnce sync.Once
	file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDescData []byte
)

func file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDescGZIP() []byte {
	file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDescOnce.Do(func() {
		file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDesc), len(file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDesc)))
	})
	return file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDescData
}

var file_waChatLockSettings_WAProtobufsChatLockSettings_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_waChatLockSettings_WAProtobufsChatLockSettings_proto_goTypes = []any{
	(*ChatLockSettings)(nil),            // 0: WAProtobufsChatLockSettings.ChatLockSettings
	(*waUserPassword.UserPassword)(nil), // 1: WAProtobufsUserPassword.UserPassword
}
var file_waChatLockSettings_WAProtobufsChatLockSettings_proto_depIdxs = []int32{
	1, // 0: WAProtobufsChatLockSettings.ChatLockSettings.secretCode:type_name -> WAProtobufsUserPassword.UserPassword
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_waChatLockSettings_WAProtobufsChatLockSettings_proto_init() }
func file_waChatLockSettings_WAProtobufsChatLockSettings_proto_init() {
	if File_waChatLockSettings_WAProtobufsChatLockSettings_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDesc), len(file_waChatLockSettings_WAProtobufsChatLockSettings_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waChatLockSettings_WAProtobufsChatLockSettings_proto_goTypes,
		DependencyIndexes: file_waChatLockSettings_WAProtobufsChatLockSettings_proto_depIdxs,
		MessageInfos:      file_waChatLockSettings_WAProtobufsChatLockSettings_proto_msgTypes,
	}.Build()
	File_waChatLockSettings_WAProtobufsChatLockSettings_proto = out.File
	file_waChatLockSettings_WAProtobufsChatLockSettings_proto_goTypes = nil
	file_waChatLockSettings_WAProtobufsChatLockSettings_proto_depIdxs = nil
}
