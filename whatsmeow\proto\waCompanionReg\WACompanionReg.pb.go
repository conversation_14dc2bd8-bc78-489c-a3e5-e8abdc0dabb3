// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: waCompanionReg/WACompanionReg.proto

package waCompanionReg

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeviceProps_PlatformType int32

const (
	DeviceProps_UNKNOWN           DeviceProps_PlatformType = 0
	DeviceProps_CHROME            DeviceProps_PlatformType = 1
	DeviceProps_FIREFOX           DeviceProps_PlatformType = 2
	DeviceProps_IE                DeviceProps_PlatformType = 3
	DeviceProps_OPERA             DeviceProps_PlatformType = 4
	DeviceProps_SAFARI            DeviceProps_PlatformType = 5
	DeviceProps_EDGE              DeviceProps_PlatformType = 6
	DeviceProps_DESKTOP           DeviceProps_PlatformType = 7
	DeviceProps_IPAD              DeviceProps_PlatformType = 8
	DeviceProps_ANDROID_TABLET    DeviceProps_PlatformType = 9
	DeviceProps_OHANA             DeviceProps_PlatformType = 10
	DeviceProps_ALOHA             DeviceProps_PlatformType = 11
	DeviceProps_CATALINA          DeviceProps_PlatformType = 12
	DeviceProps_TCL_TV            DeviceProps_PlatformType = 13
	DeviceProps_IOS_PHONE         DeviceProps_PlatformType = 14
	DeviceProps_IOS_CATALYST      DeviceProps_PlatformType = 15
	DeviceProps_ANDROID_PHONE     DeviceProps_PlatformType = 16
	DeviceProps_ANDROID_AMBIGUOUS DeviceProps_PlatformType = 17
	DeviceProps_WEAR_OS           DeviceProps_PlatformType = 18
	DeviceProps_AR_WRIST          DeviceProps_PlatformType = 19
	DeviceProps_AR_DEVICE         DeviceProps_PlatformType = 20
	DeviceProps_UWP               DeviceProps_PlatformType = 21
	DeviceProps_VR                DeviceProps_PlatformType = 22
	DeviceProps_CLOUD_API         DeviceProps_PlatformType = 23
	DeviceProps_SMARTGLASSES      DeviceProps_PlatformType = 24
)

// Enum value maps for DeviceProps_PlatformType.
var (
	DeviceProps_PlatformType_name = map[int32]string{
		0:  "UNKNOWN",
		1:  "CHROME",
		2:  "FIREFOX",
		3:  "IE",
		4:  "OPERA",
		5:  "SAFARI",
		6:  "EDGE",
		7:  "DESKTOP",
		8:  "IPAD",
		9:  "ANDROID_TABLET",
		10: "OHANA",
		11: "ALOHA",
		12: "CATALINA",
		13: "TCL_TV",
		14: "IOS_PHONE",
		15: "IOS_CATALYST",
		16: "ANDROID_PHONE",
		17: "ANDROID_AMBIGUOUS",
		18: "WEAR_OS",
		19: "AR_WRIST",
		20: "AR_DEVICE",
		21: "UWP",
		22: "VR",
		23: "CLOUD_API",
		24: "SMARTGLASSES",
	}
	DeviceProps_PlatformType_value = map[string]int32{
		"UNKNOWN":           0,
		"CHROME":            1,
		"FIREFOX":           2,
		"IE":                3,
		"OPERA":             4,
		"SAFARI":            5,
		"EDGE":              6,
		"DESKTOP":           7,
		"IPAD":              8,
		"ANDROID_TABLET":    9,
		"OHANA":             10,
		"ALOHA":             11,
		"CATALINA":          12,
		"TCL_TV":            13,
		"IOS_PHONE":         14,
		"IOS_CATALYST":      15,
		"ANDROID_PHONE":     16,
		"ANDROID_AMBIGUOUS": 17,
		"WEAR_OS":           18,
		"AR_WRIST":          19,
		"AR_DEVICE":         20,
		"UWP":               21,
		"VR":                22,
		"CLOUD_API":         23,
		"SMARTGLASSES":      24,
	}
)

func (x DeviceProps_PlatformType) Enum() *DeviceProps_PlatformType {
	p := new(DeviceProps_PlatformType)
	*p = x
	return p
}

func (x DeviceProps_PlatformType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceProps_PlatformType) Descriptor() protoreflect.EnumDescriptor {
	return file_waCompanionReg_WACompanionReg_proto_enumTypes[0].Descriptor()
}

func (DeviceProps_PlatformType) Type() protoreflect.EnumType {
	return &file_waCompanionReg_WACompanionReg_proto_enumTypes[0]
}

func (x DeviceProps_PlatformType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *DeviceProps_PlatformType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = DeviceProps_PlatformType(num)
	return nil
}

// Deprecated: Use DeviceProps_PlatformType.Descriptor instead.
func (DeviceProps_PlatformType) EnumDescriptor() ([]byte, []int) {
	return file_waCompanionReg_WACompanionReg_proto_rawDescGZIP(), []int{0, 0}
}

type DeviceProps struct {
	state             protoimpl.MessageState         `protogen:"open.v1"`
	Os                *string                        `protobuf:"bytes,1,opt,name=os" json:"os,omitempty"`
	Version           *DeviceProps_AppVersion        `protobuf:"bytes,2,opt,name=version" json:"version,omitempty"`
	PlatformType      *DeviceProps_PlatformType      `protobuf:"varint,3,opt,name=platformType,enum=WACompanionReg.DeviceProps_PlatformType" json:"platformType,omitempty"`
	RequireFullSync   *bool                          `protobuf:"varint,4,opt,name=requireFullSync" json:"requireFullSync,omitempty"`
	HistorySyncConfig *DeviceProps_HistorySyncConfig `protobuf:"bytes,5,opt,name=historySyncConfig" json:"historySyncConfig,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *DeviceProps) Reset() {
	*x = DeviceProps{}
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceProps) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceProps) ProtoMessage() {}

func (x *DeviceProps) ProtoReflect() protoreflect.Message {
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceProps.ProtoReflect.Descriptor instead.
func (*DeviceProps) Descriptor() ([]byte, []int) {
	return file_waCompanionReg_WACompanionReg_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceProps) GetOs() string {
	if x != nil && x.Os != nil {
		return *x.Os
	}
	return ""
}

func (x *DeviceProps) GetVersion() *DeviceProps_AppVersion {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *DeviceProps) GetPlatformType() DeviceProps_PlatformType {
	if x != nil && x.PlatformType != nil {
		return *x.PlatformType
	}
	return DeviceProps_UNKNOWN
}

func (x *DeviceProps) GetRequireFullSync() bool {
	if x != nil && x.RequireFullSync != nil {
		return *x.RequireFullSync
	}
	return false
}

func (x *DeviceProps) GetHistorySyncConfig() *DeviceProps_HistorySyncConfig {
	if x != nil {
		return x.HistorySyncConfig
	}
	return nil
}

type CompanionEphemeralIdentity struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	PublicKey     []byte                    `protobuf:"bytes,1,opt,name=publicKey" json:"publicKey,omitempty"`
	DeviceType    *DeviceProps_PlatformType `protobuf:"varint,2,opt,name=deviceType,enum=WACompanionReg.DeviceProps_PlatformType" json:"deviceType,omitempty"`
	Ref           *string                   `protobuf:"bytes,3,opt,name=ref" json:"ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CompanionEphemeralIdentity) Reset() {
	*x = CompanionEphemeralIdentity{}
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompanionEphemeralIdentity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanionEphemeralIdentity) ProtoMessage() {}

func (x *CompanionEphemeralIdentity) ProtoReflect() protoreflect.Message {
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanionEphemeralIdentity.ProtoReflect.Descriptor instead.
func (*CompanionEphemeralIdentity) Descriptor() ([]byte, []int) {
	return file_waCompanionReg_WACompanionReg_proto_rawDescGZIP(), []int{1}
}

func (x *CompanionEphemeralIdentity) GetPublicKey() []byte {
	if x != nil {
		return x.PublicKey
	}
	return nil
}

func (x *CompanionEphemeralIdentity) GetDeviceType() DeviceProps_PlatformType {
	if x != nil && x.DeviceType != nil {
		return *x.DeviceType
	}
	return DeviceProps_UNKNOWN
}

func (x *CompanionEphemeralIdentity) GetRef() string {
	if x != nil && x.Ref != nil {
		return *x.Ref
	}
	return ""
}

type CompanionCommitment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hash          []byte                 `protobuf:"bytes,1,opt,name=hash" json:"hash,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CompanionCommitment) Reset() {
	*x = CompanionCommitment{}
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompanionCommitment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanionCommitment) ProtoMessage() {}

func (x *CompanionCommitment) ProtoReflect() protoreflect.Message {
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanionCommitment.ProtoReflect.Descriptor instead.
func (*CompanionCommitment) Descriptor() ([]byte, []int) {
	return file_waCompanionReg_WACompanionReg_proto_rawDescGZIP(), []int{2}
}

func (x *CompanionCommitment) GetHash() []byte {
	if x != nil {
		return x.Hash
	}
	return nil
}

type ProloguePayload struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	CompanionEphemeralIdentity []byte                 `protobuf:"bytes,1,opt,name=companionEphemeralIdentity" json:"companionEphemeralIdentity,omitempty"`
	Commitment                 *CompanionCommitment   `protobuf:"bytes,2,opt,name=commitment" json:"commitment,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *ProloguePayload) Reset() {
	*x = ProloguePayload{}
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProloguePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProloguePayload) ProtoMessage() {}

func (x *ProloguePayload) ProtoReflect() protoreflect.Message {
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProloguePayload.ProtoReflect.Descriptor instead.
func (*ProloguePayload) Descriptor() ([]byte, []int) {
	return file_waCompanionReg_WACompanionReg_proto_rawDescGZIP(), []int{3}
}

func (x *ProloguePayload) GetCompanionEphemeralIdentity() []byte {
	if x != nil {
		return x.CompanionEphemeralIdentity
	}
	return nil
}

func (x *ProloguePayload) GetCommitment() *CompanionCommitment {
	if x != nil {
		return x.Commitment
	}
	return nil
}

type PrimaryEphemeralIdentity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PublicKey     []byte                 `protobuf:"bytes,1,opt,name=publicKey" json:"publicKey,omitempty"`
	Nonce         []byte                 `protobuf:"bytes,2,opt,name=nonce" json:"nonce,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrimaryEphemeralIdentity) Reset() {
	*x = PrimaryEphemeralIdentity{}
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrimaryEphemeralIdentity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrimaryEphemeralIdentity) ProtoMessage() {}

func (x *PrimaryEphemeralIdentity) ProtoReflect() protoreflect.Message {
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrimaryEphemeralIdentity.ProtoReflect.Descriptor instead.
func (*PrimaryEphemeralIdentity) Descriptor() ([]byte, []int) {
	return file_waCompanionReg_WACompanionReg_proto_rawDescGZIP(), []int{4}
}

func (x *PrimaryEphemeralIdentity) GetPublicKey() []byte {
	if x != nil {
		return x.PublicKey
	}
	return nil
}

func (x *PrimaryEphemeralIdentity) GetNonce() []byte {
	if x != nil {
		return x.Nonce
	}
	return nil
}

type PairingRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	CompanionPublicKey   []byte                 `protobuf:"bytes,1,opt,name=companionPublicKey" json:"companionPublicKey,omitempty"`
	CompanionIdentityKey []byte                 `protobuf:"bytes,2,opt,name=companionIdentityKey" json:"companionIdentityKey,omitempty"`
	AdvSecret            []byte                 `protobuf:"bytes,3,opt,name=advSecret" json:"advSecret,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PairingRequest) Reset() {
	*x = PairingRequest{}
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PairingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PairingRequest) ProtoMessage() {}

func (x *PairingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PairingRequest.ProtoReflect.Descriptor instead.
func (*PairingRequest) Descriptor() ([]byte, []int) {
	return file_waCompanionReg_WACompanionReg_proto_rawDescGZIP(), []int{5}
}

func (x *PairingRequest) GetCompanionPublicKey() []byte {
	if x != nil {
		return x.CompanionPublicKey
	}
	return nil
}

func (x *PairingRequest) GetCompanionIdentityKey() []byte {
	if x != nil {
		return x.CompanionIdentityKey
	}
	return nil
}

func (x *PairingRequest) GetAdvSecret() []byte {
	if x != nil {
		return x.AdvSecret
	}
	return nil
}

type EncryptedPairingRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	EncryptedPayload []byte                 `protobuf:"bytes,1,opt,name=encryptedPayload" json:"encryptedPayload,omitempty"`
	IV               []byte                 `protobuf:"bytes,2,opt,name=IV" json:"IV,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *EncryptedPairingRequest) Reset() {
	*x = EncryptedPairingRequest{}
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EncryptedPairingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EncryptedPairingRequest) ProtoMessage() {}

func (x *EncryptedPairingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EncryptedPairingRequest.ProtoReflect.Descriptor instead.
func (*EncryptedPairingRequest) Descriptor() ([]byte, []int) {
	return file_waCompanionReg_WACompanionReg_proto_rawDescGZIP(), []int{6}
}

func (x *EncryptedPairingRequest) GetEncryptedPayload() []byte {
	if x != nil {
		return x.EncryptedPayload
	}
	return nil
}

func (x *EncryptedPairingRequest) GetIV() []byte {
	if x != nil {
		return x.IV
	}
	return nil
}

type ClientPairingProps struct {
	state                          protoimpl.MessageState `protogen:"open.v1"`
	IsChatDbLidMigrated            *bool                  `protobuf:"varint,1,opt,name=isChatDbLidMigrated" json:"isChatDbLidMigrated,omitempty"`
	IsSyncdPureLidSession          *bool                  `protobuf:"varint,2,opt,name=isSyncdPureLidSession" json:"isSyncdPureLidSession,omitempty"`
	IsSyncdSnapshotRecoveryEnabled *bool                  `protobuf:"varint,3,opt,name=isSyncdSnapshotRecoveryEnabled" json:"isSyncdSnapshotRecoveryEnabled,omitempty"`
	unknownFields                  protoimpl.UnknownFields
	sizeCache                      protoimpl.SizeCache
}

func (x *ClientPairingProps) Reset() {
	*x = ClientPairingProps{}
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientPairingProps) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientPairingProps) ProtoMessage() {}

func (x *ClientPairingProps) ProtoReflect() protoreflect.Message {
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientPairingProps.ProtoReflect.Descriptor instead.
func (*ClientPairingProps) Descriptor() ([]byte, []int) {
	return file_waCompanionReg_WACompanionReg_proto_rawDescGZIP(), []int{7}
}

func (x *ClientPairingProps) GetIsChatDbLidMigrated() bool {
	if x != nil && x.IsChatDbLidMigrated != nil {
		return *x.IsChatDbLidMigrated
	}
	return false
}

func (x *ClientPairingProps) GetIsSyncdPureLidSession() bool {
	if x != nil && x.IsSyncdPureLidSession != nil {
		return *x.IsSyncdPureLidSession
	}
	return false
}

func (x *ClientPairingProps) GetIsSyncdSnapshotRecoveryEnabled() bool {
	if x != nil && x.IsSyncdSnapshotRecoveryEnabled != nil {
		return *x.IsSyncdSnapshotRecoveryEnabled
	}
	return false
}

type DeviceProps_HistorySyncConfig struct {
	state                                    protoimpl.MessageState `protogen:"open.v1"`
	FullSyncDaysLimit                        *uint32                `protobuf:"varint,1,opt,name=fullSyncDaysLimit" json:"fullSyncDaysLimit,omitempty"`
	FullSyncSizeMbLimit                      *uint32                `protobuf:"varint,2,opt,name=fullSyncSizeMbLimit" json:"fullSyncSizeMbLimit,omitempty"`
	StorageQuotaMb                           *uint32                `protobuf:"varint,3,opt,name=storageQuotaMb" json:"storageQuotaMb,omitempty"`
	InlineInitialPayloadInE2EeMsg            *bool                  `protobuf:"varint,4,opt,name=inlineInitialPayloadInE2EeMsg" json:"inlineInitialPayloadInE2EeMsg,omitempty"`
	RecentSyncDaysLimit                      *uint32                `protobuf:"varint,5,opt,name=recentSyncDaysLimit" json:"recentSyncDaysLimit,omitempty"`
	SupportCallLogHistory                    *bool                  `protobuf:"varint,6,opt,name=supportCallLogHistory" json:"supportCallLogHistory,omitempty"`
	SupportBotUserAgentChatHistory           *bool                  `protobuf:"varint,7,opt,name=supportBotUserAgentChatHistory" json:"supportBotUserAgentChatHistory,omitempty"`
	SupportCagReactionsAndPolls              *bool                  `protobuf:"varint,8,opt,name=supportCagReactionsAndPolls" json:"supportCagReactionsAndPolls,omitempty"`
	SupportBizHostedMsg                      *bool                  `protobuf:"varint,9,opt,name=supportBizHostedMsg" json:"supportBizHostedMsg,omitempty"`
	SupportRecentSyncChunkMessageCountTuning *bool                  `protobuf:"varint,10,opt,name=supportRecentSyncChunkMessageCountTuning" json:"supportRecentSyncChunkMessageCountTuning,omitempty"`
	SupportHostedGroupMsg                    *bool                  `protobuf:"varint,11,opt,name=supportHostedGroupMsg" json:"supportHostedGroupMsg,omitempty"`
	SupportFbidBotChatHistory                *bool                  `protobuf:"varint,12,opt,name=supportFbidBotChatHistory" json:"supportFbidBotChatHistory,omitempty"`
	SupportAddOnHistorySyncMigration         *bool                  `protobuf:"varint,13,opt,name=supportAddOnHistorySyncMigration" json:"supportAddOnHistorySyncMigration,omitempty"`
	SupportMessageAssociation                *bool                  `protobuf:"varint,14,opt,name=supportMessageAssociation" json:"supportMessageAssociation,omitempty"`
	unknownFields                            protoimpl.UnknownFields
	sizeCache                                protoimpl.SizeCache
}

func (x *DeviceProps_HistorySyncConfig) Reset() {
	*x = DeviceProps_HistorySyncConfig{}
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceProps_HistorySyncConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceProps_HistorySyncConfig) ProtoMessage() {}

func (x *DeviceProps_HistorySyncConfig) ProtoReflect() protoreflect.Message {
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceProps_HistorySyncConfig.ProtoReflect.Descriptor instead.
func (*DeviceProps_HistorySyncConfig) Descriptor() ([]byte, []int) {
	return file_waCompanionReg_WACompanionReg_proto_rawDescGZIP(), []int{0, 0}
}

func (x *DeviceProps_HistorySyncConfig) GetFullSyncDaysLimit() uint32 {
	if x != nil && x.FullSyncDaysLimit != nil {
		return *x.FullSyncDaysLimit
	}
	return 0
}

func (x *DeviceProps_HistorySyncConfig) GetFullSyncSizeMbLimit() uint32 {
	if x != nil && x.FullSyncSizeMbLimit != nil {
		return *x.FullSyncSizeMbLimit
	}
	return 0
}

func (x *DeviceProps_HistorySyncConfig) GetStorageQuotaMb() uint32 {
	if x != nil && x.StorageQuotaMb != nil {
		return *x.StorageQuotaMb
	}
	return 0
}

func (x *DeviceProps_HistorySyncConfig) GetInlineInitialPayloadInE2EeMsg() bool {
	if x != nil && x.InlineInitialPayloadInE2EeMsg != nil {
		return *x.InlineInitialPayloadInE2EeMsg
	}
	return false
}

func (x *DeviceProps_HistorySyncConfig) GetRecentSyncDaysLimit() uint32 {
	if x != nil && x.RecentSyncDaysLimit != nil {
		return *x.RecentSyncDaysLimit
	}
	return 0
}

func (x *DeviceProps_HistorySyncConfig) GetSupportCallLogHistory() bool {
	if x != nil && x.SupportCallLogHistory != nil {
		return *x.SupportCallLogHistory
	}
	return false
}

func (x *DeviceProps_HistorySyncConfig) GetSupportBotUserAgentChatHistory() bool {
	if x != nil && x.SupportBotUserAgentChatHistory != nil {
		return *x.SupportBotUserAgentChatHistory
	}
	return false
}

func (x *DeviceProps_HistorySyncConfig) GetSupportCagReactionsAndPolls() bool {
	if x != nil && x.SupportCagReactionsAndPolls != nil {
		return *x.SupportCagReactionsAndPolls
	}
	return false
}

func (x *DeviceProps_HistorySyncConfig) GetSupportBizHostedMsg() bool {
	if x != nil && x.SupportBizHostedMsg != nil {
		return *x.SupportBizHostedMsg
	}
	return false
}

func (x *DeviceProps_HistorySyncConfig) GetSupportRecentSyncChunkMessageCountTuning() bool {
	if x != nil && x.SupportRecentSyncChunkMessageCountTuning != nil {
		return *x.SupportRecentSyncChunkMessageCountTuning
	}
	return false
}

func (x *DeviceProps_HistorySyncConfig) GetSupportHostedGroupMsg() bool {
	if x != nil && x.SupportHostedGroupMsg != nil {
		return *x.SupportHostedGroupMsg
	}
	return false
}

func (x *DeviceProps_HistorySyncConfig) GetSupportFbidBotChatHistory() bool {
	if x != nil && x.SupportFbidBotChatHistory != nil {
		return *x.SupportFbidBotChatHistory
	}
	return false
}

func (x *DeviceProps_HistorySyncConfig) GetSupportAddOnHistorySyncMigration() bool {
	if x != nil && x.SupportAddOnHistorySyncMigration != nil {
		return *x.SupportAddOnHistorySyncMigration
	}
	return false
}

func (x *DeviceProps_HistorySyncConfig) GetSupportMessageAssociation() bool {
	if x != nil && x.SupportMessageAssociation != nil {
		return *x.SupportMessageAssociation
	}
	return false
}

type DeviceProps_AppVersion struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Primary       *uint32                `protobuf:"varint,1,opt,name=primary" json:"primary,omitempty"`
	Secondary     *uint32                `protobuf:"varint,2,opt,name=secondary" json:"secondary,omitempty"`
	Tertiary      *uint32                `protobuf:"varint,3,opt,name=tertiary" json:"tertiary,omitempty"`
	Quaternary    *uint32                `protobuf:"varint,4,opt,name=quaternary" json:"quaternary,omitempty"`
	Quinary       *uint32                `protobuf:"varint,5,opt,name=quinary" json:"quinary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceProps_AppVersion) Reset() {
	*x = DeviceProps_AppVersion{}
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceProps_AppVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceProps_AppVersion) ProtoMessage() {}

func (x *DeviceProps_AppVersion) ProtoReflect() protoreflect.Message {
	mi := &file_waCompanionReg_WACompanionReg_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceProps_AppVersion.ProtoReflect.Descriptor instead.
func (*DeviceProps_AppVersion) Descriptor() ([]byte, []int) {
	return file_waCompanionReg_WACompanionReg_proto_rawDescGZIP(), []int{0, 1}
}

func (x *DeviceProps_AppVersion) GetPrimary() uint32 {
	if x != nil && x.Primary != nil {
		return *x.Primary
	}
	return 0
}

func (x *DeviceProps_AppVersion) GetSecondary() uint32 {
	if x != nil && x.Secondary != nil {
		return *x.Secondary
	}
	return 0
}

func (x *DeviceProps_AppVersion) GetTertiary() uint32 {
	if x != nil && x.Tertiary != nil {
		return *x.Tertiary
	}
	return 0
}

func (x *DeviceProps_AppVersion) GetQuaternary() uint32 {
	if x != nil && x.Quaternary != nil {
		return *x.Quaternary
	}
	return 0
}

func (x *DeviceProps_AppVersion) GetQuinary() uint32 {
	if x != nil && x.Quinary != nil {
		return *x.Quinary
	}
	return 0
}

var File_waCompanionReg_WACompanionReg_proto protoreflect.FileDescriptor

const file_waCompanionReg_WACompanionReg_proto_rawDesc = "" +
	"\n" +
	"#waCompanionReg/WACompanionReg.proto\x12\x0eWACompanionReg\"\x95\r\n" +
	"\vDeviceProps\x12\x0e\n" +
	"\x02os\x18\x01 \x01(\tR\x02os\x12@\n" +
	"\aversion\x18\x02 \x01(\v2&.WACompanionReg.DeviceProps.AppVersionR\aversion\x12L\n" +
	"\fplatformType\x18\x03 \x01(\x0e2(.WACompanionReg.DeviceProps.PlatformTypeR\fplatformType\x12(\n" +
	"\x0frequireFullSync\x18\x04 \x01(\bR\x0frequireFullSync\x12[\n" +
	"\x11historySyncConfig\x18\x05 \x01(\v2-.WACompanionReg.DeviceProps.HistorySyncConfigR\x11historySyncConfig\x1a\xdf\x06\n" +
	"\x11HistorySyncConfig\x12,\n" +
	"\x11fullSyncDaysLimit\x18\x01 \x01(\rR\x11fullSyncDaysLimit\x120\n" +
	"\x13fullSyncSizeMbLimit\x18\x02 \x01(\rR\x13fullSyncSizeMbLimit\x12&\n" +
	"\x0estorageQuotaMb\x18\x03 \x01(\rR\x0estorageQuotaMb\x12D\n" +
	"\x1dinlineInitialPayloadInE2EeMsg\x18\x04 \x01(\bR\x1dinlineInitialPayloadInE2EeMsg\x120\n" +
	"\x13recentSyncDaysLimit\x18\x05 \x01(\rR\x13recentSyncDaysLimit\x124\n" +
	"\x15supportCallLogHistory\x18\x06 \x01(\bR\x15supportCallLogHistory\x12F\n" +
	"\x1esupportBotUserAgentChatHistory\x18\a \x01(\bR\x1esupportBotUserAgentChatHistory\x12@\n" +
	"\x1bsupportCagReactionsAndPolls\x18\b \x01(\bR\x1bsupportCagReactionsAndPolls\x120\n" +
	"\x13supportBizHostedMsg\x18\t \x01(\bR\x13supportBizHostedMsg\x12Z\n" +
	"(supportRecentSyncChunkMessageCountTuning\x18\n" +
	" \x01(\bR(supportRecentSyncChunkMessageCountTuning\x124\n" +
	"\x15supportHostedGroupMsg\x18\v \x01(\bR\x15supportHostedGroupMsg\x12<\n" +
	"\x19supportFbidBotChatHistory\x18\f \x01(\bR\x19supportFbidBotChatHistory\x12J\n" +
	" supportAddOnHistorySyncMigration\x18\r \x01(\bR supportAddOnHistorySyncMigration\x12<\n" +
	"\x19supportMessageAssociation\x18\x0e \x01(\bR\x19supportMessageAssociation\x1a\x9a\x01\n" +
	"\n" +
	"AppVersion\x12\x18\n" +
	"\aprimary\x18\x01 \x01(\rR\aprimary\x12\x1c\n" +
	"\tsecondary\x18\x02 \x01(\rR\tsecondary\x12\x1a\n" +
	"\btertiary\x18\x03 \x01(\rR\btertiary\x12\x1e\n" +
	"\n" +
	"quaternary\x18\x04 \x01(\rR\n" +
	"quaternary\x12\x18\n" +
	"\aquinary\x18\x05 \x01(\rR\aquinary\"\xdf\x02\n" +
	"\fPlatformType\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\n" +
	"\n" +
	"\x06CHROME\x10\x01\x12\v\n" +
	"\aFIREFOX\x10\x02\x12\x06\n" +
	"\x02IE\x10\x03\x12\t\n" +
	"\x05OPERA\x10\x04\x12\n" +
	"\n" +
	"\x06SAFARI\x10\x05\x12\b\n" +
	"\x04EDGE\x10\x06\x12\v\n" +
	"\aDESKTOP\x10\a\x12\b\n" +
	"\x04IPAD\x10\b\x12\x12\n" +
	"\x0eANDROID_TABLET\x10\t\x12\t\n" +
	"\x05OHANA\x10\n" +
	"\x12\t\n" +
	"\x05ALOHA\x10\v\x12\f\n" +
	"\bCATALINA\x10\f\x12\n" +
	"\n" +
	"\x06TCL_TV\x10\r\x12\r\n" +
	"\tIOS_PHONE\x10\x0e\x12\x10\n" +
	"\fIOS_CATALYST\x10\x0f\x12\x11\n" +
	"\rANDROID_PHONE\x10\x10\x12\x15\n" +
	"\x11ANDROID_AMBIGUOUS\x10\x11\x12\v\n" +
	"\aWEAR_OS\x10\x12\x12\f\n" +
	"\bAR_WRIST\x10\x13\x12\r\n" +
	"\tAR_DEVICE\x10\x14\x12\a\n" +
	"\x03UWP\x10\x15\x12\x06\n" +
	"\x02VR\x10\x16\x12\r\n" +
	"\tCLOUD_API\x10\x17\x12\x10\n" +
	"\fSMARTGLASSES\x10\x18\"\x96\x01\n" +
	"\x1aCompanionEphemeralIdentity\x12\x1c\n" +
	"\tpublicKey\x18\x01 \x01(\fR\tpublicKey\x12H\n" +
	"\n" +
	"deviceType\x18\x02 \x01(\x0e2(.WACompanionReg.DeviceProps.PlatformTypeR\n" +
	"deviceType\x12\x10\n" +
	"\x03ref\x18\x03 \x01(\tR\x03ref\")\n" +
	"\x13CompanionCommitment\x12\x12\n" +
	"\x04hash\x18\x01 \x01(\fR\x04hash\"\x96\x01\n" +
	"\x0fProloguePayload\x12>\n" +
	"\x1acompanionEphemeralIdentity\x18\x01 \x01(\fR\x1acompanionEphemeralIdentity\x12C\n" +
	"\n" +
	"commitment\x18\x02 \x01(\v2#.WACompanionReg.CompanionCommitmentR\n" +
	"commitment\"N\n" +
	"\x18PrimaryEphemeralIdentity\x12\x1c\n" +
	"\tpublicKey\x18\x01 \x01(\fR\tpublicKey\x12\x14\n" +
	"\x05nonce\x18\x02 \x01(\fR\x05nonce\"\x92\x01\n" +
	"\x0ePairingRequest\x12.\n" +
	"\x12companionPublicKey\x18\x01 \x01(\fR\x12companionPublicKey\x122\n" +
	"\x14companionIdentityKey\x18\x02 \x01(\fR\x14companionIdentityKey\x12\x1c\n" +
	"\tadvSecret\x18\x03 \x01(\fR\tadvSecret\"U\n" +
	"\x17EncryptedPairingRequest\x12*\n" +
	"\x10encryptedPayload\x18\x01 \x01(\fR\x10encryptedPayload\x12\x0e\n" +
	"\x02IV\x18\x02 \x01(\fR\x02IV\"\xc4\x01\n" +
	"\x12ClientPairingProps\x120\n" +
	"\x13isChatDbLidMigrated\x18\x01 \x01(\bR\x13isChatDbLidMigrated\x124\n" +
	"\x15isSyncdPureLidSession\x18\x02 \x01(\bR\x15isSyncdPureLidSession\x12F\n" +
	"\x1eisSyncdSnapshotRecoveryEnabled\x18\x03 \x01(\bR\x1eisSyncdSnapshotRecoveryEnabledB*Z(go.mau.fi/whatsmeow/proto/waCompanionReg"

var (
	file_waCompanionReg_WACompanionReg_proto_rawDescOnce sync.Once
	file_waCompanionReg_WACompanionReg_proto_rawDescData []byte
)

func file_waCompanionReg_WACompanionReg_proto_rawDescGZIP() []byte {
	file_waCompanionReg_WACompanionReg_proto_rawDescOnce.Do(func() {
		file_waCompanionReg_WACompanionReg_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_waCompanionReg_WACompanionReg_proto_rawDesc), len(file_waCompanionReg_WACompanionReg_proto_rawDesc)))
	})
	return file_waCompanionReg_WACompanionReg_proto_rawDescData
}

var file_waCompanionReg_WACompanionReg_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_waCompanionReg_WACompanionReg_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_waCompanionReg_WACompanionReg_proto_goTypes = []any{
	(DeviceProps_PlatformType)(0),         // 0: WACompanionReg.DeviceProps.PlatformType
	(*DeviceProps)(nil),                   // 1: WACompanionReg.DeviceProps
	(*CompanionEphemeralIdentity)(nil),    // 2: WACompanionReg.CompanionEphemeralIdentity
	(*CompanionCommitment)(nil),           // 3: WACompanionReg.CompanionCommitment
	(*ProloguePayload)(nil),               // 4: WACompanionReg.ProloguePayload
	(*PrimaryEphemeralIdentity)(nil),      // 5: WACompanionReg.PrimaryEphemeralIdentity
	(*PairingRequest)(nil),                // 6: WACompanionReg.PairingRequest
	(*EncryptedPairingRequest)(nil),       // 7: WACompanionReg.EncryptedPairingRequest
	(*ClientPairingProps)(nil),            // 8: WACompanionReg.ClientPairingProps
	(*DeviceProps_HistorySyncConfig)(nil), // 9: WACompanionReg.DeviceProps.HistorySyncConfig
	(*DeviceProps_AppVersion)(nil),        // 10: WACompanionReg.DeviceProps.AppVersion
}
var file_waCompanionReg_WACompanionReg_proto_depIdxs = []int32{
	10, // 0: WACompanionReg.DeviceProps.version:type_name -> WACompanionReg.DeviceProps.AppVersion
	0,  // 1: WACompanionReg.DeviceProps.platformType:type_name -> WACompanionReg.DeviceProps.PlatformType
	9,  // 2: WACompanionReg.DeviceProps.historySyncConfig:type_name -> WACompanionReg.DeviceProps.HistorySyncConfig
	0,  // 3: WACompanionReg.CompanionEphemeralIdentity.deviceType:type_name -> WACompanionReg.DeviceProps.PlatformType
	3,  // 4: WACompanionReg.ProloguePayload.commitment:type_name -> WACompanionReg.CompanionCommitment
	5,  // [5:5] is the sub-list for method output_type
	5,  // [5:5] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_waCompanionReg_WACompanionReg_proto_init() }
func file_waCompanionReg_WACompanionReg_proto_init() {
	if File_waCompanionReg_WACompanionReg_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_waCompanionReg_WACompanionReg_proto_rawDesc), len(file_waCompanionReg_WACompanionReg_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_waCompanionReg_WACompanionReg_proto_goTypes,
		DependencyIndexes: file_waCompanionReg_WACompanionReg_proto_depIdxs,
		EnumInfos:         file_waCompanionReg_WACompanionReg_proto_enumTypes,
		MessageInfos:      file_waCompanionReg_WACompanionReg_proto_msgTypes,
	}.Build()
	File_waCompanionReg_WACompanionReg_proto = out.File
	file_waCompanionReg_WACompanionReg_proto_goTypes = nil
	file_waCompanionReg_WACompanionReg_proto_depIdxs = nil
}
