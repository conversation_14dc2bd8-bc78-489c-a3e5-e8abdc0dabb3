//! Device-related types

use crate::jid::Jid;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use thiserror::Error;

/// Device validation errors
#[derive(Debug, Error)]
pub enum DeviceError {
    #[error("Invalid platform: {0}")]
    InvalidPlatform(String),

    #[error("Invalid registration ID: {0}")]
    InvalidRegistrationId(u32),

    #[error("Missing required field: {0}")]
    MissingField(String),

    #[error("Invalid key format: {0}")]
    InvalidKey(String),
}

/// Device platform enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum DevicePlatform {
    Android,
    IPhone,
    Web,
    Desktop,
    Portal,
    Unknown(String),
}

impl DevicePlatform {
    pub fn from_string(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "android" => DevicePlatform::Android,
            "iphone" => DevicePlatform::IPhone,
            "web" => DevicePlatform::Web,
            "desktop" => DevicePlatform::Desktop,
            "portal" => DevicePlatform::Portal,
            _ => DevicePlatform::Unknown(s.to_string()),
        }
    }

    pub fn as_str(&self) -> &str {
        match self {
            DevicePlatform::Android => "android",
            DevicePlatform::IPhone => "iphone",
            DevicePlatform::Web => "web",
            DevicePlatform::Desktop => "desktop",
            DevicePlatform::Portal => "portal",
            DevicePlatform::Unknown(s) => s,
        }
    }
}

impl Default for DevicePlatform {
    fn default() -> Self {
        DevicePlatform::Unknown("unknown".to_string())
    }
}

/// Cryptographic key information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CryptoKeys {
    pub identity_key: Option<Vec<u8>>,
    pub signed_pre_key: Option<Vec<u8>>,
    pub signed_pre_key_id: Option<u32>,
    pub signed_pre_key_signature: Option<Vec<u8>>,
    pub registration_id: u32,
    pub pre_keys: HashMap<u32, Vec<u8>>,
    pub next_pre_key_id: u32,
}

impl CryptoKeys {
    pub fn new(registration_id: u32) -> Self {
        Self {
            identity_key: None,
            signed_pre_key: None,
            signed_pre_key_id: None,
            signed_pre_key_signature: None,
            registration_id,
            pre_keys: HashMap::new(),
            next_pre_key_id: 1,
        }
    }

    pub fn with_identity_key(mut self, key: Vec<u8>) -> Self {
        self.identity_key = Some(key);
        self
    }

    pub fn with_signed_pre_key(mut self, key: Vec<u8>, id: u32, signature: Vec<u8>) -> Self {
        self.signed_pre_key = Some(key);
        self.signed_pre_key_id = Some(id);
        self.signed_pre_key_signature = Some(signature);
        self
    }

    pub fn add_pre_key(&mut self, id: u32, key: Vec<u8>) {
        self.pre_keys.insert(id, key);
        if id >= self.next_pre_key_id {
            self.next_pre_key_id = id + 1;
        }
    }

    pub fn get_pre_key(&self, id: u32) -> Option<&Vec<u8>> {
        self.pre_keys.get(&id)
    }

    pub fn remove_pre_key(&mut self, id: u32) -> Option<Vec<u8>> {
        self.pre_keys.remove(&id)
    }

    pub fn has_identity_key(&self) -> bool {
        self.identity_key.is_some()
    }

    pub fn has_signed_pre_key(&self) -> bool {
        self.signed_pre_key.is_some() && self.signed_pre_key_signature.is_some()
    }
}

impl Default for CryptoKeys {
    fn default() -> Self {
        Self::new(0)
    }
}

/// Device information with comprehensive metadata and cryptographic keys
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Device {
    pub jid: Jid,
    pub registration_id: u32,
    pub platform: DevicePlatform,
    pub business_name: Option<String>,
    pub push_name: Option<String>,
    pub crypto_keys: CryptoKeys,
    pub capabilities: Vec<String>,
    pub version: Option<String>,
    pub os_version: Option<String>,
    pub device_manufacturer: Option<String>,
    pub device_model: Option<String>,
    pub locale: Option<String>,
    pub timezone: Option<String>,
    pub battery_level: Option<u8>,
    pub charging: Option<bool>,
    pub connected: bool,
    pub last_seen: Option<std::time::SystemTime>,
    pub user_agent: Option<String>,
    pub app_state_sync_key_id: Option<Vec<u8>>,
    pub app_state_sync_key_data: Option<Vec<u8>>,
}

impl Device {
    /// Create a new Device with required fields
    pub fn new(
        jid: Jid,
        registration_id: u32,
        platform: DevicePlatform,
    ) -> Result<Self, DeviceError> {
        Self::validate_registration_id(registration_id)?;

        Ok(Self {
            jid,
            registration_id,
            platform,
            business_name: None,
            push_name: None,
            crypto_keys: CryptoKeys::new(registration_id),
            capabilities: Vec::new(),
            version: None,
            os_version: None,
            device_manufacturer: None,
            device_model: None,
            locale: None,
            timezone: None,
            battery_level: None,
            charging: None,
            connected: false,
            last_seen: None,
            user_agent: None,
            app_state_sync_key_id: None,
            app_state_sync_key_data: None,
        })
    }

    /// Create a Device builder
    pub fn builder(
        jid: Jid,
        registration_id: u32,
        platform: DevicePlatform,
    ) -> Result<DeviceBuilder, DeviceError> {
        Self::validate_registration_id(registration_id)?;
        Ok(DeviceBuilder::new(jid, registration_id, platform))
    }

    /// Validate registration ID
    fn validate_registration_id(registration_id: u32) -> Result<(), DeviceError> {
        if registration_id == 0 {
            return Err(DeviceError::InvalidRegistrationId(registration_id));
        }
        Ok(())
    }

    /// Check if this is a business device
    pub fn is_business(&self) -> bool {
        self.business_name.is_some()
    }

    /// Check if the device is currently connected
    pub fn is_connected(&self) -> bool {
        self.connected
    }

    /// Check if the device has cryptographic keys set up
    pub fn has_crypto_keys(&self) -> bool {
        self.crypto_keys.has_identity_key() && self.crypto_keys.has_signed_pre_key()
    }

    /// Get the device's display name
    pub fn get_display_name(&self) -> Option<&str> {
        self.push_name.as_deref().or(self.business_name.as_deref())
    }

    /// Check if the device supports a specific capability
    pub fn supports_capability(&self, capability: &str) -> bool {
        self.capabilities.contains(&capability.to_string())
    }

    /// Add a capability to the device
    pub fn add_capability(&mut self, capability: String) {
        if !self.capabilities.contains(&capability) {
            self.capabilities.push(capability);
        }
    }

    /// Remove a capability from the device
    pub fn remove_capability(&mut self, capability: &str) {
        self.capabilities.retain(|c| c != capability);
    }

    /// Update the device's connection status
    pub fn set_connected(&mut self, connected: bool) {
        self.connected = connected;
        if connected {
            self.last_seen = Some(std::time::SystemTime::now());
        }
    }

    /// Update battery information
    pub fn update_battery(&mut self, level: u8, charging: bool) {
        self.battery_level = Some(level.min(100)); // Clamp to 100%
        self.charging = Some(charging);
    }

    /// Get a summary string for the device
    pub fn summary(&self) -> String {
        let name = self.get_display_name().unwrap_or("Unknown");
        let platform = self.platform.as_str();
        let status = if self.connected {
            "Connected"
        } else {
            "Disconnected"
        };
        format!("{} ({}) - {}", name, platform, status)
    }
}

/// Builder pattern for Device
#[derive(Debug)]
pub struct DeviceBuilder {
    device: Device,
}

impl DeviceBuilder {
    fn new(jid: Jid, registration_id: u32, platform: DevicePlatform) -> Self {
        Self {
            device: Device {
                jid,
                registration_id,
                platform,
                business_name: None,
                push_name: None,
                crypto_keys: CryptoKeys::new(registration_id),
                capabilities: Vec::new(),
                version: None,
                os_version: None,
                device_manufacturer: None,
                device_model: None,
                locale: None,
                timezone: None,
                battery_level: None,
                charging: None,
                connected: false,
                last_seen: None,
                user_agent: None,
                app_state_sync_key_id: None,
                app_state_sync_key_data: None,
            },
        }
    }

    pub fn business_name(mut self, name: String) -> Self {
        self.device.business_name = Some(name);
        self
    }

    pub fn push_name(mut self, name: String) -> Self {
        self.device.push_name = Some(name);
        self
    }

    pub fn crypto_keys(mut self, keys: CryptoKeys) -> Self {
        self.device.crypto_keys = keys;
        self
    }

    pub fn capabilities(mut self, capabilities: Vec<String>) -> Self {
        self.device.capabilities = capabilities;
        self
    }

    pub fn version(mut self, version: String) -> Self {
        self.device.version = Some(version);
        self
    }

    pub fn os_version(mut self, os_version: String) -> Self {
        self.device.os_version = Some(os_version);
        self
    }

    pub fn device_info(mut self, manufacturer: String, model: String) -> Self {
        self.device.device_manufacturer = Some(manufacturer);
        self.device.device_model = Some(model);
        self
    }

    pub fn locale(mut self, locale: String) -> Self {
        self.device.locale = Some(locale);
        self
    }

    pub fn timezone(mut self, timezone: String) -> Self {
        self.device.timezone = Some(timezone);
        self
    }

    pub fn battery(mut self, level: u8, charging: bool) -> Self {
        self.device.battery_level = Some(level.min(100));
        self.device.charging = Some(charging);
        self
    }

    pub fn connected(mut self, connected: bool) -> Self {
        self.device.connected = connected;
        if connected {
            self.device.last_seen = Some(std::time::SystemTime::now());
        }
        self
    }

    pub fn user_agent(mut self, user_agent: String) -> Self {
        self.device.user_agent = Some(user_agent);
        self
    }

    pub fn app_state_sync_keys(mut self, key_id: Vec<u8>, key_data: Vec<u8>) -> Self {
        self.device.app_state_sync_key_id = Some(key_id);
        self.device.app_state_sync_key_data = Some(key_data);
        self
    }

    pub fn build(self) -> Device {
        self.device
    }
}
#[cfg(test)]
mod tests {
    use super::*;
    use crate::jid::Jid;

    fn create_test_jid() -> Jid {
        Jid::new_unchecked("1234567890".to_string(), "s.whatsapp.net".to_string(), 0)
    }

    #[test]
    fn test_device_platform_from_string() {
        assert_eq!(
            DevicePlatform::from_string("android"),
            DevicePlatform::Android
        );
        assert_eq!(
            DevicePlatform::from_string("ANDROID"),
            DevicePlatform::Android
        );
        assert_eq!(
            DevicePlatform::from_string("iphone"),
            DevicePlatform::IPhone
        );
        assert_eq!(DevicePlatform::from_string("web"), DevicePlatform::Web);
        assert_eq!(
            DevicePlatform::from_string("desktop"),
            DevicePlatform::Desktop
        );
        assert_eq!(
            DevicePlatform::from_string("portal"),
            DevicePlatform::Portal
        );

        if let DevicePlatform::Unknown(s) = DevicePlatform::from_string("custom") {
            assert_eq!(s, "custom");
        } else {
            panic!("Expected Unknown variant");
        }
    }

    #[test]
    fn test_device_platform_as_str() {
        assert_eq!(DevicePlatform::Android.as_str(), "android");
        assert_eq!(DevicePlatform::IPhone.as_str(), "iphone");
        assert_eq!(DevicePlatform::Web.as_str(), "web");
        assert_eq!(DevicePlatform::Desktop.as_str(), "desktop");
        assert_eq!(DevicePlatform::Portal.as_str(), "portal");
        assert_eq!(
            DevicePlatform::Unknown("custom".to_string()).as_str(),
            "custom"
        );
    }

    #[test]
    fn test_device_platform_default() {
        let default_platform = DevicePlatform::default();
        assert_eq!(default_platform.as_str(), "unknown");
    }

    #[test]
    fn test_crypto_keys_creation() {
        let keys = CryptoKeys::new(12345);
        assert_eq!(keys.registration_id, 12345);
        assert!(!keys.has_identity_key());
        assert!(!keys.has_signed_pre_key());
        assert_eq!(keys.next_pre_key_id, 1);
        assert!(keys.pre_keys.is_empty());
    }

    #[test]
    fn test_crypto_keys_with_identity_key() {
        let key_data = vec![1, 2, 3, 4, 5];
        let keys = CryptoKeys::new(12345).with_identity_key(key_data.clone());

        assert!(keys.has_identity_key());
        assert_eq!(keys.identity_key, Some(key_data));
    }

    #[test]
    fn test_crypto_keys_with_signed_pre_key() {
        let key_data = vec![1, 2, 3];
        let signature = vec![4, 5, 6];
        let keys =
            CryptoKeys::new(12345).with_signed_pre_key(key_data.clone(), 1, signature.clone());

        assert!(keys.has_signed_pre_key());
        assert_eq!(keys.signed_pre_key, Some(key_data));
        assert_eq!(keys.signed_pre_key_id, Some(1));
        assert_eq!(keys.signed_pre_key_signature, Some(signature));
    }

    #[test]
    fn test_crypto_keys_pre_key_management() {
        let mut keys = CryptoKeys::new(12345);
        let key1 = vec![1, 2, 3];
        let key2 = vec![4, 5, 6];

        // Add pre-keys
        keys.add_pre_key(1, key1.clone());
        keys.add_pre_key(2, key2.clone());

        assert_eq!(keys.get_pre_key(1), Some(&key1));
        assert_eq!(keys.get_pre_key(2), Some(&key2));
        assert_eq!(keys.get_pre_key(3), None);
        assert_eq!(keys.next_pre_key_id, 3);

        // Remove pre-key
        let removed = keys.remove_pre_key(1);
        assert_eq!(removed, Some(key1));
        assert_eq!(keys.get_pre_key(1), None);
    }

    #[test]
    fn test_crypto_keys_default() {
        let keys = CryptoKeys::default();
        assert_eq!(keys.registration_id, 0);
        assert!(!keys.has_identity_key());
        assert!(!keys.has_signed_pre_key());
    }

    #[test]
    fn test_device_creation_success() {
        let jid = create_test_jid();
        let device = Device::new(jid.clone(), 12345, DevicePlatform::Android).unwrap();

        assert_eq!(device.jid, jid);
        assert_eq!(device.registration_id, 12345);
        assert_eq!(device.platform, DevicePlatform::Android);
        assert!(!device.is_business());
        assert!(!device.is_connected());
        assert!(!device.has_crypto_keys());
        assert_eq!(device.get_display_name(), None);
    }

    #[test]
    fn test_device_creation_invalid_registration_id() {
        let jid = create_test_jid();
        let result = Device::new(jid, 0, DevicePlatform::Android);

        assert!(result.is_err());
        assert!(matches!(
            result.unwrap_err(),
            DeviceError::InvalidRegistrationId(0)
        ));
    }

    #[test]
    fn test_device_builder_basic() {
        let jid = create_test_jid();
        let device = Device::builder(jid.clone(), 12345, DevicePlatform::Web)
            .unwrap()
            .push_name("Test User".to_string())
            .version("2.2.24".to_string())
            .connected(true)
            .build();

        assert_eq!(device.jid, jid);
        assert_eq!(device.registration_id, 12345);
        assert_eq!(device.platform, DevicePlatform::Web);
        assert_eq!(device.push_name, Some("Test User".to_string()));
        assert_eq!(device.version, Some("2.2.24".to_string()));
        assert!(device.is_connected());
        assert_eq!(device.get_display_name(), Some("Test User"));
    }

    #[test]
    fn test_device_builder_comprehensive() {
        let jid = create_test_jid();
        let capabilities = vec!["audio".to_string(), "video".to_string()];
        let crypto_keys = CryptoKeys::new(12345)
            .with_identity_key(vec![1, 2, 3])
            .with_signed_pre_key(vec![4, 5, 6], 1, vec![7, 8, 9]);

        let device = Device::builder(jid.clone(), 12345, DevicePlatform::Android)
            .unwrap()
            .business_name("Test Business".to_string())
            .push_name("Test User".to_string())
            .crypto_keys(crypto_keys)
            .capabilities(capabilities.clone())
            .os_version("Android 12".to_string())
            .device_info("Samsung".to_string(), "Galaxy S21".to_string())
            .locale("en-US".to_string())
            .timezone("America/New_York".to_string())
            .battery(85, true)
            .user_agent("WhatsApp/2.21.24".to_string())
            .app_state_sync_keys(vec![10, 11, 12], vec![13, 14, 15])
            .build();

        assert!(device.is_business());
        assert_eq!(device.business_name, Some("Test Business".to_string()));
        assert_eq!(device.push_name, Some("Test User".to_string()));
        assert_eq!(device.capabilities, capabilities);
        assert_eq!(device.os_version, Some("Android 12".to_string()));
        assert_eq!(device.device_manufacturer, Some("Samsung".to_string()));
        assert_eq!(device.device_model, Some("Galaxy S21".to_string()));
        assert_eq!(device.locale, Some("en-US".to_string()));
        assert_eq!(device.timezone, Some("America/New_York".to_string()));
        assert_eq!(device.battery_level, Some(85));
        assert_eq!(device.charging, Some(true));
        assert_eq!(device.user_agent, Some("WhatsApp/2.21.24".to_string()));
        assert!(device.has_crypto_keys());
    }

    #[test]
    fn test_device_is_business() {
        let jid = create_test_jid();
        let mut device = Device::new(jid, 12345, DevicePlatform::Android).unwrap();

        assert!(!device.is_business());

        device.business_name = Some("Test Business".to_string());
        assert!(device.is_business());
    }

    #[test]
    fn test_device_has_crypto_keys() {
        let jid = create_test_jid();
        let mut device = Device::new(jid, 12345, DevicePlatform::Android).unwrap();

        assert!(!device.has_crypto_keys());

        // Add identity key only
        device.crypto_keys.identity_key = Some(vec![1, 2, 3]);
        assert!(!device.has_crypto_keys());

        // Add signed pre-key
        device.crypto_keys.signed_pre_key = Some(vec![4, 5, 6]);
        device.crypto_keys.signed_pre_key_signature = Some(vec![7, 8, 9]);
        assert!(device.has_crypto_keys());
    }

    #[test]
    fn test_device_get_display_name() {
        let jid = create_test_jid();
        let mut device = Device::new(jid, 12345, DevicePlatform::Android).unwrap();

        assert_eq!(device.get_display_name(), None);

        device.push_name = Some("Push Name".to_string());
        assert_eq!(device.get_display_name(), Some("Push Name"));

        device.business_name = Some("Business Name".to_string());
        // push_name takes precedence
        assert_eq!(device.get_display_name(), Some("Push Name"));

        device.push_name = None;
        assert_eq!(device.get_display_name(), Some("Business Name"));
    }

    #[test]
    fn test_device_capability_management() {
        let jid = create_test_jid();
        let mut device = Device::new(jid, 12345, DevicePlatform::Android).unwrap();

        assert!(!device.supports_capability("audio"));

        device.add_capability("audio".to_string());
        assert!(device.supports_capability("audio"));
        assert_eq!(device.capabilities.len(), 1);

        // Adding same capability again should not duplicate
        device.add_capability("audio".to_string());
        assert_eq!(device.capabilities.len(), 1);

        device.add_capability("video".to_string());
        assert!(device.supports_capability("video"));
        assert_eq!(device.capabilities.len(), 2);

        device.remove_capability("audio");
        assert!(!device.supports_capability("audio"));
        assert!(device.supports_capability("video"));
        assert_eq!(device.capabilities.len(), 1);
    }

    #[test]
    fn test_device_connection_status() {
        let jid = create_test_jid();
        let mut device = Device::new(jid, 12345, DevicePlatform::Android).unwrap();

        assert!(!device.is_connected());
        assert_eq!(device.last_seen, None);

        device.set_connected(true);
        assert!(device.is_connected());
        assert!(device.last_seen.is_some());

        device.set_connected(false);
        assert!(!device.is_connected());
        // last_seen should still be set
        assert!(device.last_seen.is_some());
    }

    #[test]
    fn test_device_battery_update() {
        let jid = create_test_jid();
        let mut device = Device::new(jid, 12345, DevicePlatform::Android).unwrap();

        assert_eq!(device.battery_level, None);
        assert_eq!(device.charging, None);

        device.update_battery(75, false);
        assert_eq!(device.battery_level, Some(75));
        assert_eq!(device.charging, Some(false));

        // Test clamping to 100%
        device.update_battery(150, true);
        assert_eq!(device.battery_level, Some(100));
        assert_eq!(device.charging, Some(true));
    }

    #[test]
    fn test_device_summary() {
        let jid = create_test_jid();
        let device = Device::builder(jid.clone(), 12345, DevicePlatform::Android)
            .unwrap()
            .push_name("Test User".to_string())
            .connected(true)
            .build();

        let summary = device.summary();
        assert!(summary.contains("Test User"));
        assert!(summary.contains("android"));
        assert!(summary.contains("Connected"));

        let device_disconnected = Device::builder(jid, 12345, DevicePlatform::IPhone)
            .unwrap()
            .build();

        let summary_disconnected = device_disconnected.summary();
        assert!(summary_disconnected.contains("Unknown"));
        assert!(summary_disconnected.contains("iphone"));
        assert!(summary_disconnected.contains("Disconnected"));
    }

    #[test]
    fn test_device_serialization() {
        let jid = create_test_jid();
        let crypto_keys = CryptoKeys::new(12345).with_identity_key(vec![1, 2, 3]);

        let device = Device::builder(jid, 12345, DevicePlatform::Web)
            .unwrap()
            .push_name("Serialize Test".to_string())
            .crypto_keys(crypto_keys)
            .capabilities(vec!["audio".to_string()])
            .version("1.0.0".to_string())
            .connected(true)
            .build();

        let serialized = serde_json::to_string(&device).unwrap();
        let deserialized: Device = serde_json::from_str(&serialized).unwrap();

        assert_eq!(device.jid, deserialized.jid);
        assert_eq!(device.registration_id, deserialized.registration_id);
        assert_eq!(device.platform, deserialized.platform);
        assert_eq!(device.push_name, deserialized.push_name);
        assert_eq!(device.capabilities, deserialized.capabilities);
        assert_eq!(device.version, deserialized.version);
        assert_eq!(device.connected, deserialized.connected);
        assert_eq!(
            device.crypto_keys.registration_id,
            deserialized.crypto_keys.registration_id
        );
        assert_eq!(
            device.crypto_keys.identity_key,
            deserialized.crypto_keys.identity_key
        );
    }

    #[test]
    fn test_crypto_keys_serialization() {
        let mut keys = CryptoKeys::new(12345)
            .with_identity_key(vec![1, 2, 3])
            .with_signed_pre_key(vec![4, 5, 6], 1, vec![7, 8, 9]);

        keys.add_pre_key(10, vec![10, 11, 12]);

        let serialized = serde_json::to_string(&keys).unwrap();
        let deserialized: CryptoKeys = serde_json::from_str(&serialized).unwrap();

        assert_eq!(keys.registration_id, deserialized.registration_id);
        assert_eq!(keys.identity_key, deserialized.identity_key);
        assert_eq!(keys.signed_pre_key, deserialized.signed_pre_key);
        assert_eq!(keys.signed_pre_key_id, deserialized.signed_pre_key_id);
        assert_eq!(
            keys.signed_pre_key_signature,
            deserialized.signed_pre_key_signature
        );
        assert_eq!(keys.pre_keys, deserialized.pre_keys);
        assert_eq!(keys.next_pre_key_id, deserialized.next_pre_key_id);
    }

    #[test]
    fn test_device_platform_serialization() {
        let platforms = vec![
            DevicePlatform::Android,
            DevicePlatform::IPhone,
            DevicePlatform::Web,
            DevicePlatform::Desktop,
            DevicePlatform::Portal,
            DevicePlatform::Unknown("custom".to_string()),
        ];

        for platform in platforms {
            let serialized = serde_json::to_string(&platform).unwrap();
            let deserialized: DevicePlatform = serde_json::from_str(&serialized).unwrap();
            assert_eq!(platform, deserialized);
        }
    }
}
