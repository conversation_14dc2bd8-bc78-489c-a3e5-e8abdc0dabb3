//! Storage trait definitions

use crate::error::StoreError;
use async_trait::async_trait;
use whatsmeow_types::Device;

/// Device storage interface
#[async_trait]
pub trait DeviceStore: Send + Sync {
    async fn get_device(&self) -> Result<Option<Device>, StoreError>;
    async fn put_device(&self, device: &Device) -> Result<(), StoreError>;
    async fn delete_device(&self) -> Result<(), StoreError>;
}

/// Session storage interface
#[async_trait]
pub trait SessionStore: Send + Sync {
    async fn get_session(&self, address: &str) -> Result<Option<Vec<u8>>, StoreError>;
    async fn put_session(&self, address: &str, session: &[u8]) -> Result<(), StoreError>;
    async fn delete_session(&self, address: &str) -> Result<(), StoreError>;
    async fn delete_all_sessions(&self, phone: &str) -> Result<(), StoreError>;
}

/// PreKey storage interface
#[async_trait]
pub trait PreKeyStore: Send + Sync {
    async fn get_or_gen_prekeys(&self, count: u32) -> Result<Vec<PreKey>, StoreError>;
    async fn get_prekey(&self, id: u32) -> Result<Option<PreKey>, StoreError>;
    async fn remove_prekey(&self, id: u32) -> Result<(), StoreError>;
    async fn mark_prekeys_as_uploaded(&self, up_to_id: u32) -> Result<(), StoreError>;
}

/// Sender key storage interface
#[async_trait]
pub trait SenderKeyStore: Send + Sync {
    async fn get_sender_key(
        &self,
        group_id: &str,
        sender_id: &str,
    ) -> Result<Option<Vec<u8>>, StoreError>;
    async fn put_sender_key(
        &self,
        group_id: &str,
        sender_id: &str,
        key: &[u8],
    ) -> Result<(), StoreError>;
}

/// Identity key storage interface
#[async_trait]
pub trait IdentityStore: Send + Sync {
    async fn get_identity_key(&self, address: &str) -> Result<Option<Vec<u8>>, StoreError>;
    async fn put_identity_key(&self, address: &str, key: &[u8]) -> Result<(), StoreError>;
    async fn is_trusted_identity(&self, address: &str, key: &[u8]) -> Result<bool, StoreError>;
}

/// PreKey data structure
#[derive(Debug, Clone)]
pub struct PreKey {
    pub id: u32,
    pub key_pair: Vec<u8>, // Serialized key pair
    pub uploaded: bool,
}

impl PreKey {
    pub fn new(id: u32, key_pair: Vec<u8>) -> Self {
        Self {
            id,
            key_pair,
            uploaded: false,
        }
    }
}
