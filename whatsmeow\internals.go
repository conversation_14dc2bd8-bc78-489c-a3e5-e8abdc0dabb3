// GENERATED BY internals_generate.go; DO NOT EDIT

//go:generate go run internals_generate.go
//go:generate goimports -local go.mau.fi/whatsmeow -w internals.go

package whatsmeow

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"time"

	"go.mau.fi/libsignal/keys/prekey"

	"go.mau.fi/whatsmeow/appstate"
	waBinary "go.mau.fi/whatsmeow/binary"
	"go.mau.fi/whatsmeow/proto/waCommon"
	"go.mau.fi/whatsmeow/proto/waE2E"
	"go.mau.fi/whatsmeow/proto/waHistorySync"
	"go.mau.fi/whatsmeow/proto/waMsgApplication"
	"go.mau.fi/whatsmeow/proto/waMsgTransport"
	"go.mau.fi/whatsmeow/proto/waServerSync"
	"go.mau.fi/whatsmeow/socket"
	"go.mau.fi/whatsmeow/store"
	"go.mau.fi/whatsmeow/types"
	"go.mau.fi/whatsmeow/types/events"
	"go.mau.fi/whatsmeow/util/keys"
)

type DangerousInternalClient struct {
	c *Client
}

// DangerousInternals allows access to all unexported methods in Client.
//
// Deprecated: dangerous
func (cli *Client) DangerousInternals() *DangerousInternalClient {
	return &DangerousInternalClient{cli}
}

type DangerousInfoQuery = infoQuery
type DangerousInfoQueryType = infoQueryType

func (int *DangerousInternalClient) FilterContacts(mutations []appstate.Mutation) ([]appstate.Mutation, []store.ContactEntry) {
	return int.c.filterContacts(mutations)
}

func (int *DangerousInternalClient) DispatchAppState(ctx context.Context, mutation appstate.Mutation, fullSync bool, emitOnFullSync bool) {
	int.c.dispatchAppState(ctx, mutation, fullSync, emitOnFullSync)
}

func (int *DangerousInternalClient) DownloadExternalAppStateBlob(ctx context.Context, ref *waServerSync.ExternalBlobReference) ([]byte, error) {
	return int.c.downloadExternalAppStateBlob(ctx, ref)
}

func (int *DangerousInternalClient) FetchAppStatePatches(ctx context.Context, name appstate.WAPatchName, fromVersion uint64, snapshot bool) (*appstate.PatchList, error) {
	return int.c.fetchAppStatePatches(ctx, name, fromVersion, snapshot)
}

func (int *DangerousInternalClient) RequestMissingAppStateKeys(ctx context.Context, patches *appstate.PatchList) {
	int.c.requestMissingAppStateKeys(ctx, patches)
}

func (int *DangerousInternalClient) RequestAppStateKeys(ctx context.Context, rawKeyIDs [][]byte) {
	int.c.requestAppStateKeys(ctx, rawKeyIDs)
}

func (int *DangerousInternalClient) HandleDecryptedArmadillo(ctx context.Context, info *types.MessageInfo, decrypted []byte, retryCount int) (handled, handlerFailed bool) {
	return int.c.handleDecryptedArmadillo(ctx, info, decrypted, retryCount)
}

func (int *DangerousInternalClient) GetBroadcastListParticipants(ctx context.Context, jid types.JID) ([]types.JID, error) {
	return int.c.getBroadcastListParticipants(ctx, jid)
}

func (int *DangerousInternalClient) GetStatusBroadcastRecipients(ctx context.Context) ([]types.JID, error) {
	return int.c.getStatusBroadcastRecipients(ctx)
}

func (int *DangerousInternalClient) HandleCallEvent(node *waBinary.Node) {
	int.c.handleCallEvent(node)
}

func (int *DangerousInternalClient) GetSocketWaitChan() <-chan struct{} {
	return int.c.getSocketWaitChan()
}

func (int *DangerousInternalClient) CloseSocketWaitChan() {
	int.c.closeSocketWaitChan()
}

func (int *DangerousInternalClient) GetOwnID() types.JID {
	return int.c.getOwnID()
}

func (int *DangerousInternalClient) GetOwnLID() types.JID {
	return int.c.getOwnLID()
}

func (int *DangerousInternalClient) Connect() error {
	return int.c.connect()
}

func (int *DangerousInternalClient) UnlockedConnect() error {
	return int.c.unlockedConnect()
}

func (int *DangerousInternalClient) OnDisconnect(ns *socket.NoiseSocket, remote bool) {
	int.c.onDisconnect(ns, remote)
}

func (int *DangerousInternalClient) ExpectDisconnect() {
	int.c.expectDisconnect()
}

func (int *DangerousInternalClient) ResetExpectedDisconnect() {
	int.c.resetExpectedDisconnect()
}

func (int *DangerousInternalClient) IsExpectedDisconnect() bool {
	return int.c.isExpectedDisconnect()
}

func (int *DangerousInternalClient) AutoReconnect() {
	int.c.autoReconnect()
}

func (int *DangerousInternalClient) UnlockedDisconnect() {
	int.c.unlockedDisconnect()
}

func (int *DangerousInternalClient) HandleFrame(data []byte) {
	int.c.handleFrame(data)
}

func (int *DangerousInternalClient) HandlerQueueLoop(ctx context.Context) {
	int.c.handlerQueueLoop(ctx)
}

func (int *DangerousInternalClient) SendNodeAndGetData(node waBinary.Node) ([]byte, error) {
	return int.c.sendNodeAndGetData(node)
}

func (int *DangerousInternalClient) SendNode(node waBinary.Node) error {
	return int.c.sendNode(node)
}

func (int *DangerousInternalClient) DispatchEvent(evt any) (handlerFailed bool) {
	return int.c.dispatchEvent(evt)
}

func (int *DangerousInternalClient) HandleStreamError(node *waBinary.Node) {
	int.c.handleStreamError(node)
}

func (int *DangerousInternalClient) HandleIB(node *waBinary.Node) {
	int.c.handleIB(node)
}

func (int *DangerousInternalClient) HandleConnectFailure(node *waBinary.Node) {
	int.c.handleConnectFailure(node)
}

func (int *DangerousInternalClient) HandleConnectSuccess(node *waBinary.Node) {
	int.c.handleConnectSuccess(node)
}

func (int *DangerousInternalClient) DownloadAndDecrypt(ctx context.Context, url string, mediaKey []byte, appInfo MediaType, fileLength int, fileEncSHA256, fileSHA256 []byte) (data []byte, err error) {
	return int.c.downloadAndDecrypt(ctx, url, mediaKey, appInfo, fileLength, fileEncSHA256, fileSHA256)
}

func (int *DangerousInternalClient) DownloadPossiblyEncryptedMediaWithRetries(ctx context.Context, url string, checksum []byte) (file, mac []byte, err error) {
	return int.c.downloadPossiblyEncryptedMediaWithRetries(ctx, url, checksum)
}

func (int *DangerousInternalClient) DoMediaDownloadRequest(ctx context.Context, url string) (*http.Response, error) {
	return int.c.doMediaDownloadRequest(ctx, url)
}

func (int *DangerousInternalClient) DownloadMedia(ctx context.Context, url string) ([]byte, error) {
	return int.c.downloadMedia(ctx, url)
}

func (int *DangerousInternalClient) DownloadEncryptedMedia(ctx context.Context, url string, checksum []byte) (file, mac []byte, err error) {
	return int.c.downloadEncryptedMedia(ctx, url, checksum)
}

func (int *DangerousInternalClient) DownloadAndDecryptToFile(ctx context.Context, url string, mediaKey []byte, appInfo MediaType, fileLength int, fileEncSHA256, fileSHA256 []byte, file File) error {
	return int.c.downloadAndDecryptToFile(ctx, url, mediaKey, appInfo, fileLength, fileEncSHA256, fileSHA256, file)
}

func (int *DangerousInternalClient) DownloadPossiblyEncryptedMediaWithRetriesToFile(ctx context.Context, url string, checksum []byte, file File) (mac []byte, err error) {
	return int.c.downloadPossiblyEncryptedMediaWithRetriesToFile(ctx, url, checksum, file)
}

func (int *DangerousInternalClient) DownloadMediaToFile(ctx context.Context, url string, file io.Writer) (int64, []byte, error) {
	return int.c.downloadMediaToFile(ctx, url, file)
}

func (int *DangerousInternalClient) DownloadEncryptedMediaToFile(ctx context.Context, url string, checksum []byte, file File) ([]byte, error) {
	return int.c.downloadEncryptedMediaToFile(ctx, url, checksum, file)
}

func (int *DangerousInternalClient) SendGroupIQ(ctx context.Context, iqType infoQueryType, jid types.JID, content waBinary.Node) (*waBinary.Node, error) {
	return int.c.sendGroupIQ(ctx, iqType, jid, content)
}

func (int *DangerousInternalClient) GetGroupInfo(ctx context.Context, jid types.JID, lockParticipantCache bool) (*types.GroupInfo, error) {
	return int.c.getGroupInfo(ctx, jid, lockParticipantCache)
}

func (int *DangerousInternalClient) GetCachedGroupData(ctx context.Context, jid types.JID) (*groupMetaCache, error) {
	return int.c.getCachedGroupData(ctx, jid)
}

func (int *DangerousInternalClient) ParseGroupNode(groupNode *waBinary.Node) (*types.GroupInfo, error) {
	return int.c.parseGroupNode(groupNode)
}

func (int *DangerousInternalClient) ParseGroupCreate(parentNode, node *waBinary.Node) (*events.JoinedGroup, error) {
	return int.c.parseGroupCreate(parentNode, node)
}

func (int *DangerousInternalClient) ParseGroupChange(node *waBinary.Node) (*events.GroupInfo, error) {
	return int.c.parseGroupChange(node)
}

func (int *DangerousInternalClient) UpdateGroupParticipantCache(evt *events.GroupInfo) {
	int.c.updateGroupParticipantCache(evt)
}

func (int *DangerousInternalClient) ParseGroupNotification(node *waBinary.Node) (any, error) {
	return int.c.parseGroupNotification(node)
}

func (int *DangerousInternalClient) DoHandshake(fs *socket.FrameSocket, ephemeralKP keys.KeyPair) error {
	return int.c.doHandshake(fs, ephemeralKP)
}

func (int *DangerousInternalClient) KeepAliveLoop(ctx context.Context) {
	int.c.keepAliveLoop(ctx)
}

func (int *DangerousInternalClient) SendKeepAlive(ctx context.Context) (isSuccess, shouldContinue bool) {
	return int.c.sendKeepAlive(ctx)
}

func (int *DangerousInternalClient) RefreshMediaConn(ctx context.Context, force bool) (*MediaConn, error) {
	return int.c.refreshMediaConn(ctx, force)
}

func (int *DangerousInternalClient) QueryMediaConn(ctx context.Context) (*MediaConn, error) {
	return int.c.queryMediaConn(ctx)
}

func (int *DangerousInternalClient) HandleMediaRetryNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handleMediaRetryNotification(ctx, node)
}

func (int *DangerousInternalClient) HandleEncryptedMessage(node *waBinary.Node) {
	int.c.handleEncryptedMessage(node)
}

func (int *DangerousInternalClient) ParseMessageSource(node *waBinary.Node, requireParticipant bool) (source types.MessageSource, err error) {
	return int.c.parseMessageSource(node, requireParticipant)
}

func (int *DangerousInternalClient) ParseMsgBotInfo(node waBinary.Node) (botInfo types.MsgBotInfo, err error) {
	return int.c.parseMsgBotInfo(node)
}

func (int *DangerousInternalClient) ParseMsgMetaInfo(node waBinary.Node) (metaInfo types.MsgMetaInfo, err error) {
	return int.c.parseMsgMetaInfo(node)
}

func (int *DangerousInternalClient) ParseMessageInfo(node *waBinary.Node) (*types.MessageInfo, error) {
	return int.c.parseMessageInfo(node)
}

func (int *DangerousInternalClient) HandlePlaintextMessage(ctx context.Context, info *types.MessageInfo, node *waBinary.Node) bool {
	return int.c.handlePlaintextMessage(ctx, info, node)
}

func (int *DangerousInternalClient) MigrateSessionStore(ctx context.Context, pn, lid types.JID) {
	int.c.migrateSessionStore(ctx, pn, lid)
}

func (int *DangerousInternalClient) DecryptMessages(ctx context.Context, info *types.MessageInfo, node *waBinary.Node) (handlerFailed bool) {
	return int.c.decryptMessages(ctx, info, node)
}

func (int *DangerousInternalClient) ClearUntrustedIdentity(ctx context.Context, target types.JID) error {
	return int.c.clearUntrustedIdentity(ctx, target)
}

func (int *DangerousInternalClient) BufferedDecrypt(ctx context.Context, ciphertext []byte, serverTimestamp time.Time, decrypt func(context.Context) ([]byte, error)) (plaintext []byte, ciphertextHash [32]byte, err error) {
	return int.c.bufferedDecrypt(ctx, ciphertext, serverTimestamp, decrypt)
}

func (int *DangerousInternalClient) DecryptDM(ctx context.Context, child *waBinary.Node, from types.JID, isPreKey bool, serverTS time.Time) ([]byte, *[32]byte, error) {
	return int.c.decryptDM(ctx, child, from, isPreKey, serverTS)
}

func (int *DangerousInternalClient) DecryptGroupMsg(ctx context.Context, child *waBinary.Node, from types.JID, chat types.JID, serverTS time.Time) ([]byte, *[32]byte, error) {
	return int.c.decryptGroupMsg(ctx, child, from, chat, serverTS)
}

func (int *DangerousInternalClient) HandleSenderKeyDistributionMessage(ctx context.Context, chat, from types.JID, axolotlSKDM []byte) {
	int.c.handleSenderKeyDistributionMessage(ctx, chat, from, axolotlSKDM)
}

func (int *DangerousInternalClient) HandleHistorySyncNotificationLoop() {
	int.c.handleHistorySyncNotificationLoop()
}

func (int *DangerousInternalClient) HandleAppStateSyncKeyShare(ctx context.Context, keys *waE2E.AppStateSyncKeyShare) {
	int.c.handleAppStateSyncKeyShare(ctx, keys)
}

func (int *DangerousInternalClient) HandlePlaceholderResendResponse(msg *waE2E.PeerDataOperationRequestResponseMessage) {
	int.c.handlePlaceholderResendResponse(msg)
}

func (int *DangerousInternalClient) HandleProtocolMessage(ctx context.Context, info *types.MessageInfo, msg *waE2E.Message) {
	int.c.handleProtocolMessage(ctx, info, msg)
}

func (int *DangerousInternalClient) ProcessProtocolParts(ctx context.Context, info *types.MessageInfo, msg *waE2E.Message) {
	int.c.processProtocolParts(ctx, info, msg)
}

func (int *DangerousInternalClient) StoreMessageSecret(ctx context.Context, info *types.MessageInfo, msg *waE2E.Message) {
	int.c.storeMessageSecret(ctx, info, msg)
}

func (int *DangerousInternalClient) StoreHistoricalMessageSecrets(ctx context.Context, conversations []*waHistorySync.Conversation) {
	int.c.storeHistoricalMessageSecrets(ctx, conversations)
}

func (int *DangerousInternalClient) HandleDecryptedMessage(ctx context.Context, info *types.MessageInfo, msg *waE2E.Message, retryCount int) bool {
	return int.c.handleDecryptedMessage(ctx, info, msg, retryCount)
}

func (int *DangerousInternalClient) SendProtocolMessageReceipt(id types.MessageID, msgType types.ReceiptType) {
	int.c.sendProtocolMessageReceipt(id, msgType)
}

func (int *DangerousInternalClient) DecryptMsgSecret(ctx context.Context, msg *events.Message, useCase MsgSecretType, encrypted messageEncryptedSecret, origMsgKey *waCommon.MessageKey) ([]byte, error) {
	return int.c.decryptMsgSecret(ctx, msg, useCase, encrypted, origMsgKey)
}

func (int *DangerousInternalClient) EncryptMsgSecret(ctx context.Context, ownID, chat, origSender types.JID, origMsgID types.MessageID, useCase MsgSecretType, plaintext []byte) (ciphertext, iv []byte, err error) {
	return int.c.encryptMsgSecret(ctx, ownID, chat, origSender, origMsgID, useCase, plaintext)
}

func (int *DangerousInternalClient) DecryptBotMessage(ctx context.Context, messageSecret []byte, msMsg messageEncryptedSecret, messageID types.MessageID, targetSenderJID types.JID, info *types.MessageInfo) ([]byte, error) {
	return int.c.decryptBotMessage(ctx, messageSecret, msMsg, messageID, targetSenderJID, info)
}

func (int *DangerousInternalClient) SendMexIQ(ctx context.Context, queryID string, variables any) (json.RawMessage, error) {
	return int.c.sendMexIQ(ctx, queryID, variables)
}

func (int *DangerousInternalClient) GetNewsletterInfo(input map[string]any, fetchViewerMeta bool) (*types.NewsletterMetadata, error) {
	return int.c.getNewsletterInfo(input, fetchViewerMeta)
}

func (int *DangerousInternalClient) HandleEncryptNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handleEncryptNotification(ctx, node)
}

func (int *DangerousInternalClient) HandleAppStateNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handleAppStateNotification(ctx, node)
}

func (int *DangerousInternalClient) HandlePictureNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handlePictureNotification(ctx, node)
}

func (int *DangerousInternalClient) HandleDeviceNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handleDeviceNotification(ctx, node)
}

func (int *DangerousInternalClient) HandleFBDeviceNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handleFBDeviceNotification(ctx, node)
}

func (int *DangerousInternalClient) HandleOwnDevicesNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handleOwnDevicesNotification(ctx, node)
}

func (int *DangerousInternalClient) HandleBlocklist(ctx context.Context, node *waBinary.Node) {
	int.c.handleBlocklist(ctx, node)
}

func (int *DangerousInternalClient) HandleAccountSyncNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handleAccountSyncNotification(ctx, node)
}

func (int *DangerousInternalClient) HandlePrivacyTokenNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handlePrivacyTokenNotification(ctx, node)
}

func (int *DangerousInternalClient) ParseNewsletterMessages(node *waBinary.Node) []*types.NewsletterMessage {
	return int.c.parseNewsletterMessages(node)
}

func (int *DangerousInternalClient) HandleNewsletterNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handleNewsletterNotification(ctx, node)
}

func (int *DangerousInternalClient) HandleMexNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handleMexNotification(ctx, node)
}

func (int *DangerousInternalClient) HandleStatusNotification(ctx context.Context, node *waBinary.Node) {
	int.c.handleStatusNotification(ctx, node)
}

func (int *DangerousInternalClient) HandleNotification(node *waBinary.Node) {
	int.c.handleNotification(node)
}

func (int *DangerousInternalClient) TryHandleCodePairNotification(ctx context.Context, parentNode *waBinary.Node) {
	int.c.tryHandleCodePairNotification(ctx, parentNode)
}

func (int *DangerousInternalClient) HandleCodePairNotification(ctx context.Context, parentNode *waBinary.Node) error {
	return int.c.handleCodePairNotification(ctx, parentNode)
}

func (int *DangerousInternalClient) HandleIQ(node *waBinary.Node) {
	int.c.handleIQ(node)
}

func (int *DangerousInternalClient) HandlePairDevice(node *waBinary.Node) {
	int.c.handlePairDevice(node)
}

func (int *DangerousInternalClient) MakeQRData(ref string) string {
	return int.c.makeQRData(ref)
}

func (int *DangerousInternalClient) HandlePairSuccess(node *waBinary.Node) {
	int.c.handlePairSuccess(node)
}

func (int *DangerousInternalClient) HandlePair(ctx context.Context, deviceIdentityBytes []byte, reqID, businessName, platform string, jid, lid types.JID) error {
	return int.c.handlePair(ctx, deviceIdentityBytes, reqID, businessName, platform, jid, lid)
}

func (int *DangerousInternalClient) SendPairError(id string, code int, text string) {
	int.c.sendPairError(id, code, text)
}

func (int *DangerousInternalClient) GetServerPreKeyCount(ctx context.Context) (int, error) {
	return int.c.getServerPreKeyCount(ctx)
}

func (int *DangerousInternalClient) UploadPreKeys(ctx context.Context) {
	int.c.uploadPreKeys(ctx)
}

func (int *DangerousInternalClient) FetchPreKeys(ctx context.Context, users []types.JID) (map[types.JID]preKeyResp, error) {
	return int.c.fetchPreKeys(ctx, users)
}

func (int *DangerousInternalClient) HandleChatState(node *waBinary.Node) {
	int.c.handleChatState(node)
}

func (int *DangerousInternalClient) HandlePresence(node *waBinary.Node) {
	int.c.handlePresence(node)
}

func (int *DangerousInternalClient) ParsePrivacySettings(privacyNode *waBinary.Node, settings *types.PrivacySettings) *events.PrivacySettings {
	return int.c.parsePrivacySettings(privacyNode, settings)
}

func (int *DangerousInternalClient) HandlePrivacySettingsNotification(ctx context.Context, privacyNode *waBinary.Node) {
	int.c.handlePrivacySettingsNotification(ctx, privacyNode)
}

func (int *DangerousInternalClient) HandleReceipt(node *waBinary.Node) {
	int.c.handleReceipt(node)
}

func (int *DangerousInternalClient) HandleGroupedReceipt(partialReceipt events.Receipt, participants *waBinary.Node) {
	int.c.handleGroupedReceipt(partialReceipt, participants)
}

func (int *DangerousInternalClient) ParseReceipt(node *waBinary.Node) (*events.Receipt, error) {
	return int.c.parseReceipt(node)
}

func (int *DangerousInternalClient) MaybeDeferredAck(ctx context.Context, node *waBinary.Node) func(...*bool) {
	return int.c.maybeDeferredAck(ctx, node)
}

func (int *DangerousInternalClient) SendAck(node *waBinary.Node) {
	int.c.sendAck(node)
}

func (int *DangerousInternalClient) SendMessageReceipt(info *types.MessageInfo) {
	int.c.sendMessageReceipt(info)
}

func (int *DangerousInternalClient) GenerateRequestID() string {
	return int.c.generateRequestID()
}

func (int *DangerousInternalClient) ClearResponseWaiters(node *waBinary.Node) {
	int.c.clearResponseWaiters(node)
}

func (int *DangerousInternalClient) WaitResponse(reqID string) chan *waBinary.Node {
	return int.c.waitResponse(reqID)
}

func (int *DangerousInternalClient) CancelResponse(reqID string, ch chan *waBinary.Node) {
	int.c.cancelResponse(reqID, ch)
}

func (int *DangerousInternalClient) ReceiveResponse(data *waBinary.Node) bool {
	return int.c.receiveResponse(data)
}

func (int *DangerousInternalClient) SendIQAsyncAndGetData(query *infoQuery) (<-chan *waBinary.Node, []byte, error) {
	return int.c.sendIQAsyncAndGetData(query)
}

func (int *DangerousInternalClient) SendIQAsync(query infoQuery) (<-chan *waBinary.Node, error) {
	return int.c.sendIQAsync(query)
}

func (int *DangerousInternalClient) SendIQ(query infoQuery) (*waBinary.Node, error) {
	return int.c.sendIQ(query)
}

func (int *DangerousInternalClient) RetryFrame(reqType, id string, data []byte, origResp *waBinary.Node, ctx context.Context, timeout time.Duration) (*waBinary.Node, error) {
	return int.c.retryFrame(reqType, id, data, origResp, ctx, timeout)
}

func (int *DangerousInternalClient) AddRecentMessage(to types.JID, id types.MessageID, wa *waE2E.Message, fb *waMsgApplication.MessageApplication) {
	int.c.addRecentMessage(to, id, wa, fb)
}

func (int *DangerousInternalClient) GetRecentMessage(to types.JID, id types.MessageID) RecentMessage {
	return int.c.getRecentMessage(to, id)
}

func (int *DangerousInternalClient) GetMessageForRetry(ctx context.Context, receipt *events.Receipt, messageID types.MessageID) (RecentMessage, error) {
	return int.c.getMessageForRetry(ctx, receipt, messageID)
}

func (int *DangerousInternalClient) ShouldRecreateSession(ctx context.Context, retryCount int, jid types.JID) (reason string, recreate bool) {
	return int.c.shouldRecreateSession(ctx, retryCount, jid)
}

func (int *DangerousInternalClient) HandleRetryReceipt(ctx context.Context, receipt *events.Receipt, node *waBinary.Node) error {
	return int.c.handleRetryReceipt(ctx, receipt, node)
}

func (int *DangerousInternalClient) CancelDelayedRequestFromPhone(msgID types.MessageID) {
	int.c.cancelDelayedRequestFromPhone(msgID)
}

func (int *DangerousInternalClient) DelayedRequestMessageFromPhone(info *types.MessageInfo) {
	int.c.delayedRequestMessageFromPhone(info)
}

func (int *DangerousInternalClient) ImmediateRequestMessageFromPhone(ctx context.Context, info *types.MessageInfo) {
	int.c.immediateRequestMessageFromPhone(ctx, info)
}

func (int *DangerousInternalClient) ClearDelayedMessageRequests() {
	int.c.clearDelayedMessageRequests()
}

func (int *DangerousInternalClient) SendRetryReceipt(ctx context.Context, node *waBinary.Node, info *types.MessageInfo, forceIncludeIdentity bool) {
	int.c.sendRetryReceipt(ctx, node, info, forceIncludeIdentity)
}

func (int *DangerousInternalClient) SendGroupV3(ctx context.Context, to, ownID types.JID, id types.MessageID, messageApp []byte, msgAttrs messageAttrs, frankingTag []byte, timings *MessageDebugTimings) (string, []byte, error) {
	return int.c.sendGroupV3(ctx, to, ownID, id, messageApp, msgAttrs, frankingTag, timings)
}

func (int *DangerousInternalClient) SendDMV3(ctx context.Context, to, ownID types.JID, id types.MessageID, messageApp []byte, msgAttrs messageAttrs, frankingTag []byte, timings *MessageDebugTimings) ([]byte, string, error) {
	return int.c.sendDMV3(ctx, to, ownID, id, messageApp, msgAttrs, frankingTag, timings)
}

func (int *DangerousInternalClient) PrepareMessageNodeV3(ctx context.Context, to, ownID types.JID, id types.MessageID, payload *waMsgTransport.MessageTransport_Payload, skdm *waMsgTransport.MessageTransport_Protocol_Ancillary_SenderKeyDistributionMessage, msgAttrs messageAttrs, frankingTag []byte, participants []types.JID, timings *MessageDebugTimings) (*waBinary.Node, []types.JID, error) {
	return int.c.prepareMessageNodeV3(ctx, to, ownID, id, payload, skdm, msgAttrs, frankingTag, participants, timings)
}

func (int *DangerousInternalClient) EncryptMessageForDevicesV3(ctx context.Context, allDevices []types.JID, ownID types.JID, id string, payload *waMsgTransport.MessageTransport_Payload, skdm *waMsgTransport.MessageTransport_Protocol_Ancillary_SenderKeyDistributionMessage, dsm *waMsgTransport.MessageTransport_Protocol_Integral_DeviceSentMessage, encAttrs waBinary.Attrs) []waBinary.Node {
	return int.c.encryptMessageForDevicesV3(ctx, allDevices, ownID, id, payload, skdm, dsm, encAttrs)
}

func (int *DangerousInternalClient) EncryptMessageForDeviceAndWrapV3(ctx context.Context, payload *waMsgTransport.MessageTransport_Payload, skdm *waMsgTransport.MessageTransport_Protocol_Ancillary_SenderKeyDistributionMessage, dsm *waMsgTransport.MessageTransport_Protocol_Integral_DeviceSentMessage, to types.JID, bundle *prekey.Bundle, encAttrs waBinary.Attrs) (*waBinary.Node, error) {
	return int.c.encryptMessageForDeviceAndWrapV3(ctx, payload, skdm, dsm, to, bundle, encAttrs)
}

func (int *DangerousInternalClient) EncryptMessageForDeviceV3(ctx context.Context, payload *waMsgTransport.MessageTransport_Payload, skdm *waMsgTransport.MessageTransport_Protocol_Ancillary_SenderKeyDistributionMessage, dsm *waMsgTransport.MessageTransport_Protocol_Integral_DeviceSentMessage, to types.JID, bundle *prekey.Bundle, extraAttrs waBinary.Attrs) (*waBinary.Node, error) {
	return int.c.encryptMessageForDeviceV3(ctx, payload, skdm, dsm, to, bundle, extraAttrs)
}

func (int *DangerousInternalClient) SendNewsletter(to types.JID, id types.MessageID, message *waE2E.Message, mediaID string, timings *MessageDebugTimings) ([]byte, error) {
	return int.c.sendNewsletter(to, id, message, mediaID, timings)
}

func (int *DangerousInternalClient) SendGroup(ctx context.Context, ownID, to types.JID, participants []types.JID, id types.MessageID, message *waE2E.Message, timings *MessageDebugTimings, extraParams nodeExtraParams) (string, []byte, error) {
	return int.c.sendGroup(ctx, ownID, to, participants, id, message, timings, extraParams)
}

func (int *DangerousInternalClient) SendPeerMessage(ctx context.Context, to types.JID, id types.MessageID, message *waE2E.Message, timings *MessageDebugTimings) ([]byte, error) {
	return int.c.sendPeerMessage(ctx, to, id, message, timings)
}

func (int *DangerousInternalClient) SendDM(ctx context.Context, ownID, to types.JID, id types.MessageID, message *waE2E.Message, timings *MessageDebugTimings, extraParams nodeExtraParams) ([]byte, error) {
	return int.c.sendDM(ctx, ownID, to, id, message, timings, extraParams)
}

func (int *DangerousInternalClient) PreparePeerMessageNode(ctx context.Context, to types.JID, id types.MessageID, message *waE2E.Message, timings *MessageDebugTimings) (*waBinary.Node, error) {
	return int.c.preparePeerMessageNode(ctx, to, id, message, timings)
}

func (int *DangerousInternalClient) GetMessageContent(baseNode waBinary.Node, message *waE2E.Message, msgAttrs waBinary.Attrs, includeIdentity bool, extraParams nodeExtraParams) []waBinary.Node {
	return int.c.getMessageContent(baseNode, message, msgAttrs, includeIdentity, extraParams)
}

func (int *DangerousInternalClient) PrepareMessageNode(ctx context.Context, to types.JID, id types.MessageID, message *waE2E.Message, participants []types.JID, plaintext, dsmPlaintext []byte, timings *MessageDebugTimings, extraParams nodeExtraParams) (*waBinary.Node, []types.JID, error) {
	return int.c.prepareMessageNode(ctx, to, id, message, participants, plaintext, dsmPlaintext, timings, extraParams)
}

func (int *DangerousInternalClient) MakeDeviceIdentityNode() waBinary.Node {
	return int.c.makeDeviceIdentityNode()
}

func (int *DangerousInternalClient) EncryptMessageForDevices(ctx context.Context, allDevices []types.JID, id string, msgPlaintext, dsmPlaintext []byte, encAttrs waBinary.Attrs) ([]waBinary.Node, bool) {
	return int.c.encryptMessageForDevices(ctx, allDevices, id, msgPlaintext, dsmPlaintext, encAttrs)
}

func (int *DangerousInternalClient) EncryptMessageForDeviceAndWrap(ctx context.Context, plaintext []byte, wireIdentity, encryptionIdentity types.JID, bundle *prekey.Bundle, encAttrs waBinary.Attrs) (*waBinary.Node, bool, error) {
	return int.c.encryptMessageForDeviceAndWrap(ctx, plaintext, wireIdentity, encryptionIdentity, bundle, encAttrs)
}

func (int *DangerousInternalClient) EncryptMessageForDevice(ctx context.Context, plaintext []byte, to types.JID, bundle *prekey.Bundle, extraAttrs waBinary.Attrs) (*waBinary.Node, bool, error) {
	return int.c.encryptMessageForDevice(ctx, plaintext, to, bundle, extraAttrs)
}

func (int *DangerousInternalClient) RawUpload(ctx context.Context, dataToUpload io.Reader, uploadSize uint64, fileHash []byte, appInfo MediaType, newsletter bool, resp *UploadResponse) error {
	return int.c.rawUpload(ctx, dataToUpload, uploadSize, fileHash, appInfo, newsletter, resp)
}

func (int *DangerousInternalClient) ParseBusinessProfile(node *waBinary.Node) (*types.BusinessProfile, error) {
	return int.c.parseBusinessProfile(node)
}

func (int *DangerousInternalClient) HandleHistoricalPushNames(ctx context.Context, names []*waHistorySync.Pushname) {
	int.c.handleHistoricalPushNames(ctx, names)
}

func (int *DangerousInternalClient) UpdatePushName(ctx context.Context, user types.JID, messageInfo *types.MessageInfo, name string) {
	int.c.updatePushName(ctx, user, messageInfo, name)
}

func (int *DangerousInternalClient) UpdateBusinessName(ctx context.Context, user types.JID, messageInfo *types.MessageInfo, name string) {
	int.c.updateBusinessName(ctx, user, messageInfo, name)
}

func (int *DangerousInternalClient) GetFBIDDevicesInternal(ctx context.Context, jids []types.JID) (*waBinary.Node, error) {
	return int.c.getFBIDDevicesInternal(ctx, jids)
}

func (int *DangerousInternalClient) GetFBIDDevices(ctx context.Context, jids []types.JID) ([]types.JID, error) {
	return int.c.getFBIDDevices(ctx, jids)
}

func (int *DangerousInternalClient) Usync(ctx context.Context, jids []types.JID, mode, context string, query []waBinary.Node, extra ...UsyncQueryExtras) (*waBinary.Node, error) {
	return int.c.usync(ctx, jids, mode, context, query, extra...)
}

func (int *DangerousInternalClient) ParseBlocklist(node *waBinary.Node) *types.Blocklist {
	return int.c.parseBlocklist(node)
}

func (int *DangerousInternalClient) ShouldIncludeReportingToken(message *waE2E.Message) bool {
	return int.c.shouldIncludeReportingToken(message)
}

func (int *DangerousInternalClient) GetMessageReportingToken(msgProtobuf []byte, msg *waE2E.Message, senderJID, remoteJID types.JID, messageID types.MessageID) waBinary.Node {
	return int.c.getMessageReportingToken(msgProtobuf, msg, senderJID, remoteJID, messageID)
}
