//! Message-related types

use crate::jid::Jid;
use serde::{Deserialize, Serialize};
use std::fmt;
use std::time::SystemTime;

/// Message ID type
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, <PERSON>h, Serialize, Deserialize)]
pub struct MessageId(pub String);

impl MessageId {
    /// Create a new MessageId
    pub fn new(id: String) -> Self {
        Self(id)
    }

    /// Get the message ID as a string slice
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Generate a random message ID
    pub fn generate() -> Self {
        use std::time::{SystemTime, UNIX_EPOCH};
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis();
        let random_part: u32 = rand::random();
        Self(format!("{:X}{:08X}", timestamp, random_part))
    }

    /// Check if the message ID is empty
    pub fn is_empty(&self) -> bool {
        self.0.is_empty()
    }

    /// Get the length of the message ID
    pub fn len(&self) -> usize {
        self.0.len()
    }
}

impl fmt::Display for MessageId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl From<String> for MessageId {
    fn from(id: String) -> Self {
        Self(id)
    }
}

impl From<&str> for MessageId {
    fn from(id: &str) -> Self {
        Self(id.to_string())
    }
}

impl AsRef<str> for MessageId {
    fn as_ref(&self) -> &str {
        &self.0
    }
}

/// Message type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Default)]
pub enum MessageType {
    Text,
    Image,
    Video,
    Audio,
    Document,
    Sticker,
    Location,
    Contact,
    Reaction,
    Poll,
    System,
    #[default]
    Unknown,
}

/// Message status enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Default)]
pub enum MessageStatus {
    #[default]
    Pending,
    Sent,
    Delivered,
    Read,
    Failed,
    Cancelled,
}

/// Message information metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageInfo {
    pub id: MessageId,
    pub timestamp: SystemTime,
    pub chat: Jid,
    pub sender: Option<Jid>,
    pub from_me: bool,
    pub broadcast_list_owner: Option<Jid>,
    pub message_type: MessageType,
    pub status: MessageStatus,
    pub quoted_message_id: Option<MessageId>,
    pub forwarded: bool,
    pub ephemeral_duration: Option<u32>,
    pub edit_count: u32,
    pub media_key: Option<Vec<u8>>,
    pub push_name: Option<String>,
    pub multicast: bool,
    pub url_text: bool,
    pub url_number: bool,
    pub message_stub_type: Option<u32>,
    pub clear_media: bool,
    pub message_c2s_timestamp: Option<SystemTime>,
    pub ignore: bool,
    pub starred: bool,
    pub broadcast: bool,
    pub media_ciphertext: Option<Vec<u8>>,
}

impl MessageInfo {
    /// Create a new MessageInfo with required fields
    pub fn new(
        id: MessageId,
        timestamp: SystemTime,
        chat: Jid,
        sender: Option<Jid>,
        from_me: bool,
    ) -> Self {
        Self {
            id,
            timestamp,
            chat,
            sender,
            from_me,
            broadcast_list_owner: None,
            message_type: MessageType::default(),
            status: MessageStatus::default(),
            quoted_message_id: None,
            forwarded: false,
            ephemeral_duration: None,
            edit_count: 0,
            media_key: None,
            push_name: None,
            multicast: false,
            url_text: false,
            url_number: false,
            message_stub_type: None,
            clear_media: false,
            message_c2s_timestamp: None,
            ignore: false,
            starred: false,
            broadcast: false,
            media_ciphertext: None,
        }
    }

    /// Create a builder for MessageInfo
    pub fn builder(id: MessageId, timestamp: SystemTime, chat: Jid) -> MessageInfoBuilder {
        MessageInfoBuilder::new(id, timestamp, chat)
    }

    /// Check if this is a group message
    pub fn is_group_message(&self) -> bool {
        self.chat.is_group()
    }

    /// Check if this is a broadcast message
    pub fn is_broadcast_message(&self) -> bool {
        self.broadcast_list_owner.is_some() || self.broadcast
    }

    /// Check if this message is ephemeral (disappearing)
    pub fn is_ephemeral(&self) -> bool {
        self.ephemeral_duration.is_some()
    }

    /// Check if this message has been edited
    pub fn is_edited(&self) -> bool {
        self.edit_count > 0
    }

    /// Check if this message is a reply to another message
    pub fn is_reply(&self) -> bool {
        self.quoted_message_id.is_some()
    }

    /// Get the sender JID, falling back to chat JID if sender is None
    pub fn get_sender(&self) -> &Jid {
        self.sender.as_ref().unwrap_or(&self.chat)
    }

    /// Get the display name for the sender
    pub fn get_sender_name(&self) -> Option<&str> {
        self.push_name.as_deref()
    }
}

/// Builder pattern for MessageInfo
#[derive(Debug)]
pub struct MessageInfoBuilder {
    info: MessageInfo,
}

impl MessageInfoBuilder {
    fn new(id: MessageId, timestamp: SystemTime, chat: Jid) -> Self {
        Self {
            info: MessageInfo {
                id,
                timestamp,
                chat,
                sender: None,
                from_me: false,
                broadcast_list_owner: None,
                message_type: MessageType::default(),
                status: MessageStatus::default(),
                quoted_message_id: None,
                forwarded: false,
                ephemeral_duration: None,
                edit_count: 0,
                media_key: None,
                push_name: None,
                multicast: false,
                url_text: false,
                url_number: false,
                message_stub_type: None,
                clear_media: false,
                message_c2s_timestamp: None,
                ignore: false,
                starred: false,
                broadcast: false,
                media_ciphertext: None,
            },
        }
    }

    pub fn sender(mut self, sender: Jid) -> Self {
        self.info.sender = Some(sender);
        self
    }

    pub fn from_me(mut self, from_me: bool) -> Self {
        self.info.from_me = from_me;
        self
    }

    pub fn message_type(mut self, message_type: MessageType) -> Self {
        self.info.message_type = message_type;
        self
    }

    pub fn status(mut self, status: MessageStatus) -> Self {
        self.info.status = status;
        self
    }

    pub fn quoted_message_id(mut self, quoted_id: MessageId) -> Self {
        self.info.quoted_message_id = Some(quoted_id);
        self
    }

    pub fn forwarded(mut self, forwarded: bool) -> Self {
        self.info.forwarded = forwarded;
        self
    }

    pub fn ephemeral_duration(mut self, duration: u32) -> Self {
        self.info.ephemeral_duration = Some(duration);
        self
    }

    pub fn push_name(mut self, name: String) -> Self {
        self.info.push_name = Some(name);
        self
    }

    pub fn starred(mut self, starred: bool) -> Self {
        self.info.starred = starred;
        self
    }

    pub fn broadcast_list_owner(mut self, owner: Jid) -> Self {
        self.info.broadcast_list_owner = Some(owner);
        self
    }

    pub fn build(self) -> MessageInfo {
        self.info
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::jid::Jid;
    use std::time::{SystemTime, UNIX_EPOCH};

    #[test]
    fn test_message_id_creation() {
        let id = MessageId::new("test123".to_string());
        assert_eq!(id.as_str(), "test123");
        assert_eq!(id.to_string(), "test123");
        assert!(!id.is_empty());
        assert_eq!(id.len(), 7);
    }

    #[test]
    fn test_message_id_from_string() {
        let id: MessageId = "test456".into();
        assert_eq!(id.as_str(), "test456");
    }

    #[test]
    fn test_message_id_from_str() {
        let id: MessageId = "test789".into();
        assert_eq!(id.as_str(), "test789");
    }

    #[test]
    fn test_message_id_display() {
        let id = MessageId::new("display_test".to_string());
        assert_eq!(format!("{}", id), "display_test");
    }

    #[test]
    fn test_message_id_as_ref() {
        let id = MessageId::new("ref_test".to_string());
        let s: &str = id.as_ref();
        assert_eq!(s, "ref_test");
    }

    #[test]
    fn test_message_id_empty() {
        let empty_id = MessageId::new("".to_string());
        assert!(empty_id.is_empty());
        assert_eq!(empty_id.len(), 0);

        let non_empty_id = MessageId::new("test".to_string());
        assert!(!non_empty_id.is_empty());
        assert_eq!(non_empty_id.len(), 4);
    }

    #[test]
    fn test_message_id_generate() {
        let id1 = MessageId::generate();
        let id2 = MessageId::generate();

        // Generated IDs should not be empty
        assert!(!id1.is_empty());
        assert!(!id2.is_empty());

        // Generated IDs should be different
        assert_ne!(id1, id2);

        // Generated IDs should be reasonable length (timestamp + random)
        assert!(id1.len() > 8);
        assert!(id2.len() > 8);
    }

    #[test]
    fn test_message_id_serialization() {
        let id = MessageId::new("serialize_test".to_string());
        let serialized = serde_json::to_string(&id).unwrap();
        let deserialized: MessageId = serde_json::from_str(&serialized).unwrap();
        assert_eq!(id, deserialized);
    }

    #[test]
    fn test_message_type_default() {
        let msg_type = MessageType::default();
        assert_eq!(msg_type, MessageType::Unknown);
    }

    #[test]
    fn test_message_status_default() {
        let status = MessageStatus::default();
        assert_eq!(status, MessageStatus::Pending);
    }

    #[test]
    fn test_message_info_creation() {
        let id = MessageId::new("msg123".to_string());
        let timestamp = SystemTime::now();
        let chat = Jid::new_unchecked("1234567890".to_string(), "s.whatsapp.net".to_string(), 0);
        let sender = Jid::new_unchecked("0987654321".to_string(), "s.whatsapp.net".to_string(), 0);

        let info = MessageInfo::new(
            id.clone(),
            timestamp,
            chat.clone(),
            Some(sender.clone()),
            false,
        );

        assert_eq!(info.id, id);
        assert_eq!(info.timestamp, timestamp);
        assert_eq!(info.chat, chat);
        assert_eq!(info.sender, Some(sender));
        assert!(!info.from_me);
        assert_eq!(info.message_type, MessageType::Unknown);
        assert_eq!(info.status, MessageStatus::Pending);
        assert!(!info.forwarded);
        assert_eq!(info.edit_count, 0);
        assert!(!info.starred);
    }

    #[test]
    fn test_message_info_builder() {
        let id = MessageId::new("builder_test".to_string());
        let timestamp = SystemTime::now();
        let chat = Jid::new_unchecked("group123".to_string(), "g.us".to_string(), 0);
        let sender = Jid::new_unchecked("user456".to_string(), "s.whatsapp.net".to_string(), 0);
        let quoted_id = MessageId::new("quoted123".to_string());

        let info = MessageInfo::builder(id.clone(), timestamp, chat.clone())
            .sender(sender.clone())
            .from_me(true)
            .message_type(MessageType::Text)
            .status(MessageStatus::Sent)
            .quoted_message_id(quoted_id.clone())
            .forwarded(true)
            .ephemeral_duration(3600)
            .push_name("Test User".to_string())
            .starred(true)
            .build();

        assert_eq!(info.id, id);
        assert_eq!(info.sender, Some(sender));
        assert!(info.from_me);
        assert_eq!(info.message_type, MessageType::Text);
        assert_eq!(info.status, MessageStatus::Sent);
        assert_eq!(info.quoted_message_id, Some(quoted_id));
        assert!(info.forwarded);
        assert_eq!(info.ephemeral_duration, Some(3600));
        assert_eq!(info.push_name, Some("Test User".to_string()));
        assert!(info.starred);
    }

    #[test]
    fn test_message_info_is_group_message() {
        let id = MessageId::new("group_test".to_string());
        let timestamp = SystemTime::now();

        let group_chat = Jid::new_unchecked("group123".to_string(), "g.us".to_string(), 0);
        let group_info = MessageInfo::new(id.clone(), timestamp, group_chat, None, false);
        assert!(group_info.is_group_message());

        let user_chat = Jid::new_unchecked("user123".to_string(), "s.whatsapp.net".to_string(), 0);
        let user_info = MessageInfo::new(id, timestamp, user_chat, None, false);
        assert!(!user_info.is_group_message());
    }

    #[test]
    fn test_message_info_is_broadcast_message() {
        let id = MessageId::new("broadcast_test".to_string());
        let timestamp = SystemTime::now();
        let chat = Jid::new_unchecked("user123".to_string(), "s.whatsapp.net".to_string(), 0);
        let owner = Jid::new_unchecked("owner123".to_string(), "s.whatsapp.net".to_string(), 0);

        let broadcast_info = MessageInfo::builder(id.clone(), timestamp, chat.clone())
            .broadcast_list_owner(owner)
            .build();
        assert!(broadcast_info.is_broadcast_message());

        let regular_info = MessageInfo::new(id, timestamp, chat, None, false);
        assert!(!regular_info.is_broadcast_message());
    }

    #[test]
    fn test_message_info_is_ephemeral() {
        let id = MessageId::new("ephemeral_test".to_string());
        let timestamp = SystemTime::now();
        let chat = Jid::new_unchecked("user123".to_string(), "s.whatsapp.net".to_string(), 0);

        let ephemeral_info = MessageInfo::builder(id.clone(), timestamp, chat.clone())
            .ephemeral_duration(3600)
            .build();
        assert!(ephemeral_info.is_ephemeral());

        let regular_info = MessageInfo::new(id, timestamp, chat, None, false);
        assert!(!regular_info.is_ephemeral());
    }

    #[test]
    fn test_message_info_is_edited() {
        let id = MessageId::new("edit_test".to_string());
        let timestamp = SystemTime::now();
        let chat = Jid::new_unchecked("user123".to_string(), "s.whatsapp.net".to_string(), 0);

        let mut edited_info = MessageInfo::new(id.clone(), timestamp, chat.clone(), None, false);
        edited_info.edit_count = 2;
        assert!(edited_info.is_edited());

        let regular_info = MessageInfo::new(id, timestamp, chat, None, false);
        assert!(!regular_info.is_edited());
    }

    #[test]
    fn test_message_info_is_reply() {
        let id = MessageId::new("reply_test".to_string());
        let timestamp = SystemTime::now();
        let chat = Jid::new_unchecked("user123".to_string(), "s.whatsapp.net".to_string(), 0);
        let quoted_id = MessageId::new("quoted123".to_string());

        let reply_info = MessageInfo::builder(id.clone(), timestamp, chat.clone())
            .quoted_message_id(quoted_id)
            .build();
        assert!(reply_info.is_reply());

        let regular_info = MessageInfo::new(id, timestamp, chat, None, false);
        assert!(!regular_info.is_reply());
    }

    #[test]
    fn test_message_info_get_sender() {
        let id = MessageId::new("sender_test".to_string());
        let timestamp = SystemTime::now();
        let chat = Jid::new_unchecked("chat123".to_string(), "s.whatsapp.net".to_string(), 0);
        let sender = Jid::new_unchecked("sender123".to_string(), "s.whatsapp.net".to_string(), 0);

        // With explicit sender
        let info_with_sender = MessageInfo::new(
            id.clone(),
            timestamp,
            chat.clone(),
            Some(sender.clone()),
            false,
        );
        assert_eq!(info_with_sender.get_sender(), &sender);

        // Without explicit sender (should fall back to chat)
        let info_without_sender = MessageInfo::new(id, timestamp, chat.clone(), None, false);
        assert_eq!(info_without_sender.get_sender(), &chat);
    }

    #[test]
    fn test_message_info_get_sender_name() {
        let id = MessageId::new("name_test".to_string());
        let timestamp = SystemTime::now();
        let chat = Jid::new_unchecked("user123".to_string(), "s.whatsapp.net".to_string(), 0);

        let info_with_name = MessageInfo::builder(id.clone(), timestamp, chat.clone())
            .push_name("John Doe".to_string())
            .build();
        assert_eq!(info_with_name.get_sender_name(), Some("John Doe"));

        let info_without_name = MessageInfo::new(id, timestamp, chat, None, false);
        assert_eq!(info_without_name.get_sender_name(), None);
    }

    #[test]
    fn test_message_info_serialization() {
        let id = MessageId::new("serialize_test".to_string());
        let timestamp = UNIX_EPOCH;
        let chat = Jid::new_unchecked("user123".to_string(), "s.whatsapp.net".to_string(), 0);
        let sender = Jid::new_unchecked("sender123".to_string(), "s.whatsapp.net".to_string(), 0);

        let info = MessageInfo::builder(id, timestamp, chat)
            .sender(sender)
            .from_me(true)
            .message_type(MessageType::Text)
            .status(MessageStatus::Delivered)
            .build();

        let serialized = serde_json::to_string(&info).unwrap();
        let deserialized: MessageInfo = serde_json::from_str(&serialized).unwrap();

        assert_eq!(info.id, deserialized.id);
        assert_eq!(info.from_me, deserialized.from_me);
        assert_eq!(info.message_type, deserialized.message_type);
        assert_eq!(info.status, deserialized.status);
    }

    #[test]
    fn test_message_type_serialization() {
        let msg_types = vec![
            MessageType::Text,
            MessageType::Image,
            MessageType::Video,
            MessageType::Audio,
            MessageType::Document,
            MessageType::Sticker,
            MessageType::Location,
            MessageType::Contact,
            MessageType::Reaction,
            MessageType::Poll,
            MessageType::System,
            MessageType::Unknown,
        ];

        for msg_type in msg_types {
            let serialized = serde_json::to_string(&msg_type).unwrap();
            let deserialized: MessageType = serde_json::from_str(&serialized).unwrap();
            assert_eq!(msg_type, deserialized);
        }
    }

    #[test]
    fn test_message_status_serialization() {
        let statuses = vec![
            MessageStatus::Pending,
            MessageStatus::Sent,
            MessageStatus::Delivered,
            MessageStatus::Read,
            MessageStatus::Failed,
            MessageStatus::Cancelled,
        ];

        for status in statuses {
            let serialized = serde_json::to_string(&status).unwrap();
            let deserialized: MessageStatus = serde_json::from_str(&serialized).unwrap();
            assert_eq!(status, deserialized);
        }
    }
}
